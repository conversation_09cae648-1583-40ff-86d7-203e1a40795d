# RViz中的多点导航循环执行指南

## 概述
Nav2的RViz插件已经内置了完整的waypoint_follower功能，你可以直接在RViz中通过点击操作实现多点导航循环执行。

## 使用步骤

### 1. 启动导航系统
```bash
ros2 launch robotcar_nav nav_05_navigation.launch.py
```

### 2. 在RViz中设置初始位置
- 使用 "2D Pose Estimate" 工具设置机器人的初始位置
- 确保机器人在地图上正确定位

### 3. 切换到Waypoint模式
在RViz右侧的 "Navigation 2" 面板中：
- 点击 "Waypoint / Nav Through Poses Mode" 按钮
- 面板会切换到waypoint累积模式

### 4. 添加路径点
- 使用 "Nav2 Goal" 工具（绿色箭头图标）在地图上点击多个位置
- 每次点击都会添加一个waypoint
- 你会看到红色圆圈和绿色箭头标记每个waypoint
- waypoint会按照点击顺序编号

### 5. 开始waypoint导航
在 "Navigation 2" 面板中有两个选项：

#### 选项A: Waypoint Following（循环执行）
- 点击 "Start Waypoint Following" 按钮
- 机器人会按顺序访问所有waypoint
- **到达最后一个waypoint后会自动循环回到第一个waypoint**
- 这就是你需要的循环执行功能

#### 选项B: Nav Through Poses（一次性执行）
- 点击 "Start Nav Through Poses" 按钮  
- 机器人会按顺序访问所有waypoint
- 到达最后一个waypoint后停止（不循环）

### 6. 控制导航
- **暂停**: 点击 "Pause" 按钮暂停导航
- **恢复**: 点击 "Resume" 按钮恢复导航
- **取消**: 点击 "Cancel" 按钮取消当前导航
- **清除waypoints**: 点击 "Cancel Accumulation" 清除所有已设置的waypoint

## 配置说明

### waypoint_follower配置
在 `nav2_simple.yaml` 中的相关配置：
```yaml
waypoint_follower:
  ros__parameters:
    use_sim_time: false
    loop_rate: 20
    stop_on_failure: false
    waypoint_task_executor_plugin: "wait_at_waypoint"
    wait_at_waypoint:
      plugin: "nav2_waypoint_follower::WaitAtWaypoint"
      enabled: True
      waypoint_pause_duration: 2.0  # 在每个路径点停留2秒
```

### 关键参数说明
- `loop_rate`: waypoint_follower的运行频率
- `stop_on_failure`: 失败时是否停止（false表示继续执行）
- `waypoint_pause_duration`: 到达每个waypoint后的停留时间（秒）

## 循环执行的实现
- **Waypoint Following模式**会自动实现循环执行
- 机器人到达最后一个waypoint后，会自动返回第一个waypoint
- 这个循环会一直持续，直到你手动取消导航

## 故障排除

### 如果waypoint不显示
1. 检查RViz中的 "Waypoints" 显示是否启用
2. 确保topic设置为 `/waypoints`

### 如果导航失败
1. 检查机器人是否正确定位
2. 确保waypoint设置在可到达的位置
3. 检查costmap是否正确显示障碍物

### 如果循环不工作
1. 确保使用的是 "Start Waypoint Following" 而不是 "Start Nav Through Poses"
2. 检查waypoint_follower节点是否正常运行：
   ```bash
   ros2 node list | grep waypoint
   ```

## 高级功能
- 可以在导航过程中动态添加新的waypoint
- 支持在waypoint处执行自定义任务（通过waypoint_task_executor_plugin）
- 支持不同的waypoint执行策略

这样你就可以完全通过RViz界面实现多点导航循环执行，无需编写任何额外代码！
