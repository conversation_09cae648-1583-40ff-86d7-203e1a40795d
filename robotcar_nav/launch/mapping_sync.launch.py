import os
from ament_index_python.packages import get_package_share_directory
from launch import LaunchDescription
from launch.actions import DeclareLaunchArgument, IncludeLaunchDescription
from launch.substitutions import LaunchConfiguration, PathJoinSubstitution, PythonExpression
from launch_ros.actions import Node
from launch.conditions import IfCondition, UnlessCondition

def generate_launch_description():
    """
    Launch file for running Cartographer SLAM for mapping.
    It includes an option to use EKF for odometry, which is disabled by default.
    """
    pkg_robotcar_nav = get_package_share_directory('robotcar_nav')
    
    use_sim_time = LaunchConfiguration('use_sim_time')
    use_ekf = LaunchConfiguration('use_ekf')
    start_rviz = LaunchConfiguration('start_rviz')
    use_sensor_sync = LaunchConfiguration('use_sensor_sync')
    use_laser_filter = LaunchConfiguration('use_laser_filter')
    lidar_type = LaunchConfiguration('lidar_type')
    
    cartographer_config_dir = PathJoinSubstitution([pkg_robotcar_nav, 'config'])
    cartographer_config_basename = 'cartographer_mapping.lua'
    rviz_config_file = PathJoinSubstitution([pkg_robotcar_nav, 'config', 'mapping.rviz'])

    remappings = [
        (
            'scan',
            PythonExpression([
                "'/synced/scan' if ", use_sensor_sync, " else '/scan_filtered'"
            ])
        ),
        (
            'imu',
            PythonExpression([
                "'/synced/imu' if ", use_sensor_sync, " else '/imu'"
            ])
        ),
        (
            'odom',
            PythonExpression([
                "'/odom' if ", use_ekf, " else '/diff_drive_controller/odom'"
            ])
        )
    ]

    return LaunchDescription([
        DeclareLaunchArgument(
            'use_sim_time', default_value='false',
            description='Use simulation (Gazebo) clock if true'),
        # use_ekf: 控制是否启动robot_localization进行传感器融合。
        # - 'false' (默认): 依赖diff_drive_controller发布TF。适用于只有轮式里程计的场景。
        # - 'true': 启动EKF节点融合轮式里程计和IMU，提供更精确的位姿估计。
        #           使用此模式时，请确保diff_drive_controller的enable_odom_tf为false。
        DeclareLaunchArgument(
            'use_ekf', default_value='false',
            description='Set to "true" to use EKF for odometry fusion.'),
        DeclareLaunchArgument(
            'start_rviz', default_value='true',
            description='Whether to start RViz'),
        DeclareLaunchArgument(
            'use_sensor_sync', default_value='false',
            description='Whether to use message_filters for sensor synchronization'),
        DeclareLaunchArgument(
            'use_laser_filter', default_value='true',
            description='Whether to use laser filter to remove outliers'),
        DeclareLaunchArgument(
            'lidar_type', default_value='oradar',
            description="Type of lidar to use: 'vanjee' or 'oradar'"),

        # 1. Launch peripherals
        IncludeLaunchDescription(
            PythonLaunchDescriptionSource(
                os.path.join(pkg_robotcar_nav, 'launch', 'nav_01_hardware.launch.py')),
            launch_arguments={
                'use_sim_time': use_sim_time,
                'use_sensor_sync': use_sensor_sync,
                'use_laser_filter': use_laser_filter,
                'lidar_type': lidar_type
            }.items()),
        
        # 2. Launch EKF-based odometry IF use_ekf is true
        IncludeLaunchDescription(
            PythonLaunchDescriptionSource(
                os.path.join(pkg_robotcar_nav, 'launch', 'nav_02_ekf.launch.py')),
            launch_arguments={'use_sim_time': use_sim_time}.items(),
            condition=IfCondition(use_ekf)),

        # 3. Remap odom topic if not using EKF (default)
        Node(
            package='cartographer_ros',
            executable='cartographer_node',
            name='cartographer_node',
            output='screen',
            parameters=[{'use_sim_time': use_sim_time}],
            arguments=['-configuration_directory', cartographer_config_dir,
                       '-configuration_basename', cartographer_config_basename],
            remappings=remappings
        ),

        Node(
            package='cartographer_ros',
            executable='cartographer_occupancy_grid_node',
            name='cartographer_occupancy_grid_node',
            output='screen',
            parameters=[{'use_sim_time': use_sim_time}],
            arguments=['-resolution', '0.05', '-publish_period_sec', '1.0']),

        # 5. Launch RViz for visualization
        Node(
            package='rviz2',
            executable='rviz2',
            name='rviz2',
            arguments=['-d', rviz_config_file],
            condition=IfCondition(start_rviz)),
    ]) 