import os
from ament_index_python.packages import get_package_share_directory
from launch import LaunchDescription
from launch.actions import DeclareLaunchArgument
from launch.substitutions import LaunchConfiguration
from launch_ros.actions import Node

def generate_launch_description():
    """
    Launch file for starting the sensor synchronization node.
    This node uses message_filters to synchronize data from different sensors.
    """
    use_sim_time = LaunchConfiguration('use_sim_time', default='false')
    sync_mode = LaunchConfiguration('sync_mode', default='approximate')
    sync_slop = LaunchConfiguration('sync_slop', default='0.1')
    use_filtered_scan = LaunchConfiguration('use_filtered_scan', default='true')

    return LaunchDescription([
        DeclareLaunchArgument(
            'use_sim_time',
            default_value='false',
            description='Use simulation (Gazebo) clock if true'
        ),
        DeclareLaunchArgument(
            'sync_mode',
            default_value='approximate',
            description='Synchronization mode: "exact" or "approximate"'
        ),
        DeclareLaunchArgument(
            'sync_slop',
            default_value='0.1',
            description='Time tolerance for approximate synchronization (in seconds)'
        ),
        DeclareLaunchArgument(
            'use_filtered_scan',
            default_value='true',
            description='Use filtered laser scan data instead of raw data'
        ),

        # Sensor synchronization node
        Node(
            package='robotcar_nav',
            executable='sensor_sync_node',
            name='sensor_sync_node',
            output='screen',
            parameters=[{
                'use_sim_time': use_sim_time,
                'sync_mode': sync_mode,
                'sync_slop': sync_slop,
                'use_filtered_scan': use_filtered_scan,
                'scan_topic': ['/scan_filtered', '/scan']  # 根据参数选择不同的话题
            }]
        ),
    ]) 