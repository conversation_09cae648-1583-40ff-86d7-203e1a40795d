import os
from ament_index_python.packages import get_package_share_directory
from launch import LaunchDescription
from launch.actions import DeclareLaunchArgument, IncludeLaunchDescription
from launch.substitutions import LaunchConfiguration, PythonExpression
from launch.conditions import IfCondition, UnlessCondition
from launch_ros.actions import Node
from launch.launch_description_sources import PythonLaunchDescriptionSource

def generate_launch_description():
    """
    Launch file for starting essential robot peripherals.
    - LiDAR driver (Vanjee or ORadar)
    - ROS2 Control for chassis communication (via bringup.launch.py)
    - IMU driver (via bringup.launch.py)
    - Robot URDF model
    - Static transforms
    - Laser filter (optional)
    """
    pkg_robotcar_base = get_package_share_directory('robotcar_base')
    pkg_oradar_ros = get_package_share_directory('oradar_ros')
    
    use_sim_time = LaunchConfiguration('use_sim_time', default='false')
    use_laser_filter = LaunchConfiguration('use_laser_filter', default='true')
    scan_topic = LaunchConfiguration('scan_topic', default='/scan')
    filtered_scan_topic = LaunchConfiguration('filtered_scan_topic', default='/scan_filtered')
    lidar_type = LaunchConfiguration('lidar_type', default='oradar')

    # 启动ROS2 Control和IMU（通过bringup.launch.py）
    robotcar_bringup_launch = IncludeLaunchDescription(
        PythonLaunchDescriptionSource(
            os.path.join(pkg_robotcar_base, 'launch', 'bringup.launch.py')
        ),
        launch_arguments={
            'use_sim_time': use_sim_time,
            'imu_port': '/dev/imu_usb',
            'imu_topic': 'imu'
        }.items()
    )
    
    # ORadar Lidar driver launch - 如果选择ORadar雷达
    oradar_lidar_launch = IncludeLaunchDescription(
         PythonLaunchDescriptionSource(
                os.path.join(pkg_oradar_ros, 'launch', 'ms500_scan.launch.py')
         ),
         condition=IfCondition(PythonExpression(["'", lidar_type, "' == 'oradar'"]))
    )
    
    # 激光雷达过滤器节点 - 仅在use_laser_filter为true时启动
    laser_filter_node = Node(
        package='robotcar_base',
        executable='laser_filter_node_exe',
        name='lidar_filter',
        output='screen',
        condition=IfCondition(use_laser_filter),
        parameters=[
            {'source_topic': scan_topic},
            {'pub_topic': filtered_scan_topic},
            {'outlier_threshold': 0.1}
        ]
    )
    
    # Declare use_sensor_sync argument
    use_sensor_sync_arg = DeclareLaunchArgument(
        'use_sensor_sync',
        default_value='false',
        description='Use message_filters for sensor synchronization'
    )

    # Declare use_laser_filter argument
    use_laser_filter_arg = DeclareLaunchArgument(
        'use_laser_filter',
        default_value='true',
        description='Use laser filter to remove outliers/ghost points'
    )
    
    # Declare scan topic arguments
    scan_topic_arg = DeclareLaunchArgument(
        'scan_topic',
        default_value='/scan',
        description='Input laser scan topic'
    )
    
    filtered_scan_topic_arg = DeclareLaunchArgument(
        'filtered_scan_topic',
        default_value='/scan_filtered',
        description='Filtered output laser scan topic'
    )
    
    # Declare lidar type argument
    lidar_type_arg = DeclareLaunchArgument(
        'lidar_type',
        default_value='oradar',
        description="Type of lidar to use: 'vanjee' or 'oradar'"
    )
    
    # Sensor synchronization node - only started if use_sensor_sync is true
    sensor_sync_node = Node(
        package='robotcar_nav',
        executable='sensor_sync_node',
        name='sensor_sync_node',
        output='screen',
        parameters=[{
            'use_sim_time': use_sim_time,
            'sync_mode': 'approximate', 
            'sync_slop': 0.1  # 传感器同步的误差 单位为秒
        }],
        condition=IfCondition(LaunchConfiguration('use_sensor_sync'))
    )

    return LaunchDescription([
        DeclareLaunchArgument(
            'use_sim_time',
            default_value='false',
            description='Use simulation (Gazebo) clock if true'
        ),
        use_sensor_sync_arg,
        use_laser_filter_arg,
        scan_topic_arg,
        filtered_scan_topic_arg,
        lidar_type_arg,
        
        robotcar_bringup_launch,  # 这会启动ROS2 Control, robot_state_publisher和IMU
        oradar_lidar_launch,
        laser_filter_node,
        sensor_sync_node,
    ]) 