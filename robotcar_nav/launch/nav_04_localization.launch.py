import os
from ament_index_python.packages import get_package_share_directory
from launch import LaunchDescription
from launch.actions import DeclareLaunchArgument, IncludeLaunchDescription, TimerAction
from launch.substitutions import LaunchConfiguration, PathJoinSubstitution
from launch_ros.actions import Node
from launch.conditions import IfCondition
from launch.launch_description_sources import PythonLaunchDescriptionSource

def generate_launch_description():
    """
    Launch file for running Cartographer in localization-only mode.
    Uses the adjusted nav_01, nav_02 launch files and 0624 map for navigation.
    """
    pkg_robotcar_nav = get_package_share_directory('robotcar_nav')

    use_sim_time = LaunchConfiguration('use_sim_time')
    use_ekf = LaunchConfiguration('use_ekf')
    start_rviz = LaunchConfiguration('start_rviz')
    map_file = LaunchConfiguration('map_file')

    cartographer_config_dir = PathJoinSubstitution([pkg_robotcar_nav, 'config'])
    cartographer_config_basename = 'cartographer_localization.lua'
    rviz_config_file = PathJoinSubstitution([pkg_robotcar_nav, 'config', 'nav2.rviz'])  # 使用建图的rviz配置

    # 1. Launch hardware only (without RViz from nav1)
    robotcar_bringup_launch = IncludeLaunchDescription(
        PythonLaunchDescriptionSource(
            os.path.join(get_package_share_directory('robotcar_base'), 'launch', 'bringup.launch.py')
        ),
        launch_arguments={
            'use_sim_time': use_sim_time,
            'start_rviz': 'true'
        }.items()
    )

    # Launch ORadar LiDAR only (without RViz)
    oradar_scan_launch = IncludeLaunchDescription(
        PythonLaunchDescriptionSource(
            os.path.join(get_package_share_directory('oradar_ros'), 'launch', 'ms500_scan.launch.py')
        ),
        launch_arguments={'use_sim_time': use_sim_time}.items()
    )

    # 2. Launch EKF (nav_02_ekf.launch.py) - 条件启动
    nav02_ekf_launch = IncludeLaunchDescription(
        PythonLaunchDescriptionSource(
            os.path.join(pkg_robotcar_nav, 'launch', 'nav_02_ekf.launch.py')
        ),
        launch_arguments={
            'use_sim_time': use_sim_time,
        }.items(),
        condition=IfCondition(use_ekf)
    )

    # 3. Cartographer定位节点 (延迟启动确保硬件就绪)
    cartographer_node = TimerAction(
        period=2.0,  # 等待2秒让硬件初始化
        actions=[
            Node(
                package='cartographer_ros',
                executable='cartographer_node',
                name='cartographer_node',
                output='screen',
                parameters=[{'use_sim_time': use_sim_time}],
                arguments=[
                    '-configuration_directory', cartographer_config_dir,
                    '-configuration_basename', cartographer_config_basename,
                    '-load_state_filename', map_file
                ],
                remappings=[
                    ('scan', '/scan'),
                    ('imu', '/imu'),
                    ('odom', '/odom'),  # 使用EKF融合后的里程计
                ]
            )
        ]
    )

    # 4. 占用栅格地图节点 (发布地图给Nav2使用)
    occupancy_grid_node = TimerAction(
        period=3.0,  # 等待3秒让Cartographer启动
        actions=[
            Node(
                package='cartographer_ros',
                executable='cartographer_occupancy_grid_node',
                name='cartographer_occupancy_grid_node',
                output='screen',
                parameters=[{'use_sim_time': use_sim_time}],
                arguments=['-resolution', '0.03', '-publish_period_sec', '1.0']
            )
        ]
    )

    # 5. RViz可视化 (延迟启动，使用建图的rviz配置)
    localization_rviz_node = TimerAction(
        period=3.0,  # 等待3秒让节点启动
        actions=[
            Node(
                package='rviz2',
                executable='rviz2',
                name='rviz2_localization',
                arguments=['-d', rviz_config_file],
                parameters=[{'use_sim_time': use_sim_time}],
                condition=IfCondition(start_rviz),
                output='screen'
            )
        ]
    )

    return LaunchDescription([
        # 声明启动参数
        DeclareLaunchArgument(
            'use_sim_time',
            default_value='false',
            description='Use simulation (Gazebo) clock if true'
        ),
        DeclareLaunchArgument(
            'use_ekf',
            default_value='true',  # 默认启用EKF，与nav_03保持一致
            description='Set to "true" to use EKF for odometry fusion.'
        ),
        DeclareLaunchArgument(
            'start_rviz',
            default_value='false',
            description='Whether to start RViz for localization visualization'
        ),
        DeclareLaunchArgument(
            'map_file',
            default_value=PathJoinSubstitution([pkg_robotcar_nav, 'maps', '0703.pbstream']),
            description='Full path to the .pbstream map file to load for localization'
        ),

        # 启动序列
        robotcar_bringup_launch,  # 立即启动硬件
        oradar_scan_launch,       # 立即启动激光雷达
        nav02_ekf_launch,         # 立即启动EKF (如果启用)
        cartographer_node,        # 2秒后启动定位
        occupancy_grid_node,      # 3秒后启动占用栅格(发布地图给Nav2)
        localization_rviz_node,   # 3秒后启动RViz
    ])