#!/usr/bin/env python3

import os
from ament_index_python.packages import get_package_share_directory
from launch import LaunchDescription
from launch.actions import DeclareLaunchArgument, IncludeLaunchDescription
from launch.launch_description_sources import PythonLaunchDescriptionSource
from launch.substitutions import LaunchConfiguration, PathJoinSubstitution
from launch.conditions import IfCondition
from launch_ros.actions import Node


def generate_launch_description():
    """
    Simple launch file that starts:
    1. robotcar_base bringup.launch.py (ROS2 Control + IMU + robot_state_publisher)
    2. oradar_ros ms500_scan_view.launch.py (LiDAR + RViz)
    
    This provides the basic hardware interface and visualization for mapping and navigation.
    """
    
    # Get package directories
    pkg_robotcar_base = get_package_share_directory('robotcar_base')
    pkg_oradar_ros = get_package_share_directory('oradar_ros')
    pkg_robotcar_nav = get_package_share_directory('robotcar_nav')
    
    # Launch configuration
    use_sim_time = LaunchConfiguration('use_sim_time')
    start_rviz = LaunchConfiguration('start_rviz')
    rviz_config_file = PathJoinSubstitution([pkg_robotcar_nav, 'rviz', 'nav2.rviz'])
    
    # 1. Launch robotcar_base bringup (ROS2 Control + IMU + robot_state_publisher)
    robotcar_base_bringup_launch = IncludeLaunchDescription(
        PythonLaunchDescriptionSource(
            os.path.join(pkg_robotcar_base, 'launch', 'bringup.launch.py')
        ),
        launch_arguments={
            'use_sim_time': use_sim_time,
            'start_rviz': 'false'
        }.items()
    )
    
    # 2. Launch ORadar LiDAR only (without RViz)
    oradar_scan_launch = IncludeLaunchDescription(
        PythonLaunchDescriptionSource(
            os.path.join(pkg_oradar_ros, 'launch', 'ms500_scan_view.launch.py')
        ),
        launch_arguments={'use_sim_time': use_sim_time}.items()
    )
    
    # RViz node
    rviz_node = Node(
        package='rviz2',
        executable='rviz2',
        name='rviz2_bringup',
        arguments=['-d', rviz_config_file],
        parameters=[{'use_sim_time': use_sim_time}],
        condition=IfCondition(start_rviz),
        output='screen'
    )
    
    return LaunchDescription([
        DeclareLaunchArgument(
            'use_sim_time',
            default_value='false',
            description='Use simulation (Gazebo) clock if true'
        ),
        DeclareLaunchArgument(
            'start_rviz',
            default_value='true',
            description='Whether to start RViz'
        ),
        
        robotcar_base_bringup_launch,
        oradar_scan_launch,
        rviz_node,
    ])
