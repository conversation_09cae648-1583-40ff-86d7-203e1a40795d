import os
from ament_index_python.packages import get_package_share_directory
from launch import LaunchDescription
from launch.actions import DeclareLaunchArgument, IncludeLaunchDescription, TimerAction
from launch.substitutions import LaunchConfiguration, PathJoinSubstitution
from launch.launch_description_sources import PythonLaunchDescriptionSource
from launch_ros.actions import Node
from launch.conditions import IfCondition

def generate_launch_description():
    """
    Launch file that builds on top of nav_04_localization to run the full Nav2 stack.
    Uses the adjusted nav1234 configurations and 0624 map for navigation.
    """
    pkg_robotcar_nav = get_package_share_directory('robotcar_nav')


    use_sim_time = LaunchConfiguration('use_sim_time')
    use_ekf = LaunchConfiguration('use_ekf')
    start_rviz = LaunchConfiguration('start_rviz')

    # Nav2 configuration file - 
    nav2_params_file_path = PathJoinSubstitution(
        [pkg_robotcar_nav, 'config', 'nav2_simple.yaml']
    )

    # Nav2 uses YAML format map for navigation
    navigation_map_file = LaunchConfiguration('navigation_map_file')

    # Cartographer requires a .pbstream file - 使用0624地图
    cartographer_map_file = LaunchConfiguration('cartographer_map_file')

    # RViz配置文件 - 修正路径到rviz目录
    rviz_config_file = PathJoinSubstitution([
        get_package_share_directory('robotcar_nav'), 'rviz', 'nav2.rviz'
    ])
    
    return LaunchDescription([
        # 声明启动参数
        DeclareLaunchArgument(
            'use_sim_time',
            default_value='false',
            description='Use simulation (Gazebo) clock if true'
        ),
        DeclareLaunchArgument(
            'use_ekf',
            default_value='true',  # 默认启用EKF
            description='Set to "true" to use EKF for odometry fusion.'
        ),
        DeclareLaunchArgument(
            'start_rviz',
            default_value='false',
            description='Whether to start RViz with navigation configuration'
        ),
        DeclareLaunchArgument(
            'navigation_map_file',
            default_value=PathJoinSubstitution([pkg_robotcar_nav, 'maps', '0703.yaml']),
            description='Full path to the YAML map file for Nav2 navigation'
        ),

        DeclareLaunchArgument(
            'nav2_params_file',
            default_value=nav2_params_file_path,  # 简化：默认使用标准nav2配置
            description='Full path to the Nav2 parameters file'
        ),
        DeclareLaunchArgument(
            'cartographer_map_file',
            default_value=PathJoinSubstitution([pkg_robotcar_nav, 'maps', '0703.pbstream']),
            description='Full path to the .pbstream map file to load for Cartographer localization'
        ),

        # 1. Launch the localization system (nav_04) - 并且确保不启动它自己的RViz
        IncludeLaunchDescription(
            PythonLaunchDescriptionSource(
                os.path.join(pkg_robotcar_nav, 'launch', 'nav_04_localization.launch.py')),
            launch_arguments={
                'use_sim_time': use_sim_time,
                'use_ekf': use_ekf,
                'start_rviz': 'true',  # 禁用nav_04中的RViz，由nav_05统一管理
                'map_file': cartographer_map_file
            }.items()),

        # 2. Launch Nav2 navigation using source code (without AMCL, without map_server)
        IncludeLaunchDescription(
            PythonLaunchDescriptionSource(
                '/home/<USER>/test_ws/src/navigation/nav2_bringup/launch/navigation_launch.py'),
            launch_arguments={
                'use_sim_time': use_sim_time,
                'params_file': nav2_params_file_path,
                'autostart': 'true',
            }.items()),

        # 3. 话题重映射节点：将Nav2的cmd_vel重映射到ROS2 Control
        Node(
            package='topic_tools',
            executable='relay',
            name='cmd_vel_relay',
            arguments=['/cmd_vel', '/diff_drive_controller/cmd_vel_unstamped'],
            parameters=[{'use_sim_time': use_sim_time}],
            output='screen'
        ),

        # 4. Launch RViz with navigation configuration (延迟启动，确保所有系统就绪)
        TimerAction(
            period=5.0,  # 等待5秒让定位和导航系统完全启动
            actions=[
                Node(
                    package='rviz2',
                    executable='rviz2',
                    name='rviz2_navigation',
                    arguments=['-d', rviz_config_file],
                    parameters=[{'use_sim_time': use_sim_time}],
                    condition=IfCondition(start_rviz),
                    output='screen'
                )
            ]
        ),
    ])