import os
from ament_index_python.packages import get_package_share_directory
from launch import LaunchDescription
from launch.actions import DeclareLaunchArgument, IncludeLaunchDescription
from launch.substitutions import LaunchConfiguration, PathJoinSubstitution
from launch.launch_description_sources import PythonLaunchDescriptionSource
from launch_ros.actions import Node

def generate_launch_description():
    """
    Launch file for running multi-point (waypoint) navigation.
    """
    pkg_robotcar_nav = get_package_share_directory('robotcar_nav')

    use_sim_time = LaunchConfiguration('use_sim_time')
    use_ekf = LaunchConfiguration('use_ekf')
    use_sensor_sync = LaunchConfiguration('use_sensor_sync')
    use_laser_filter = LaunchConfiguration('use_laser_filter')
    lidar_type = LaunchConfiguration('lidar_type')
    params_file = LaunchConfiguration('params_file')

    return LaunchDescription([
        DeclareLaunchArgument(
            'use_sim_time',
            default_value='false',
            description='Use simulation (Gazebo) clock if true'),
        DeclareLaunchArgument(
            'use_ekf',
            default_value='false',
            description='Set to "true" to use EKF for odometry fusion.'),
        DeclareLaunchArgument(
            'use_sensor_sync',
            default_value='false',
            description='Whether to use message_filters for sensor synchronization'),
        DeclareLaunchArgument(
            'use_laser_filter',
            default_value='true',
            description='Whether to use laser filter to remove outliers'),
        DeclareLaunchArgument(
            'lidar_type',
            default_value='oradar',
            description="Type of lidar to use: 'vanjee' or 'oradar'"),
        DeclareLaunchArgument(
            'params_file',
            default_value=PathJoinSubstitution([pkg_robotcar_nav, 'config', 'waypoints.yaml']),
            description='Full path to the waypoints parameter file'),

        # 1. Launch the full navigation stack
        IncludeLaunchDescription(
            PythonLaunchDescriptionSource(
                os.path.join(pkg_robotcar_nav, 'launch', 'nav_05_navigation.launch.py')),
            launch_arguments={
                'use_sim_time': use_sim_time,
                'use_ekf': use_ekf,
                'use_sensor_sync': use_sensor_sync,
                'use_laser_filter': use_laser_filter,
                'lidar_type': lidar_type,
                # Pass other arguments if needed, e.g., map file
            }.items()),

        # 2. Launch the waypoint follower node
        Node(
            package='robotcar_nav',
            executable='waypoint_follower_node',
            name='waypoint_follower_node',
            output='screen',
            parameters=[params_file, 
                        {'use_sim_time': use_sim_time}]
        ),
    ]) 