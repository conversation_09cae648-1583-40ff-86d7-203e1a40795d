#!/usr/bin/env python3
"""
Cartographer性能监控工具
监控TF发布频率和延迟，评估频率优化效果
"""

import rclpy
from rclpy.node import Node
import tf2_ros
import tf2_geometry_msgs
from geometry_msgs.msg import TransformStamped
import time
from collections import deque
import statistics
import threading

class CartographerPerformanceMonitor(Node):
    def __init__(self):
        super().__init__('cartographer_performance_monitor')
        
        # TF监听器
        self.tf_buffer = tf2_ros.Buffer()
        self.tf_listener = tf2_ros.TransformListener(self.tf_buffer, self)
        
        # 性能数据缓存
        self.map_odom_timestamps = deque(maxlen=200)  # 存储最近200个TF时间戳
        self.tf_lookup_latencies = deque(maxlen=100)  # TF查询延迟
        
        # 性能监控定时器
        self.monitor_timer = self.create_timer(0.1, self.monitor_tf_performance)  # 10Hz监控
        self.report_timer = self.create_timer(5.0, self.generate_performance_report)  # 5秒报告
        
        # 线程锁
        self.data_lock = threading.Lock()
        
        self.get_logger().info('Cartographer性能监控器已启动')
        self.get_logger().info('监控TF变换: map → odom')
    
    def monitor_tf_performance(self):
        """监控TF性能"""
        try:
            # 记录TF查询开始时间
            start_time = time.time()
            
            # 尝试获取map→odom变换
            transform = self.tf_buffer.lookup_transform(
                'map', 'odom', rclpy.time.Time()
            )
            
            # 记录查询延迟
            lookup_latency = time.time() - start_time
            
            # 获取TF时间戳
            tf_timestamp = transform.header.stamp.sec + transform.header.stamp.nanosec * 1e-9
            current_time = time.time()
            
            with self.data_lock:
                self.map_odom_timestamps.append(current_time)
                self.tf_lookup_latencies.append(lookup_latency)
                
                # 检查TF数据的新鲜度
                tf_age = current_time - tf_timestamp
                if tf_age > 0.1:  # 如果TF数据超过100ms
                    self.get_logger().warn(f'TF数据过时: {tf_age*1000:.1f}ms')
                    
        except tf2_ros.TransformException as e:
            self.get_logger().error(f'TF查询失败: {e}')
    
    def calculate_tf_frequency(self):
        """计算TF发布频率"""
        with self.data_lock:
            if len(self.map_odom_timestamps) < 10:
                return None, None
            
            # 计算时间间隔
            intervals = []
            timestamps = list(self.map_odom_timestamps)
            for i in range(1, len(timestamps)):
                intervals.append(timestamps[i] - timestamps[i-1])
            
            if not intervals:
                return None, None
            
            # 计算频率统计
            avg_interval = statistics.mean(intervals)
            frequency = 1.0 / avg_interval if avg_interval > 0 else 0
            jitter = statistics.stdev(intervals) if len(intervals) > 1 else 0
            
            return frequency, jitter
    
    def calculate_lookup_latency(self):
        """计算TF查询延迟统计"""
        with self.data_lock:
            if len(self.tf_lookup_latencies) < 5:
                return None, None, None
            
            latencies = list(self.tf_lookup_latencies)
            avg_latency = statistics.mean(latencies)
            max_latency = max(latencies)
            min_latency = min(latencies)
            
            return avg_latency, max_latency, min_latency
    
    def generate_performance_report(self):
        """生成性能报告"""
        self.get_logger().info('='*50)
        self.get_logger().info('Cartographer性能报告')
        self.get_logger().info('='*50)
        
        # TF频率分析
        frequency, jitter = self.calculate_tf_frequency()
        if frequency:
            self.get_logger().info(f'map→odom TF发布频率: {frequency:.1f}Hz')
            self.get_logger().info(f'频率抖动: {jitter*1000:.1f}ms')
            
            # 频率评估
            if frequency >= 35.0:
                self.get_logger().info('✅ TF发布频率良好 (≥35Hz)')
            elif frequency >= 25.0:
                self.get_logger().warn('⚠️  TF发布频率一般 (25-35Hz)')
            else:
                self.get_logger().error('❌ TF发布频率过低 (<25Hz)')
        else:
            self.get_logger().error('❌ 无法计算TF频率')
        
        # TF查询延迟分析
        avg_latency, max_latency, min_latency = self.calculate_lookup_latency()
        if avg_latency:
            self.get_logger().info(f'TF查询延迟 - 平均: {avg_latency*1000:.1f}ms, '
                                   f'最大: {max_latency*1000:.1f}ms, '
                                   f'最小: {min_latency*1000:.1f}ms')
            
            # 延迟评估
            if avg_latency < 0.005:  # 5ms
                self.get_logger().info('✅ TF查询延迟优秀 (<5ms)')
            elif avg_latency < 0.010:  # 10ms
                self.get_logger().warn('⚠️  TF查询延迟一般 (5-10ms)')
            else:
                self.get_logger().error('❌ TF查询延迟过高 (>10ms)')
        
        # 系统建议
        self.get_logger().info('-'*30)
        self.get_logger().info('优化建议:')
        
        if frequency and frequency < 30.0:
            self.get_logger().warn('建议: 增加Cartographer pose_publish_period_sec频率')
            self.get_logger().warn('  建图模式: pose_publish_period_sec = 0.025 (40Hz)')
            self.get_logger().warn('  定位模式: pose_publish_period_sec = 0.02 (50Hz)')
        
        if avg_latency and avg_latency > 0.01:
            self.get_logger().warn('建议: 优化系统性能，减少TF查询延迟')
            self.get_logger().warn('  - 检查CPU负载')
            self.get_logger().warn('  - 优化Cartographer参数')
        
        # 与导航系统的匹配性
        if frequency:
            controller_freq = 40.0  # 从配置文件读取
            if abs(frequency - controller_freq) > 5.0:
                self.get_logger().warn(f'⚠️  TF频率与控制器不匹配: {frequency:.1f}Hz vs {controller_freq}Hz')
            else:
                self.get_logger().info('✅ TF频率与控制器匹配良好')
        
        self.get_logger().info('='*50)

def main():
    rclpy.init()
    
    monitor = CartographerPerformanceMonitor()
    
    try:
        self.get_logger().info('开始监控Cartographer性能...')
        self.get_logger().info('按Ctrl+C停止监控')
        rclpy.spin(monitor)
    except KeyboardInterrupt:
        pass
    finally:
        monitor.destroy_node()
        rclpy.shutdown()

if __name__ == '__main__':
    main()