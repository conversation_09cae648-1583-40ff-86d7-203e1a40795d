#!/usr/bin/env python3
"""
传感器频率诊断工具
监控各传感器的实际发布频率，检测频率不匹配问题
"""

import rclpy
from rclpy.node import Node
from sensor_msgs.msg import Imu, LaserScan
from nav_msgs.msg import Odometry
import time
from collections import deque
import statistics

class FrequencyMonitor(Node):
    def __init__(self):
        super().__init__('frequency_monitor')
        
        # 创建订阅者
        self.imu_sub = self.create_subscription(Imu, '/imu', self.imu_callback, 10)
        self.scan_sub = self.create_subscription(LaserScan, '/scan', self.scan_callback, 10)
        self.odom_sub = self.create_subscription(Odometry, '/diff_drive_controller/odom', self.odom_callback, 10)
        self.filtered_odom_sub = self.create_subscription(Odometry, '/odom', self.filtered_odom_callback, 10)
        
        # 时间戳缓存 (保存最近100个消息的时间戳)
        self.imu_timestamps = deque(maxlen=100)
        self.scan_timestamps = deque(maxlen=100)
        self.odom_timestamps = deque(maxlen=100)
        self.filtered_odom_timestamps = deque(maxlen=100)
        
        # 创建诊断定时器
        self.diagnosis_timer = self.create_timer(5.0, self.diagnose_frequencies)
        
        self.get_logger().info('传感器频率监控器已启动')
        self.get_logger().info('监控话题:')
        self.get_logger().info('  - /imu (IMU数据)')
        self.get_logger().info('  - /scan (激光雷达)')
        self.get_logger().info('  - /diff_drive_controller/odom (原始里程计)')
        self.get_logger().info('  - /odom (EKF融合里程计)')
    
    def imu_callback(self, msg):
        current_time = time.time()
        self.imu_timestamps.append(current_time)
    
    def scan_callback(self, msg):
        current_time = time.time()
        self.scan_timestamps.append(current_time)
    
    def odom_callback(self, msg):
        current_time = time.time()
        self.odom_timestamps.append(current_time)
    
    def filtered_odom_callback(self, msg):
        current_time = time.time()
        self.filtered_odom_timestamps.append(current_time)
    
    def calculate_frequency(self, timestamps):
        """计算频率统计信息"""
        if len(timestamps) < 10:
            return None, None, None
        
        # 计算时间间隔
        intervals = []
        for i in range(1, len(timestamps)):
            intervals.append(timestamps[i] - timestamps[i-1])
        
        # 计算频率
        avg_interval = statistics.mean(intervals)
        freq_hz = 1.0 / avg_interval if avg_interval > 0 else 0
        
        # 计算抖动 (标准差)
        jitter = statistics.stdev(intervals) if len(intervals) > 1 else 0
        
        # 计算频率范围
        min_interval = min(intervals)
        max_interval = max(intervals)
        min_freq = 1.0 / max_interval if max_interval > 0 else 0
        max_freq = 1.0 / min_interval if min_interval > 0 else 0
        
        return freq_hz, jitter, (min_freq, max_freq)
    
    def diagnose_frequencies(self):
        """诊断各传感器频率"""
        self.get_logger().info('='*60)
        self.get_logger().info('传感器频率诊断报告')
        self.get_logger().info('='*60)
        
        # IMU频率分析
        imu_freq, imu_jitter, imu_range = self.calculate_frequency(self.imu_timestamps)
        if imu_freq:
            self.get_logger().info(f'IMU频率: {imu_freq:.1f}Hz (抖动: {imu_jitter*1000:.1f}ms)')
            self.get_logger().info(f'  频率范围: {imu_range[0]:.1f}-{imu_range[1]:.1f}Hz')
            if abs(imu_freq - 50.0) > 5.0:
                self.get_logger().warn(f'  ⚠️  IMU频率异常！期望50Hz，实际{imu_freq:.1f}Hz')
        else:
            self.get_logger().error('❌ IMU数据未收到或不足')
        
        # 激光雷达频率分析
        scan_freq, scan_jitter, scan_range = self.calculate_frequency(self.scan_timestamps)
        if scan_freq:
            self.get_logger().info(f'激光雷达频率: {scan_freq:.1f}Hz (抖动: {scan_jitter*1000:.1f}ms)')
            self.get_logger().info(f'  频率范围: {scan_range[0]:.1f}-{scan_range[1]:.1f}Hz')
            if abs(scan_freq - 25.0) > 3.0:
                self.get_logger().warn(f'  ⚠️  激光雷达频率异常！期望25Hz，实际{scan_freq:.1f}Hz')
        else:
            self.get_logger().error('❌ 激光雷达数据未收到或不足')
        
        # 原始里程计频率分析
        odom_freq, odom_jitter, odom_range = self.calculate_frequency(self.odom_timestamps)
        if odom_freq:
            self.get_logger().info(f'原始里程计频率: {odom_freq:.1f}Hz (抖动: {odom_jitter*1000:.1f}ms)')
            self.get_logger().info(f'  频率范围: {odom_range[0]:.1f}-{odom_range[1]:.1f}Hz')
            if abs(odom_freq - 50.0) > 5.0:
                self.get_logger().warn(f'  ⚠️  里程计频率异常！期望50Hz，实际{odom_freq:.1f}Hz')
        else:
            self.get_logger().error('❌ 原始里程计数据未收到或不足')
        
        # EKF融合里程计频率分析
        filtered_freq, filtered_jitter, filtered_range = self.calculate_frequency(self.filtered_odom_timestamps)
        if filtered_freq:
            self.get_logger().info(f'EKF融合里程计频率: {filtered_freq:.1f}Hz (抖动: {filtered_jitter*1000:.1f}ms)')
            self.get_logger().info(f'  频率范围: {filtered_range[0]:.1f}-{filtered_range[1]:.1f}Hz')
            if abs(filtered_freq - 50.0) > 5.0:
                self.get_logger().warn(f'  ⚠️  EKF输出频率异常！期望50Hz，实际{filtered_freq:.1f}Hz')
        else:
            self.get_logger().error('❌ EKF融合里程计数据未收到或不足')
        
        # 频率匹配性分析
        self.get_logger().info('-'*40)
        self.get_logger().info('频率匹配性分析:')
        
        if imu_freq and odom_freq:
            if abs(imu_freq - odom_freq) > 10.0:
                self.get_logger().warn(f'⚠️  IMU与里程计频率不匹配: {imu_freq:.1f}Hz vs {odom_freq:.1f}Hz')
            else:
                self.get_logger().info(f'✅ IMU与里程计频率匹配良好')
        
        # 系统建议
        self.get_logger().info('-'*40)
        self.get_logger().info('系统优化建议:')
        
        if imu_freq and imu_freq > 60.0:
            self.get_logger().warn('建议: IMU频率过高，考虑降低到50Hz以节省计算资源')
        
        if scan_freq and scan_freq < 20.0:
            self.get_logger().warn('建议: 激光雷达频率过低，考虑提高到25Hz以改善导航精度')
        
        # 检查时间戳抖动
        if imu_jitter and imu_jitter > 0.01:  # 10ms抖动
            self.get_logger().warn('建议: IMU时间戳抖动较大，检查系统负载和IMU驱动')
        
        self.get_logger().info('='*60)

def main():
    rclpy.init()
    
    monitor = FrequencyMonitor()
    
    try:
        self.get_logger().info('开始监控传感器频率...')
        self.get_logger().info('按Ctrl+C停止监控')
        rclpy.spin(monitor)
    except KeyboardInterrupt:
        pass
    finally:
        monitor.destroy_node()
        rclpy.shutdown()

if __name__ == '__main__':
    main()