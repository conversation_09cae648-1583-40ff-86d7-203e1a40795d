#!/usr/bin/env python3
"""
里程计校准脚本
使用方法：
1. 让机器人直行1米，测量实际距离
2. 让机器人原地旋转360度，测量实际角度
3. 根据结果调整controllers.yaml中的参数
"""

import rclpy
from rclpy.node import Node
from nav_msgs.msg import Odometry
from geometry_msgs.msg import Twist
import math
import time

class OdomCalibration(Node):
    def __init__(self):
        super().__init__('odom_calibration')
        
        # 订阅里程计话题
        self.odom_sub = self.create_subscription(
            Odometry, '/diff_drive_controller/odom', 
            self.odom_callback, 10)
        
        # 发布速度命令
        self.cmd_pub = self.create_publisher(Twist, '/cmd_vel', 10)
        
        # 初始化变量
        self.start_pose = None
        self.current_pose = None
        self.calibration_mode = None
        
        self.get_logger().info('里程计校准节点已启动')
        self.get_logger().info('使用方法：')
        self.get_logger().info('1. 调用 linear_test() 测试直行')
        self.get_logger().info('2. 调用 angular_test() 测试旋转')
    
    def odom_callback(self, msg):
        """里程计回调函数"""
        self.current_pose = msg.pose.pose
        
        if self.start_pose is None:
            self.start_pose = self.current_pose
    
    def linear_test(self, distance=1.0, speed=0.2):
        """直行测试
        Args:
            distance: 目标距离（米）
            speed: 速度（m/s）
        """
        self.get_logger().info(f'开始直行测试：目标距离{distance}m，速度{speed}m/s')
        
        # 重置起始位置
        self.start_pose = None
        while self.start_pose is None:
            rclpy.spin_once(self, timeout_sec=0.1)
        
        # 发送速度命令
        cmd = Twist()
        cmd.linear.x = speed
        
        start_time = time.time()
        while True:
            # 计算已行驶距离
            if self.current_pose is not None:
                dx = self.current_pose.position.x - self.start_pose.position.x
                dy = self.current_pose.position.y - self.start_pose.position.y
                traveled = math.sqrt(dx*dx + dy*dy)
                
                if traveled >= distance:
                    break
            
            # 发送速度命令
            self.cmd_pub.publish(cmd)
            rclpy.spin_once(self, timeout_sec=0.1)
        
        # 停止机器人
        cmd.linear.x = 0.0
        self.cmd_pub.publish(cmd)
        
        # 计算最终距离
        dx = self.current_pose.position.x - self.start_pose.position.x
        dy = self.current_pose.position.y - self.start_pose.position.y
        final_distance = math.sqrt(dx*dx + dy*dy)
        
        self.get_logger().info(f'直行测试完成：')
        self.get_logger().info(f'  目标距离：{distance:.3f}m')
        self.get_logger().info(f'  里程计距离：{final_distance:.3f}m')
        self.get_logger().info(f'  误差：{final_distance - distance:.3f}m')
        
        if abs(final_distance - distance) > 0.05:
            error_ratio = distance / final_distance
            self.get_logger().warn(f'建议调整wheel_radius_multiplier为：{error_ratio:.3f}')
    
    def angular_test(self, angle=360.0, speed=0.5):
        """旋转测试
        Args:
            angle: 目标角度（度）
            speed: 角速度（rad/s）
        """
        self.get_logger().info(f'开始旋转测试：目标角度{angle}度，角速度{speed}rad/s')
        
        # 重置起始位置
        self.start_pose = None
        while self.start_pose is None:
            rclpy.spin_once(self, timeout_sec=0.1)
        
        # 获取起始角度
        start_yaw = self.get_yaw(self.start_pose.orientation)
        
        # 发送角速度命令
        cmd = Twist()
        cmd.angular.z = speed
        
        target_angle_rad = math.radians(angle)
        
        while True:
            # 计算已旋转角度
            if self.current_pose is not None:
                current_yaw = self.get_yaw(self.current_pose.orientation)
                rotated = self.normalize_angle(current_yaw - start_yaw)
                
                if abs(rotated) >= abs(target_angle_rad):
                    break
            
            # 发送角速度命令
            self.cmd_pub.publish(cmd)
            rclpy.spin_once(self, timeout_sec=0.1)
        
        # 停止机器人
        cmd.angular.z = 0.0
        self.cmd_pub.publish(cmd)
        
        # 计算最终角度
        final_yaw = self.get_yaw(self.current_pose.orientation)
        final_angle = math.degrees(self.normalize_angle(final_yaw - start_yaw))
        
        self.get_logger().info(f'旋转测试完成：')
        self.get_logger().info(f'  目标角度：{angle:.1f}度')
        self.get_logger().info(f'  里程计角度：{final_angle:.1f}度')
        self.get_logger().info(f'  误差：{final_angle - angle:.1f}度')
        
        if abs(final_angle - angle) > 5.0:
            error_ratio = angle / final_angle
            self.get_logger().warn(f'建议调整wheel_separation_multiplier为：{error_ratio:.3f}')
    
    def get_yaw(self, orientation):
        """从四元数获取偏航角"""
        import tf_transformations
        euler = tf_transformations.euler_from_quaternion([
            orientation.x, orientation.y, orientation.z, orientation.w])
        return euler[2]
    
    def normalize_angle(self, angle):
        """角度归一化到[-π, π]"""
        while angle > math.pi:
            angle -= 2.0 * math.pi
        while angle < -math.pi:
            angle += 2.0 * math.pi
        return angle

def main():
    rclpy.init()
    
    calibration = OdomCalibration()
    
    try:
        # 可以在这里调用测试函数
        # calibration.linear_test(1.0, 0.2)  # 直行1米测试
        # calibration.angular_test(360.0, 0.5)  # 旋转360度测试
        
        rclpy.spin(calibration)
    except KeyboardInterrupt:
        pass
    finally:
        calibration.destroy_node()
        rclpy.shutdown()

if __name__ == '__main__':
    main()