#include "rclcpp/rclcpp.hpp"
#include "sensor_msgs/msg/imu.hpp"
#include "sensor_msgs/msg/laser_scan.hpp"
#include "robotcar_base/msg/car_info.hpp"

#include "message_filters/subscriber.h"
#include "message_filters/synchronizer.h"
#include "message_filters/sync_policies/approximate_time.h"
#include "message_filters/sync_policies/exact_time.h"

using namespace message_filters;

class SensorSyncNode : public rclcpp::Node
{
public:
    SensorSyncNode() : Node("sensor_sync_node")
    {
        // 声明并获取参数
        this->declare_parameter<std::string>("sync_mode", "approximate");
        this->declare_parameter<double>("sync_slop", 0.1); // 近似同步允许的时间差，单位：秒
        this->declare_parameter<bool>("use_filtered_scan", true); // 是否使用过滤后的激光雷达数据
        this->declare_parameter<std::vector<std::string>>("scan_topic", std::vector<std::string>{"/scan_filtered", "/scan"});
        
        std::string sync_mode = this->get_parameter("sync_mode").as_string();
        double sync_slop = this->get_parameter("sync_slop").as_double();
        bool use_filtered_scan = this->get_parameter("use_filtered_scan").as_bool();
        std::vector<std::string> scan_topics = this->get_parameter("scan_topic").as_string_array();
        
        // 选择激光雷达话题
        std::string scan_topic;
        if (use_filtered_scan && scan_topics.size() > 0) {
            scan_topic = scan_topics[0]; // 使用过滤后的话题 (默认第一个)
        } else if (!use_filtered_scan && scan_topics.size() > 1) {
            scan_topic = scan_topics[1]; // 使用原始话题 (默认第二个)
        } else {
            scan_topic = "scan"; // 默认话题
        }
        
        RCLCPP_INFO(this->get_logger(), "Using %s time synchronization with slop: %f seconds", 
                   sync_mode.c_str(), sync_slop);
        RCLCPP_INFO(this->get_logger(), "Using scan topic: %s", scan_topic.c_str());
        
        // 创建发布者
        synced_imu_pub_ = this->create_publisher<sensor_msgs::msg::Imu>("synced/imu", 10);
        synced_scan_pub_ = this->create_publisher<sensor_msgs::msg::LaserScan>("synced/scan", 10);
        synced_car_info_pub_ = this->create_publisher<robotcar_base::msg::CarInfo>("synced/car_info", 10);
        
        // 创建订阅者 (注意：这里使用message_filters的Subscriber，而不是rclcpp::Subscription)
        imu_sub_.subscribe(this, "imu");
        scan_sub_.subscribe(this, scan_topic); // 使用选择的激光雷达话题
        car_info_sub_.subscribe(this, "CarInfo");
        
        // 根据同步模式创建同步器
        if (sync_mode == "exact") {
            // 精确时间同步 - 要求时间戳完全相同
            using ExactSyncPolicy = message_filters::sync_policies::ExactTime<
                sensor_msgs::msg::Imu, 
                sensor_msgs::msg::LaserScan,
                robotcar_base::msg::CarInfo>;
                
            exact_sync_ = std::make_shared<message_filters::Synchronizer<ExactSyncPolicy>>(
                ExactSyncPolicy(10), imu_sub_, scan_sub_, car_info_sub_);
                
            exact_sync_->registerCallback(
                std::bind(&SensorSyncNode::syncCallback, this, std::placeholders::_1, 
                          std::placeholders::_2, std::placeholders::_3));
        } else {
            // 近似时间同步 - 允许时间戳有一定误差
            using ApproxSyncPolicy = message_filters::sync_policies::ApproximateTime<
                sensor_msgs::msg::Imu, 
                sensor_msgs::msg::LaserScan,
                robotcar_base::msg::CarInfo>;
                
            approx_sync_ = std::make_shared<message_filters::Synchronizer<ApproxSyncPolicy>>(
                ApproxSyncPolicy(10), imu_sub_, scan_sub_, car_info_sub_);
                
            // 设置允许的时间差
            approx_sync_->setMaxIntervalDuration(rclcpp::Duration::from_seconds(sync_slop));
            
            approx_sync_->registerCallback(
                std::bind(&SensorSyncNode::syncCallback, this, std::placeholders::_1, 
                          std::placeholders::_2, std::placeholders::_3));
        }
        
        RCLCPP_INFO(this->get_logger(), "Sensor synchronization node initialized");
    }

private:
    void syncCallback(const sensor_msgs::msg::Imu::ConstSharedPtr& imu_msg,
                     const sensor_msgs::msg::LaserScan::ConstSharedPtr& scan_msg,
                     const robotcar_base::msg::CarInfo::ConstSharedPtr& car_info_msg)
    {
        // 获取当前ROS时间作为同步后的时间戳
        rclcpp::Time now = this->get_clock()->now();
        
        // 统计和打印时间差
        double imu_diff = (now - imu_msg->header.stamp).seconds();
        double scan_diff = (now - scan_msg->header.stamp).seconds();
        
        RCLCPP_DEBUG(this->get_logger(), 
                    "Time diff - IMU: %.3fs, Scan: %.3fs", 
                    imu_diff, scan_diff);
        
        // 创建同步的消息副本
        auto synced_imu = *imu_msg;
        auto synced_scan = *scan_msg;
        auto synced_car_info = *car_info_msg;
        
        // 如果需要，可以更新时间戳，这样下游节点收到的时间戳是一致的
        // 注意：在某些情况下，保留原始时间戳可能更合适
        // synced_imu.header.stamp = now;
        // synced_scan.header.stamp = now;
        // 如果CarInfo有header，也应该更新
        
        // 发布同步后的消息
        synced_imu_pub_->publish(synced_imu);
        synced_scan_pub_->publish(synced_scan);
        synced_car_info_pub_->publish(synced_car_info);
        
        static int count = 0;
        if (++count % 100 == 0) {  // 每100条消息打印一次，减少日志量
            RCLCPP_INFO(this->get_logger(), "Synchronized %d message sets", count);
        }
    }

    // Message filters 订阅者
    message_filters::Subscriber<sensor_msgs::msg::Imu> imu_sub_;
    message_filters::Subscriber<sensor_msgs::msg::LaserScan> scan_sub_;
    message_filters::Subscriber<robotcar_base::msg::CarInfo> car_info_sub_;
    
    // 同步器
    using ExactSync = message_filters::Synchronizer<
        message_filters::sync_policies::ExactTime<
            sensor_msgs::msg::Imu, 
            sensor_msgs::msg::LaserScan,
            robotcar_base::msg::CarInfo>>;
    using ApproxSync = message_filters::Synchronizer<
        message_filters::sync_policies::ApproximateTime<
            sensor_msgs::msg::Imu, 
            sensor_msgs::msg::LaserScan,
            robotcar_base::msg::CarInfo>>;
            
    std::shared_ptr<ExactSync> exact_sync_;
    std::shared_ptr<ApproxSync> approx_sync_;
    
    // 发布者
    rclcpp::Publisher<sensor_msgs::msg::Imu>::SharedPtr synced_imu_pub_;
    rclcpp::Publisher<sensor_msgs::msg::LaserScan>::SharedPtr synced_scan_pub_;
    rclcpp::Publisher<robotcar_base::msg::CarInfo>::SharedPtr synced_car_info_pub_;
};

int main(int argc, char * argv[])
{
    rclcpp::init(argc, argv);
    auto node = std::make_shared<SensorSyncNode>();
    rclcpp::spin(node);
    rclcpp::shutdown();
    return 0;
} 