# ========================================
# Nav2导航配置文件 - 详细注释版本
# 专为Cartographer定位 + Nav2导航设计
# 适用于差速轮机器人
# ========================================

# ========================================
# 行为树导航器 (BT Navigator)
# 负责高层决策和任务协调
# ========================================
bt_navigator:
  ros__parameters:
    use_sim_time: false                    # 是否使用仿真时间
    global_frame: map                      # 全局坐标系，用于路径规划
    robot_base_frame: base_footprint       # 机器人基础坐标系
    odom_topic: /odom                      # 里程计话题
    bt_loop_duration: 10                  # 行为树循环周期(ms)
    default_server_timeout: 20            # 默认服务器超时时间(s)
    
    # 使用Nav2默认行为树，包含重规划和恢复行为
    default_nav_to_pose_bt_xml: "/opt/ros/humble/share/nav2_bt_navigator/behavior_trees/navigate_to_pose_w_replanning_and_recovery.xml"
    default_nav_through_poses_bt_xml: "/opt/ros/humble/share/nav2_bt_navigator/behavior_trees/navigate_to_pose_w_replanning_and_recovery.xml"
    
    # 行为树插件库，定义可用的行为节点
    plugin_lib_names:
      - nav2_compute_path_to_pose_action_bt_node      # 路径规划动作
      - nav2_follow_path_action_bt_node               # 路径跟踪动作
      - nav2_back_up_action_bt_node                   # 后退恢复动作
      - nav2_spin_action_bt_node                      # 旋转恢复动作
      - nav2_wait_action_bt_node                      # 等待动作
      - nav2_clear_costmap_service_bt_node            # 清除代价地图服务
      - nav2_is_stuck_condition_bt_node               # 卡住检测条件
      - nav2_goal_reached_condition_bt_node           # 目标到达条件
      - nav2_goal_updated_condition_bt_node           # 目标更新条件
      - nav2_globally_updated_goal_condition_bt_node  # 全局目标更新条件
      - nav2_is_path_valid_condition_bt_node          # 路径有效性条件
      - nav2_initial_pose_received_condition_bt_node  # 初始位姿接收条件
      - nav2_reinitialize_global_localization_service_bt_node # 重新初始化定位服务
      - nav2_rate_controller_bt_node                  # 频率控制器
      - nav2_distance_controller_bt_node              # 距离控制器
      - nav2_speed_controller_bt_node                 # 速度控制器
      - nav2_truncate_path_action_bt_node             # 路径截断动作
      - nav2_goal_updater_node_bt_node                # 目标更新节点
      - nav2_recovery_node_bt_node                    # 恢复节点
      - nav2_pipeline_sequence_bt_node                # 管道序列节点
      - nav2_round_robin_node_bt_node                 # 轮询节点
      - nav2_transform_available_condition_bt_node    # 变换可用条件
      - nav2_time_expired_condition_bt_node           # 时间过期条件
      - nav2_distance_traveled_condition_bt_node      # 行驶距离条件
      - nav2_single_trigger_bt_node                   # 单次触发节点

# ========================================
# 控制器服务器 (Controller Server)
# 负责局部路径跟踪和避障
# ========================================
controller_server:
  ros__parameters:
    use_sim_time: false
    controller_frequency: 20.0             # 控制器运行频率(Hz)
    
    # 速度阈值设置
    min_x_velocity_threshold: 0.001        # X方向最小速度阈值
    min_y_velocity_threshold: 0.5          # Y方向最小速度阈值(差速轮为0)
    min_theta_velocity_threshold: 0.001    # 角速度最小阈值
    
    failure_tolerance: 5.0                 # 失败容忍时间(s)
    
    # 插件配置
    progress_checker_plugin: "progress_checker"    # 进度检查器
    goal_checker_plugins: ["goal_checker"]         # 目标检查器
    controller_plugins: ["FollowPath"]             # 路径跟踪控制器
    
    # 进度检查器：检测机器人是否在合理时间内取得进展
    progress_checker:
      plugin: "nav2_controller::SimpleProgressChecker"
      required_movement_radius: 0.5        # 要求的移动半径(m)
      movement_time_allowance: 10.0        # 移动时间容忍度(s)
    
    # 目标检查器：判断是否到达目标
    goal_checker:
      plugin: "nav2_controller::SimpleGoalChecker"
      xy_goal_tolerance: 0.20               # 位置容忍度(m)
      yaw_goal_tolerance: 0.10              # 角度容忍度(rad)
      stateful: True                        # 状态保持
    
    # DWB局部规划器：动态窗口方法
    FollowPath:
      plugin: "dwb_core::DWBLocalPlanner"
      debug_trajectory_details: True       # 调试轨迹详情
      
      # 速度限制 (根据机器人URDF参数调整)
      min_vel_x: 0.0                       # 最小X速度(禁止倒车)
      min_vel_y: 0.0                       # 最小Y速度(差速轮为0)
      max_vel_x: 0.3                       # 最大X速度(m/s)
      max_vel_y: 0.0                       # 最大Y速度(差速轮为0)
      max_vel_theta: 0.8                   # 最大角速度(rad/s)
      
      # 速度范围
      min_speed_xy: 0.0                    # 最小线速度
      max_speed_xy: 0.3                    # 最大线速度
      min_speed_theta: 0.0                 # 最小角速度
      
      # 加速度限制
      acc_lim_x: 2.5                       # X方向加速度限制
      acc_lim_y: 0.0                       # Y方向加速度限制
      acc_lim_theta: 3.2                   # 角加速度限制
      
      # 减速度限制
      decel_lim_x: -2.5                    # X方向减速度限制
      decel_lim_y: 0.0                     # Y方向减速度限制
      decel_lim_theta: -3.2                # 角减速度限制
      
      # 采样参数：影响轨迹生成的精度和计算量
      vx_samples: 20                       # X速度采样数
      vy_samples: 5                        # Y速度采样数
      vtheta_samples: 20                   # 角速度采样数
      
      # 仿真参数
      sim_time: 1.7                        # 轨迹仿真时间(s)
      linear_granularity: 0.05             # 线性粒度(m)
      angular_granularity: 0.025           # 角度粒度(rad)
      
      # 容忍度设置
      transform_tolerance: 0.2             # 变换容忍度(s)
      xy_goal_tolerance: 0.25              # 位置目标容忍度(m)
      trans_stopped_velocity: 0.25         # 停止速度阈值(m/s)
      
      # 优化设置
      short_circuit_trajectory_evaluation: True  # 短路轨迹评估
      stateful: True                       # 状态保持
      
      # 评价函数：决定轨迹选择的权重
      critics: ["RotateToGoal", "Oscillation", "BaseObstacle", "GoalAlign", "PathAlign", "PathDist", "GoalDist"]
      BaseObstacle.scale: 0.02             # 基础障碍物权重
      PathAlign.scale: 32.0                # 路径对齐权重
      PathDist.scale: 24.0                 # 路径距离权重
      GoalAlign.scale: 24.0                # 目标对齐权重
      GoalDist.scale: 24.0                 # 目标距离权重
      RotateToGoal.scale: 32.0             # 朝向目标旋转权重
      Oscillation.scale: 0.05              # 振荡惩罚权重

# ========================================
# 局部代价地图 (Local Costmap)
# 用于局部避障和路径跟踪
# ========================================
local_costmap:
  local_costmap:
    ros__parameters:
      update_frequency: 5.0                # 更新频率(Hz)
      publish_frequency: 2.0               # 发布频率(Hz)
      global_frame: odom                   # 全局坐标系(必须是odom)
      robot_base_frame: base_footprint     # 机器人基础坐标系
      transform_timeout: 2.0               # 变换超时时间(s)
      tf_buffer_duration: 10.0             # TF缓冲时长(s)
      use_sim_time: false
      
      # 滚动窗口设置
      rolling_window: true                 # 启用滚动窗口
      width: 3                             # 地图宽度(m)
      height: 3                            # 地图高度(m)
      resolution: 0.05                     # 地图分辨率(m/pixel)
      
      # 机器人形状：使用矩形footprint而非圆形
      footprint: "[[0.325, 0.25], [0.325, -0.25], [-0.325, -0.25], [-0.325, 0.25]]"
      # robot_radius: 0.41                 # 圆形配置已注释
      
      # 插件配置
      plugins: ["obstacle_layer", "inflation_layer"]
      
      # 膨胀层：在障碍物周围创建安全区域
      inflation_layer:
        plugin: "nav2_costmap_2d::InflationLayer"
        ros__parameters:
          cost_scaling_factor: 3.0          # 代价缩放因子
          inflation_radius: 0.55            # 膨胀半径(m)
      
      # 障碍物层：处理激光雷达数据
      obstacle_layer:
        plugin: "nav2_costmap_2d::ObstacleLayer"
        enabled: True
        observation_sources: scan           # 观测源
        scan:
          topic: /scan                      # 激光雷达话题
          max_obstacle_height: 2.0          # 最大障碍物高度(m)
          clearing: True                    # 启用清除
          marking: True                     # 启用标记
          data_type: "LaserScan"            # 数据类型
          raytrace_max_range: 3.0           # 光线追踪最大范围(m)
          raytrace_min_range: 0.0           # 光线追踪最小范围(m)
          obstacle_max_range: 2.5           # 障碍物检测最大范围(m)
          obstacle_min_range: 0.0           # 障碍物检测最小范围(m)
          sensor_frame: laser               # 传感器坐标系
      
      # 静态层配置
      static_layer:
        map_subscribe_transient_local: True
      
      always_send_full_costmap: True        # 总是发送完整代价地图

# ========================================
# 全局代价地图 (Global Costmap)
# 用于全局路径规划
# ========================================
global_costmap:
  global_costmap:
    ros__parameters:
      update_frequency: 1.0                # 更新频率(Hz) - 比局部地图低
      publish_frequency: 1.0               # 发布频率(Hz)
      global_frame: map                    # 全局坐标系(必须是map)
      robot_base_frame: base_footprint     # 机器人基础坐标系
      transform_timeout: 2.0               # 变换超时时间(s)
      tf_buffer_duration: 10.0             # TF缓冲时长(s)
      use_sim_time: false

      # 机器人形状：与局部地图保持一致
      footprint: "[[0.325, 0.25], [0.325, -0.25], [-0.325, -0.25], [-0.325, 0.25]]"
      # robot_radius: 0.41                 # 圆形配置已注释

      resolution: 0.05                     # 地图分辨率(m/pixel)
      track_unknown_space: false           # 不跟踪未知空间

      # 插件配置：包含静态层
      plugins: ["static_layer", "obstacle_layer", "inflation_layer"]

      # 障碍物层：与局部地图类似但范围更大
      obstacle_layer:
        plugin: "nav2_costmap_2d::ObstacleLayer"
        enabled: True
        observation_sources: scan
        scan:
          topic: /scan
          max_obstacle_height: 2.0
          clearing: True
          marking: True
          data_type: "LaserScan"
          raytrace_max_range: 3.0           # 全局地图可以使用更大范围
          raytrace_min_range: 0.0
          obstacle_max_range: 2.5
          obstacle_min_range: 0.0

      # 静态层：加载预建地图
      static_layer:
        plugin: "nav2_costmap_2d::StaticLayer"
        map_subscribe_transient_local: True # 订阅持久化地图

      # 膨胀层：与局部地图保持一致
      inflation_layer:
        plugin: "nav2_costmap_2d::InflationLayer"
        ros__parameters:
          cost_scaling_factor: 3.0
          inflation_radius: 0.55

      always_send_full_costmap: True

# ========================================
# 地图服务器 (Map Server)
# 负责加载和提供静态地图
# ========================================
map_server:
  ros__parameters:
    use_sim_time: false
    yaml_filename: ""                      # 地图文件路径(由launch文件设置)

# ========================================
# 规划器服务器 (Planner Server)
# 负责全局路径规划
# ========================================
planner_server:
  ros__parameters:
    expected_planner_frequency: 1.0       # 期望规划频率(Hz)
    use_sim_time: false
    planner_plugins: ["GridBased"]        # 规划器插件列表

    # 混合A*规划器：适用于非完整约束机器人
    GridBased:
      plugin: "nav2_smac_planner/SmacPlannerHybrid"
      downsample_costmap: false            # 不降采样代价地图
      downsampling_factor: 1               # 降采样因子
      allow_unknown: true                  # 允许通过未知区域
      max_iterations: 1000000              # 最大迭代次数
      max_on_approach_iterations: 1000     # 接近目标时最大迭代次数
      max_planning_time: 2.0               # 最大规划时间(s)
      motion_model_for_search: "DUBIN"     # 运动模型：Dubin曲线
      cost_travel_multiplier: 2.0          # 行驶代价乘数
      allow_primitive_smoothing: true      # 允许基元平滑
      smooth_path: true                    # 平滑路径
      tolerance: 0.5                       # 规划容忍度(m)
      max_waypoint_distance: 0.2           # 最大路径点距离(m)
      use_final_approach_orientation: true # 使用最终接近朝向

# ========================================
# 平滑器服务器 (Smoother Server)
# 负责路径平滑处理
# ========================================
smoother_server:
  ros__parameters:
    use_sim_time: false
    smoother_plugins: ["simple_smoother"] # 平滑器插件
    simple_smoother:
      plugin: "nav2_smoother::SimpleSmoother"
      tolerance: 1.0e-10                  # 平滑容忍度
      max_its: 1000                       # 最大迭代次数
      do_refinement: True                  # 执行细化

# ========================================
# 行为服务器 (Behavior Server)
# 负责恢复行为(旋转、后退等)
# ========================================
behavior_server:
  ros__parameters:
    costmap_topic: local_costmap/costmap_raw        # 代价地图话题
    footprint_topic: local_costmap/published_footprint # 足迹话题
    cycle_frequency: 10.0                # 循环频率(Hz)

    # 行为插件：定义可用的恢复行为
    behavior_plugins: ["spin", "backup", "drive_on_heading", "wait"]
    spin:
      plugin: "nav2_behaviors/Spin"       # 旋转行为
    backup:
      plugin: "nav2_behaviors/BackUp"     # 后退行为
    drive_on_heading:
      plugin: "nav2_behaviors/DriveOnHeading" # 直线行驶行为
    wait:
      plugin: "nav2_behaviors/Wait"       # 等待行为

    # 坐标系设置
    global_frame: odom                    # 全局坐标系
    robot_base_frame: base_footprint     # 机器人基础坐标系
    transform_tolerance: 0.1             # 变换容忍度(s)
    use_sim_time: false

    # 行为参数
    simulate_ahead_time: 2.0              # 前瞻仿真时间(s)
    max_rotational_vel: 1.0               # 最大旋转速度(rad/s)
    min_rotational_vel: 0.4               # 最小旋转速度(rad/s)
    rotational_acc_lim: 3.2               # 旋转加速度限制(rad/s²)

# ========================================
# 路径点跟随器 (Waypoint Follower)
# 负责多点导航
# ========================================
waypoint_follower:
  ros__parameters:
    loop_rate: 20                         # 循环频率(Hz)
    stop_on_failure: false                # 失败时不停止
    waypoint_task_executor_plugin: "wait_at_waypoint" # 路径点任务执行器
    wait_at_waypoint:
      plugin: "nav2_waypoint_follower::WaitAtWaypoint"
      enabled: True
      waypoint_pause_duration: 0          # 路径点暂停时间(s)

# ========================================
# 速度平滑器 (Velocity Smoother)
# 负责速度命令平滑处理
# ========================================
velocity_smoother:
  ros__parameters:
    use_sim_time: false
    smoothing_frequency: 20.0             # 平滑频率(Hz)
    scale_velocities: false               # 不缩放速度
    feedback: "OPEN_LOOP"                 # 开环反馈

    # 速度限制 [x, y, theta]
    max_velocity: [0.26, 0.0, 1.0]       # 最大速度
    min_velocity: [0.0, 0.0, -1.0]       # 最小速度(禁止倒退)

    # 加速度限制 [x, y, theta]
    max_accel: [2.5, 0.0, 3.2]           # 最大加速度
    max_decel: [-2.5, 0.0, -3.2]         # 最大减速度

    # 里程计设置
    odom_topic: "odom"                    # 里程计话题
    odom_duration: 0.1                    # 里程计持续时间(s)

    # 其他参数
    deadband_velocity: [0.0, 0.0, 0.0]   # 死区速度
    velocity_timeout: 1.0                 # 速度超时时间(s)

# ========================================
# 生命周期管理器 (Lifecycle Manager)
# 负责管理所有Nav2节点的生命周期
# ========================================
lifecycle_manager:
  ros__parameters:
    use_sim_time: false
    autostart: true                       # 自动启动
    # 管理的节点列表
    node_names: ['controller_server', 'smoother_server', 'planner_server',
                 'behavior_server', 'bt_navigator', 'waypoint_follower',
                 'velocity_smoother', 'local_costmap', 'global_costmap']
