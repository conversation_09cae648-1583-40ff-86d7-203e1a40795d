# nav2_params.yaml
# Parameters for the entire Nav2 stack

local_costmap:
  local_costmap:
    ros__parameters:
      update_frequency: 5.0
      publish_frequency: 2.0
      global_frame: odom
      robot_base_frame: base_footprint
      use_sim_time: false
      rolling_window: true
      width: 3
      height: 3
      resolution: 0.05
      robot_radius: 0.325 # From footprint
      plugins: ["obstacle_layer", "inflation_layer"]
      obstacle_layer:
        plugin: "nav2_costmap_2d::ObstacleLayer"
        ros__parameters:
          observation_sources: scan
          scan:
            topic: /scan
            data_type: "LaserScan"
            sensor_frame: laser
            marking: true
            clearing: true
            min_obstacle_height: 0.0
            max_obstacle_height: 0.5
      inflation_layer:
        plugin: "nav2_costmap_2d::InflationLayer"
        ros__parameters:
          cost_scaling_factor: 8.0
          inflation_radius: 0.33
      static_layer:
        plugin: "nav2_costmap_2d::StaticLayer"
        ros__parameters:
          map_subscribe_transient_local: True
global_costmap:
  global_costmap:
    ros__parameters:
      update_frequency: 3.0
      publish_frequency: 3.0
      global_frame: map
      robot_base_frame: base_footprint
      use_sim_time: false
      robot_radius: 0.325 # From footprint
      resolution: 0.05
      track_unknown_space: false
      plugins: ["static_layer", "obstacle_layer", "inflation_layer"]
      obstacle_layer:
        plugin: "nav2_costmap_2d::ObstacleLayer"
        ros__parameters:
          observation_sources: scan
          scan:
            topic: /scan
            data_type: "LaserScan"
            sensor_frame: laser
            marking: true
            clearing: true
            min_obstacle_height: 0.0
            max_obstacle_height: 0.5
      static_layer:
        plugin: "nav2_costmap_2d::StaticLayer"
        ros__parameters:
          map_subscribe_transient_local: True
      inflation_layer:
        plugin: "nav2_costmap_2d::InflationLayer"
        ros__parameters:
          cost_scaling_factor: 8.0
          inflation_radius: 0.33

planner_server:
  ros__parameters:
    expected_planner_frequency: 1.0
    use_sim_time: false
    planner_plugins: ["GridBased"]
    GridBased:
      plugin: "nav2_smac_planner/SmacPlannerHybrid"
      downsample_costmap: false
      downsampling_factor: 1
      allow_unknown: true
      max_iterations: 1000000
      max_on_approach_iterations: 1000
      max_planning_time: 2.0
      motion_model_for_search: "DUBIN"
      cost_travel_multiplier: 2.0
      allow_primitive_smoothing: true
      smoother:
        w_curve: 10.0
        w_dist: 10.0
        w_smooth: 100.0
smoother_server:
  ros__parameters:
    use_sim_time: false
    smoother_plugins: ["simple_smoother"]
    simple_smoother:
      plugin: "nav2_smoother::SimpleSmoother"
      tolerance: 1.0e-10
      max_its: 1000
      do_refinement: True
smoother_server:
  ros__parameters:
    use_sim_time: false
    smoother_plugins: ["simple_smoother"]
    simple_smoother:
      plugin: "nav2_smoother::SimpleSmoother"
      tolerance: 1.0e-10
      max_its: 1000
      do_refinement: True

controller_server:
  ros__parameters:
    use_sim_time: false
    controller_frequency: 10.0
    min_x_velocity: 0.0
    min_y_velocity: 0.0
    min_theta_velocity: 0.0
    failure_tolerance: 5.0
    progress_checker_plugin: "progress_checker"
    goal_checker_plugins: ["goal_checker"]
    controller_plugins: ["FollowPath"]

    progress_checker:
      plugin: "nav2_controller::SimpleProgressChecker"
      ros__parameters:
        required_movement_radius: 0.5
        movement_time_allowance: 10.0
    goal_checker:
      plugin: "nav2_controller::SimpleGoalChecker"
      ros__parameters:
        xy_goal_tolerance: 0.10
        yaw_goal_tolerance: 0.10
        stateful: True
    FollowPath:
      plugin: "dwb_core::DWBLocalPlanner"
      ros__parameters:
        # From base_local_planner_params
        acc_lim_x: 0.8
        acc_lim_theta: 0.6
        max_vel_x: 0.28 # From dwa_params
        min_vel_x: 0.0 # 禁止倒车，只允许前进
        max_vel_y: 0.0
        min_vel_y: 0.0
        max_vel_theta: 0.6 # From dwa_params
        min_vel_theta: -0.6 # From dwa_params

        # From DWA params
        sim_time: 1.2
        vx_samples: 20
        vth_samples: 40

        # DWB specific
        critics: ["RotateToGoal", "Oscillation", "BaseObstacle", "GoalAlign", "PathAlign", "PathDist", "GoalDist"]
        BaseObstacle.scale: 0.02
        PathDist.scale: 32.0
        GoalAlign.scale: 24.0
        PathAlign.scale: 32.0
        GoalDist.scale: 24.0
        RotateToGoal.scale: 32.0
        Oscillation.scale: 0.05

bt_navigator:
  ros__parameters:
    use_sim_time: false
    global_frame: map
    robot_base_frame: base_footprint
    odom_topic: /odom  # 使用EKF融合后的里程计
    bt_loop_duration: 10
    default_server_timeout: 20
    bt_xml_filename: "navigate_to_pose_w_replanning_and_recovery.xml" # Default Nav2 BT
    plugin_lib_names:
      - nav2_compute_path_to_pose_action_bt_node
      - nav2_follow_path_action_bt_node
      - nav2_back_up_action_bt_node
      - nav2_spin_action_bt_node
      - nav2_wait_action_bt_node
      - nav2_clear_costmap_service_bt_node
      - nav2_is_stuck_condition_bt_node
      - nav2_goal_reached_condition_bt_node
      - nav2_goal_updated_condition_bt_node
      - nav2_globally_updated_goal_condition_bt_node
      - nav2_is_path_valid_condition_bt_node
      - nav2_initial_pose_received_condition_bt_node
      - nav2_reinitialize_global_localization_service_bt_node
      - nav2_rate_controller_bt_node
      - nav2_distance_controller_bt_node
      - nav2_speed_controller_bt_node
      - nav2_truncate_path_action_bt_node
      - nav2_goal_updater_node_bt_node
      - nav2_recovery_node_bt_node
      - nav2_pipeline_sequence_bt_node
      - nav2_round_robin_node_bt_node
      - nav2_transform_available_condition_bt_node
      - nav2_time_expired_condition_bt_node
      - nav2_distance_traveled_condition_bt_node
      - nav2_single_trigger_bt_node

behavior_server:
  ros__parameters:
    costmap_topic: local_costmap/costmap_raw
    footprint_topic: local_costmap/published_footprint
    cycle_frequency: 10.0
    behavior_plugins: ["spin", "backup", "drive_on_heading", "assisted_teleop", "wait"]
    spin:
      plugin: "nav2_behaviors/Spin"
    backup:
      plugin: "nav2_behaviors/BackUp"
    drive_on_heading:
      plugin: "nav2_behaviors/DriveOnHeading"
    wait:
      plugin: "nav2_behaviors/Wait"
    assisted_teleop:
      plugin: "nav2_behaviors/AssistedTeleop"
    global_frame: odom
    robot_base_frame: base_footprint
    transform_tolerance: 0.1
    use_sim_time: false
    simulate_ahead_time: 2.0
    max_rotational_vel: 1.0
    min_rotational_vel: 0.4
    rotational_acc_lim: 3.2

waypoint_follower:
  ros__parameters:
    loop_rate: 20
    stop_on_failure: false
    waypoint_task_executor_plugin: "wait_at_waypoint"
    wait_at_waypoint:
      plugin: "nav2_waypoint_follower::WaitAtWaypoint"
      enabled: True
      waypoint_pause_duration: 0

map_server:
  ros__parameters:
    use_sim_time: false
    yaml_filename: ""

map_saver:
  ros__parameters:
    use_sim_time: false
    save_map_timeout: 5.0
    free_thresh_default: 0.25
    occupied_thresh_default: 0.65
    map_subscribe_transient_local: true

# AMCL removed - using Cartographer for localization

lifecycle_manager:
  ros__parameters:
    use_sim_time: false
    autostart: true
    node_names: ['controller_server', 'smoother_server', 'planner_server', 'behavior_server', 'bt_navigator', 'waypoint_follower', 'velocity_smoother']

velocity_smoother:
  ros__parameters:
    use_sim_time: false
    smoothing_frequency: 20.0
    scale_velocities: false
    feedback: "OPEN_LOOP"
    max_velocity: [0.26, 0.0, 1.0]
    min_velocity: [-0.26, 0.0, -1.0]
    max_accel: [2.5, 0.0, 3.2]
    max_decel: [-2.5, 0.0, -3.2]
    odom_topic: "odom"
    odom_duration: 0.1
    deadband_velocity: [0.0, 0.0, 0.0]
    velocity_timeout: 1.0

# Defaults for all nodes
default_nav_to_pose_bt_xml: "navigate_w_replanning_and_recovery.xml"
default_nav_through_poses_bt_xml: "navigate_through_poses_w_replanning_and_recovery.xml"
