cmake_minimum_required(VERSION 3.8)
project(robotcar_nav)

if(CMAKE_COMPILER_IS_GNUCXX OR CMAKE_CXX_COMPILER_ID MATCHES "Clang")
  add_compile_options(-Wall -Wextra -Wpedantic)
endif()

# find dependencies
find_package(ament_cmake REQUIRED)
find_package(rclcpp REQUIRED)
find_package(rclcpp_action REQUIRED)
find_package(robotcar_base REQUIRED)
find_package(nav2_msgs REQUIRED)
find_package(apriltag_msgs REQUIRED)
find_package(geometry_msgs REQUIRED)
find_package(tf2 REQUIRED)
find_package(tf2_ros REQUIRED)
find_package(tf2_geometry_msgs REQUIRED)
find_package(sensor_msgs REQUIRED)
find_package(std_srvs REQUIRED)
find_package(std_msgs REQUIRED)
find_package(message_filters REQUIRED)

# room_scheduling_node commented out - not needed for basic navigation
# add_executable(room_scheduling_node src/room_scheduling_node.cpp)
# ament_target_dependencies(room_scheduling_node rclcpp rclcpp_action robotcar_base nav2_msgs)

# auto_charge_node is commented out because src/auto_charge.cpp doesn't exist
# add_executable(auto_charge_node src/auto_charge.cpp)
# ament_target_dependencies(auto_charge_node
#   rclcpp
#   apriltag_msgs
#   geometry_msgs
#   tf2_ros
#   tf2_geometry_msgs
# )

# add_executable(odom_tf_publisher src/car_odom_tf.cpp)
# ament_target_dependencies(odom_tf_publisher
#   rclcpp
#   robotcar_base
#   sensor_msgs
#   nav_msgs
#   tf2_ros
#   tf2_geometry_msgs
# )

# add_executable(car_serial_node src/car_serial.cpp)
# ament_target_dependencies(car_serial_node
#   rclcpp
#   robotcar_base
#   geometry_msgs
# )

# All executable nodes removed - only keeping launch files for mapping and navigation

# voice_control_node commented out - not needed for basic navigation
# add_executable(voice_control_node src/voice_test.cpp)
# ament_target_dependencies(voice_control_node
#   rclcpp
#   std_msgs
#   robotcar_base
# )

# sensor_sync_node commented out - not needed for basic navigation
# add_executable(sensor_sync_node src/sensor_sync_node.cpp)
# ament_target_dependencies(sensor_sync_node
#   rclcpp
#   sensor_msgs
#   robotcar_base
#   message_filters
# )

# No executable targets to install - only launch files and configs

install(
  DIRECTORY launch
  DESTINATION share/${PROJECT_NAME}
)

install(
  DIRECTORY config
  DESTINATION share/${PROJECT_NAME}
)

install(
  DIRECTORY maps
  DESTINATION share/${PROJECT_NAME}
)



ament_package()