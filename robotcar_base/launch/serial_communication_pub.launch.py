from launch import LaunchDescription
from launch_ros.actions import Node

def generate_launch_description():
    """
    ROS2 equivalent of serial_communication_pub.launch.
    Launches IMU node for HWT901B-TTL sensor.
    """
    return LaunchDescription([
        Node(
            package='robotcar_base',
            executable='imu_node.py',      # The new ROS2 python script
            name='wit_normal_ros',         # Keep original node name for consistency
            output='screen',
            parameters=[{
                'port': '/dev/imu_usb',
                'baud': 115200,              # 正确的IMU波特率
                'imu_topic': '/imu',       # 修正话题名以匹配EKF配置
                'mag_topic': '/mag',       # Magnetometer topic
                'frame_id': 'imu_link'     # Frame ID
            }]
        ),
    ])