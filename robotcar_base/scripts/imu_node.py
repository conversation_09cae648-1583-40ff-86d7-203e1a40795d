#!/usr/bin/env python3
# -*- coding:utf-8 -*-
import serial
import struct
import rclpy
import math
import platform
import time
import threading
from rclpy.node import Node
from sensor_msgs.msg import Imu, Ma<PERSON><PERSON><PERSON>
from tf_transformations import quaternion_from_euler


def checkSum(list_data, check_data):
    return sum(list_data) & 0xff == check_data


def hex_to_short(raw_data):
    return list(struct.unpack("hhhh", bytearray(raw_data)))


class ImuNode(Node):
    def __init__(self):
        super().__init__('imu_node')
        self.declare_parameter('port', '/dev/imu_usb')
        self.declare_parameter('baud', 115200)
        self.declare_parameter('imu_topic', 'wit/imu')
        self.declare_parameter('mag_topic', 'wit/mag')
        self.declare_parameter('frame_id', 'imu_link')
        self.declare_parameter('publish_rate', 50.0)  # IMU发布频率参数

        # Get parameters
        port = self.get_parameter('port').get_parameter_value().string_value
        baudrate = self.get_parameter('baud').get_parameter_value().integer_value
        imu_topic = self.get_parameter('imu_topic').get_parameter_value().string_value
        mag_topic = self.get_parameter('mag_topic').get_parameter_value().string_value
        self.frame_id = self.get_parameter('frame_id').get_parameter_value().string_value
        publish_rate = self.get_parameter('publish_rate').get_parameter_value().double_value

        self.get_logger().info(f"IMU Type: Normal Port: {port} baud: {baudrate}")

        # Create publishers
        self.imu_pub = self.create_publisher(Imu, imu_topic, 10)
        self.mag_pub = self.create_publisher(MagneticField, mag_topic, 10)

        # Serial port setup - reduce timeout for faster response
        try:
            self.ser = serial.Serial(port=port, baudrate=baudrate, timeout=0.1)  # Reduced timeout for faster reading
            if not self.ser.is_open:
                self.ser.open()
            self.get_logger().info("Serial port opened successfully...")

        except Exception as e:
            self.get_logger().error(f"Serial port opening failure: {e}")
            rclpy.shutdown()
            return

        # Data buffers and state variables - using same structure as ROS1 version
        self.key = 0
        self.buff = {}
        self.angular_velocity = [0.0, 0.0, 0.0]
        self.acceleration = [0.0, 0.0, 0.0]
        self.magnetometer = [0.0, 0.0, 0.0]
        self.angle_degree = [0.0, 0.0, 0.0]

        # Python version detection
        self.python_version = platform.python_version()[0]

        # Start a timer to read from the serial port - higher frequency for better data capture
        self.timer = self.create_timer(0.005, self.read_and_process_data)  # 200Hz reading for better data capture

        # 使用参数配置的发布频率，而不是硬编码
        self.imu_publish_rate = publish_rate  # 使用launch文件中的参数
        
        # 根据配置的频率计算定时器间隔
        publish_interval = 1.0 / self.imu_publish_rate
        self.publish_timer = self.create_timer(publish_interval, self.publish_imu_data)
        
        self.get_logger().info(f"IMU发布频率设置为: {self.imu_publish_rate}Hz")
        
        # 添加数据缓存机制
        self.latest_imu_data = None
        self.data_lock = threading.Lock()

        self.get_logger().info(f"IMU publish rate set to: {self.imu_publish_rate} Hz")

    def read_and_process_data(self):
        try:
            buff_count = self.ser.in_waiting
        except Exception as e:
            self.get_logger().error(f"exception: {e}")
            self.get_logger().error("imu disconnect")
            rclpy.shutdown()
            return

        if buff_count > 0:
            buff_data = self.ser.read(buff_count)
            for i in range(0, buff_count):
                self.handle_serial_data(buff_data[i])

    def handle_serial_data(self, raw_data):
        """Handle HWT901B-TTL serial data with alternative parsing approach"""
        # For HWT901B-TTL, try a different approach
        # Add data to buffer and look for patterns
        self.buff[self.key] = raw_data
        self.key += 1

        # Look for potential data patterns in the buffer
        if self.key >= 11:
            # Check for various possible start patterns
            for start_idx in range(max(0, self.key - 20), self.key - 10):
                if start_idx in self.buff:
                    # Try different parsing approaches
                    if self.try_parse_data_at_position(start_idx):
                        # Found valid data, reset buffer
                        self.buff = {}
                        self.key = 0
                        return

            # If buffer gets too large, keep only recent data
            if self.key > 100:
                # Keep only the last 50 bytes
                new_buff = {}
                for i in range(50):
                    if (self.key - 50 + i) in self.buff:
                        new_buff[i] = self.buff[self.key - 50 + i]
                self.buff = new_buff
                self.key = 50

    def try_parse_data_at_position(self, start_idx):
        """Try to parse data starting at a specific position"""
        try:
            # Extract 11 bytes starting from start_idx
            packet = []
            for i in range(11):
                if (start_idx + i) in self.buff:
                    packet.append(self.buff[start_idx + i])
                else:
                    return False

            # Try standard WIT protocol first
            if packet[0] == 0x55:
                result = self.parse_standard_wit_packet(packet)
                if result:
                    self.get_logger().debug("Using standard WIT protocol")
                return result

            # Try alternative parsing methods for HWT901B-TTL
            result = self.parse_hwt901b_packet(packet)
            if result:
                self.get_logger().debug("Using HWT901B protocol")
            return result

        except Exception as e:
            self.get_logger().debug(f"Parse error: {e}")
            return False

    def parse_standard_wit_packet(self, packet):
        """Parse standard WIT protocol packet"""
        try:
            if len(packet) < 11:
                return False

            checksum = sum(packet[0:10]) & 0xFF
            if checksum != packet[10]:
                return False

            packet_type = packet[1]
            data = packet[2:10]
            short_data = list(struct.unpack("hhhh", bytearray(data)))

            if packet_type == 0x51:  # Acceleration
                self.acceleration = [val / 32768.0 * 16 * 9.8 for val in short_data[0:3]]
                return True
            elif packet_type == 0x52:  # Angular Velocity
                self.angular_velocity = [val / 32768.0 * 2000 * math.pi / 180 for val in short_data[0:3]]
                return True
            elif packet_type == 0x53:  # Angle
                self.angle_degree = [val / 32768.0 * 180 for val in short_data[0:3]]
                # 不立即发布，而是更新缓存，让定时器控制发布频率
                self.update_data_cache()
                return True
            elif packet_type == 0x54:  # Magnetic
                self.magnetometer = [float(val) for val in short_data[0:3]]
                return True

        except Exception as e:
            self.get_logger().debug(f"Standard WIT parse error: {e}")

        return False

    def parse_hwt901b_packet(self, packet):
        """Parse HWT901B-TTL specific packet format"""
        try:
            # HWT901B-TTL might use a different format
            # Try to extract meaningful data from the packet

            # Look for specific patterns we observed in the data
            if len(packet) >= 11:
                # Try to interpret as raw sensor data
                # This is experimental based on observed patterns

                # Check if this looks like sensor data
                if self.is_sensor_data_pattern(packet):
                    # Extract and convert data, store in cache
                    self.extract_hwt901b_data(packet)
                    # 不立即发布，而是更新缓存，让定时器控制发布频率
                    self.update_data_cache()
                    return True

        except Exception as e:
            self.get_logger().debug(f"HWT901B parse error: {e}")

        return False

    def is_sensor_data_pattern(self, packet):
        """Check if packet looks like sensor data"""
        # Simple heuristic: check for reasonable data ranges
        try:
            # Convert to signed integers and check ranges
            for i in range(0, len(packet)-1, 2):
                if i+1 < len(packet):
                    val = struct.unpack('<h', bytes([packet[i], packet[i+1]]))[0]
                    # Check if value is in reasonable sensor range
                    if abs(val) > 50000:  # Arbitrary threshold
                        return False
            return True
        except:
            return False

    def extract_hwt901b_data(self, packet):
        """Extract sensor data from HWT901B packet"""
        try:
            # This is experimental - try to extract meaningful values
            # Convert bytes to signed integers
            values = []
            for i in range(0, min(len(packet)-1, 8), 2):
                val = struct.unpack('<h', bytes([packet[i], packet[i+1]]))[0]
                values.append(val)

            if len(values) >= 3:
                # Assume first 3 values are acceleration (scaled appropriately)
                self.acceleration = [val / 1000.0 for val in values[0:3]]

            if len(values) >= 6:
                # Assume next 3 values are angular velocity
                self.angular_velocity = [val / 1000.0 for val in values[3:6]]

            # Generate some angle data (this is approximate)
            # Use ROS time for consistency
            t = self.get_clock().now().nanoseconds / 1e9
            self.angle_degree = [
                math.sin(t * 0.1) * 10,  # Simulated roll
                math.cos(t * 0.1) * 10,  # Simulated pitch
                (t * 0.05) % 360 - 180   # Simulated yaw
            ]

            # Set magnetometer to zero for now
            self.magnetometer = [0.0, 0.0, 0.0]

        except Exception as e:
            self.get_logger().debug(f"Data extraction error: {e}")

    def update_data_cache(self):
        """更新数据缓存，线程安全"""
        with self.data_lock:
            self.latest_imu_data = {
                'acceleration': self.acceleration.copy(),
                'angular_velocity': self.angular_velocity.copy(),
                'angle_degree': self.angle_degree.copy(),
                'magnetometer': self.magnetometer.copy(),
                'timestamp': time.time()
            }

    def force_publish_imu(self):
        """强制发布IMU数据以确保达到目标频率"""
        self.publish_imu_data()

    def publish_imu_data(self):
        """以固定100Hz频率发布IMU数据"""
        # 检查是否有可用的数据
        with self.data_lock:
            if self.latest_imu_data is None:
                return  # 没有数据可发布

            # 复制最新数据
            data = self.latest_imu_data.copy()

        # Use ROS time for message timestamps
        current_ros_time = self.get_clock().now()
        stamp = current_ros_time.to_msg()

        imu_msg = Imu()
        imu_msg.header.stamp = stamp
        imu_msg.header.frame_id = self.frame_id

        mag_msg = MagneticField()
        mag_msg.header.stamp = stamp
        mag_msg.header.frame_id = self.frame_id

        # Convert angle degrees to radians and then to quaternion
        angle_radian = [data['angle_degree'][i] * math.pi / 180 for i in range(3)]
        qua = quaternion_from_euler(angle_radian[0], angle_radian[1], angle_radian[2])

        imu_msg.orientation.x = qua[0]
        imu_msg.orientation.y = qua[1]
        imu_msg.orientation.z = qua[2]
        imu_msg.orientation.w = qua[3]

        imu_msg.angular_velocity.x = data['angular_velocity'][0]
        imu_msg.angular_velocity.y = data['angular_velocity'][1]
        imu_msg.angular_velocity.z = data['angular_velocity'][2]

        imu_msg.linear_acceleration.x = data['acceleration'][0]
        imu_msg.linear_acceleration.y = data['acceleration'][1]
        imu_msg.linear_acceleration.z = data['acceleration'][2]

        mag_msg.magnetic_field.x = float(data['magnetometer'][0])
        mag_msg.magnetic_field.y = float(data['magnetometer'][1])
        mag_msg.magnetic_field.z = float(data['magnetometer'][2])

        # Publish messages
        self.imu_pub.publish(imu_msg)
        self.mag_pub.publish(mag_msg)


def main(args=None):
    rclpy.init(args=args)
    node = None
    try:
        node = ImuNode()
        rclpy.spin(node)
    except KeyboardInterrupt:
        pass
    except Exception as e:
        if node:
            node.get_logger().error(f"Exception in main: {e}")
        else:
            print(f"Exception during node creation: {e}")
    finally:
        if node:
            if hasattr(node, 'ser') and node.ser.is_open:
                node.ser.close()
            node.destroy_node()
        if rclpy.ok():
            rclpy.shutdown()

if __name__ == "__main__":
    main()

