controller_manager:
  ros__parameters:
    update_rate: 50  # 降低到合理的频率，避免TF跳动

    diff_drive_controller:
      type: diff_drive_controller/DiffDriveController

    joint_state_broadcaster:
      type: joint_state_broadcaster/JointStateBroadcaster

diff_drive_controller:
  ros__parameters:
    left_wheel_names: ["left_wheel_joint"]
    right_wheel_names: ["right_wheel_joint"]
    wheel_separation: 0.34  # 请实际测量！左右轮中心距离
    wheel_radius: 0.06      # 请实际测量！车轮实际半径
    # 校准因子 - 如果里程计有系统误差，可以调整这些值
    wheel_separation_multiplier: 1.0  # 如果转弯过度则增加，转弯不足则减少
    left_wheel_radius_multiplier: 1.0   # 如果左偏则增加，右偏则减少
    right_wheel_radius_multiplier: 1.0  # 如果右偏则增加，左偏则减少
    publish_rate: 50.0   # 设置为50Hz，与controller_manager同步
    odom_frame_id: odom
    base_frame_id: base_footprint
    use_stamped_vel: false
    enable_odom_tf: false  # 禁用diff_drive_controller的TF，让EKF接管TF发布

    # Odometry settings
    odom_only_twist: false  # 使用完整的odom信息
    position_feedback: true
    velocity_feedback: true


joint_state_broadcaster:
  ros__parameters:
    {} 