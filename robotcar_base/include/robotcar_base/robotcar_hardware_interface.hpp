#ifndef ROBOTCAR_HARDWARE_INTERFACE_HPP
#define ROBOTCAR_HARDWARE_INTERFACE_HPP

#include <string>
#include <vector>
#include <atomic>
#include <libserial/SerialPort.h>

#include "hardware_interface/system_interface.hpp"
#include "hardware_interface/handle.hpp"
#include "hardware_interface/hardware_info.hpp"
#include "hardware_interface/types/hardware_interface_return_values.hpp"
#include "rclcpp/macros.hpp"
#include "rclcpp/rclcpp.hpp"
#include "robotcar_base/visibility_control.h"
#include "robotcar_base/msg/car_info.hpp"
#include "robotcar_base/srv/set_control_mode.hpp"

namespace robotcar_base
{

class RobotCarHardwareInterface : public hardware_interface::SystemInterface
{
public:
    RCLCPP_SHARED_PTR_DEFINITIONS(RobotCarHardwareInterface)

    ROBOTCAR_BASE_PUBLIC
    hardware_interface::CallbackReturn on_init(const hardware_interface::HardwareInfo & info) override; // 初始化硬件接口 主要用来传参

    ROBOTCAR_BASE_PUBLIC
    std::vector<hardware_interface::StateInterface> export_state_interfaces() override; // 导出状态接口 主要用来读取硬件状态

    ROBOTCAR_BASE_PUBLIC
    std::vector<hardware_interface::CommandInterface> export_command_interfaces() override; // 导出命令接口 主要用来写入硬件命令

    ROBOTCAR_BASE_PUBLIC
    hardware_interface::CallbackReturn on_activate(const rclcpp_lifecycle::State & previous_state) override; // 激活硬件接口

    ROBOTCAR_BASE_PUBLIC
    hardware_interface::CallbackReturn on_deactivate(const rclcpp_lifecycle::State & previous_state) override; // 禁用硬件接口

    ROBOTCAR_BASE_PUBLIC
    hardware_interface::return_type read(const rclcpp::Time & time, const rclcpp::Duration & period) override; // 读取硬件状态

    ROBOTCAR_BASE_PUBLIC
    hardware_interface::return_type write(const rclcpp::Time & time, const rclcpp::Duration & period) override; // 写入硬件命令

private:
    // Communication with hardware
    LibSerial::SerialPort serial_port_;

    // Node for publishing and services
    rclcpp::Node::SharedPtr non_realtime_node_;
    rclcpp::Publisher<robotcar_base::msg::CarInfo>::SharedPtr car_info_pub_;
    rclcpp::Service<robotcar_base::srv::SetControlMode>::SharedPtr mode_service_;
    std::thread node_thread_;

    // Hardware parameters
    std::string serial_port_name_;
    int serial_baud_rate_;

    // Wheel joint states
    double hw_position_left_;
    double hw_velocity_left_;
    double hw_position_right_;
    double hw_velocity_right_;

    // Wheel joint commands
    double hw_command_velocity_left_;
    double hw_command_velocity_right_;

    // Mode commands
    std::atomic<uint16_t> hw_mode1_;
    std::atomic<uint16_t> hw_mode2_;

    // Wheel parameters from URDF
    double wheel_radius_;
    double wheel_separation_;
};

}  // namespace robotcar_base

#endif  // ROBOTCAR_HARDWARE_INTERFACE_HPP