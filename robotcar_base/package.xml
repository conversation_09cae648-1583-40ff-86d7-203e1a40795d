<?xml version="1.0"?>
<?xml-model href="http://download.ros.org/schema/package_format3.xsd" schematypens="http://www.w3.org/2001/XMLSchema"?>
<package format="3">
  <name>robotcar_base</name>
  <version>0.0.0</version>
  <description>RobotCar base package including hardware interface, URDF, and messages for ROS2</description>
  <maintainer email="<EMAIL>">user</maintainer>
  <license>Apache-2.0</license>

  <buildtool_depend>ament_cmake</buildtool_depend>

  <depend>rclcpp</depend>
  <depend>rclcpp_lifecycle</depend>
  <depend>hardware_interface</depend>
  <depend>pluginlib</depend>
  <depend>ros2_control</depend>
  <depend>controller_manager</depend>
  <depend>diff_drive_controller</depend>
  <depend>joint_state_broadcaster</depend>
  <depend>robot_state_publisher</depend>
  <depend>sensor_msgs</depend>
  <depend>xacro</depend>
  <depend>rclpy</depend>
  <depend>tf_transformations</depend>
  <depend>python3-serial</depend>
  
  <build_depend>rosidl_default_generators</build_depend>
  <exec_depend>rosidl_default_runtime</exec_depend>
  <member_of_group>rosidl_interface_packages</member_of_group>
  
  <depend>libserial-dev</depend>
  
  <test_depend>ament_lint_auto</test_depend>
  <test_depend>ament_lint_common</test_depend>

  <export>
    <build_type>ament_cmake</build_type>
  </export>
</package> 