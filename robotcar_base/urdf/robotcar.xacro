<?xml version="1.0"?>
<robot name="agrobot_2d" xmlns:xacro="http://www.ros.org/wiki/xacro">

  <xacro:property name="orange_rgba" value="1.0 0.5 0.2 1" />
  <xacro:property name="gray_rgba" value="0.2 0.2 0.2 1" />
  <xacro:property name="blue_rgba" value="0.1 0.1 1.0 0.3" />
  <xacro:property name="black_rgba" value="0.0 0.0 0.0 0.5" />
  <xacro:property name="red_rgba" value="1 0 0 1" />
  <xacro:property name="green_rgba" value="0 1 0 0.5" />

  <xacro:property name="M_PI" value="3.14159265359" />

  <xacro:property name="imu_box_x" value="0.06" />
  <xacro:property name="imu_box_y" value="0.04" />
  <xacro:property name="imu_box_z" value="0.02" />

  <xacro:property name="laser_cylinder_length" value="0.05" />
  <xacro:property name="laser_cylinder_radius" value="0.03" />

  <xacro:property name="base_box_x" value="0.65" />
  <xacro:property name="base_box_y" value="0.5" />
  <xacro:property name="base_box_z" value="0.25" />
  <xacro:property name="base_link_z_offset" value="0.085" /> 

  <xacro:property name="wheel_length" value="0.045" />
  <xacro:property name="wheel_radius_vis" value="0.085" /> <xacro:property name="wheel_offset_y" value="0.2" /> 
  <xacro:property name="wheel_offset_z" value="-0.085" /> <xacro:property name="imu_offset_x" value="0" />
  <xacro:property name="imu_offset_y" value="0" />
  <xacro:property name="imu_offset_z" value="0" />

  <xacro:property name="laser_offset_x" value="0.2765" />
  <xacro:property name="laser_offset_y" value="0" />
  <xacro:property name="laser_offset_z" value="0.06" />


  <xacro:property name="ctrl_wheel_radius" value="0.06" /> <xacro:property name="ctrl_wheel_separation" value="0.34" /> <xacro:property name="serial_port_name" value="/dev/STM32" />
  <xacro:property name="serial_baud_rate" value="115200" />


  <material name="orange">
    <color rgba="${orange_rgba}" />
  </material>
  <material name="gray">
    <color rgba="${gray_rgba}" />
  </material>
  <material name="blue">
    <color rgba="${blue_rgba}" />
  </material>
  <material name="black">
    <color rgba="${black_rgba}" />
  </material>
  <material name="red">
    <color rgba="${red_rgba}" />
  </material>
  <material name="green">
    <color rgba="${green_rgba}" />
  </material>

  <xacro:macro name="wheel_link" params="name parent_link x_offset y_offset z_offset">
    <link name="${name}_wheel_link">
      <visual>
        <origin xyz="0 0 0" rpy="${M_PI/2} 0 0"/> <geometry>
          <cylinder length="${wheel_length}" radius="${wheel_radius_vis}"/>
        </geometry>
        <material name="black" />
      </visual>
      <collision>
        <origin xyz="0 0 0" rpy="${M_PI/2} 0 0"/>
        <geometry>
          <cylinder length="${wheel_length}" radius="${wheel_radius_vis}"/>
        </geometry>
      </collision>
      <inertial>
        <mass value="0.2"/>
        <inertia ixx="0.000190416666667" ixy="0" ixz="0" iyy="0.0001904" iyz="0" izz="0.00036"/>
      </inertial>
    </link>

    <joint name="${name}_wheel_joint" type="continuous">
      <parent link="${parent_link}" />
      <child link="${name}_wheel_link" />
      <origin xyz="${x_offset} ${y_offset} ${z_offset}" />
      <axis xyz="0 1 0" />
    </joint>
  </xacro:macro>

  <xacro:macro name="caster_link" params="name parent_link x_offset y_offset z_offset">
    <link name="${name}_caster_link">
      <visual>
        <origin xyz="0 0 0" rpy="${M_PI/2} 0 0"/>
        <geometry>
          <cylinder length="0.02" radius="0.0315"/>
        </geometry>
        <material name="black" />
      </visual>
      <collision>
        <origin xyz="0 0 0" rpy="${M_PI/2} 0 0"/>
        <geometry>
          <cylinder length="0.02" radius="0.0315"/>
        </geometry>
      </collision>
      <inertial>
        <mass value="0.02"/>
        <inertia ixx="0.000190416666667" ixy="0" ixz="0" iyy="0.0001904" iyz="0" izz="0.00036"/>
      </inertial>
    </link>

    <joint name="${name}_caster_joint" type="fixed">
      <parent link="${parent_link}" />
      <child link="${name}_caster_link" />
      <origin xyz="${x_offset} ${y_offset} ${z_offset}" />
      <axis xyz="0 0 0"/> </joint>
  </xacro:macro>

  <link name="base_footprint"/>
  
  <joint name="base_joint" type="fixed">
    <parent link="base_footprint"/>
    <child link="base_link"/>
    <origin xyz="0 0 ${base_link_z_offset}" rpy="0 0 0"/>
  </joint>
  
  <link name="base_link">
    <visual>
      <geometry>
        <box size="${base_box_x} ${base_box_y} ${base_box_z}"/>
      </geometry>
      <origin rpy="0 0 0" xyz="0 0 0"/> <material name="blue"> 
        <color rgba="${blue_rgba}"/> 
      </material>
    </visual>
    <collision>
      <geometry>
        <box size="${base_box_x} ${base_box_y} ${base_box_z}"/>
      </geometry>
      <origin rpy="0 0 0" xyz="0 0 0"/>
    </collision>
    <inertial>
      <mass value="40"/>
      <inertia ixx="1.54" ixy="0" ixz="0" iyy="1.73" iyz="0" izz="1.25"/>
    </inertial>
  </link>
  
  <link name="laser">
    <visual>
      <geometry>
        <cylinder length="${laser_cylinder_length}" radius="${laser_cylinder_radius}"/>
      </geometry>
      <material name="red">
        <color rgba="${red_rgba}"/>
      </material>
    </visual>
    <collision>
      <geometry>
        <cylinder length="${laser_cylinder_length}" radius="${laser_cylinder_radius}"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="0.1"/>
      <inertia ixx="0.000190416666667" ixy="0" ixz="0" iyy="0.0001904" iyz="0" izz="0.00036"/>
    </inertial>
  </link>
  
  <joint name="laser_joint" type="fixed">
    <parent link="base_link" />
    <child link="laser" />
    <origin xyz="${laser_offset_x} ${laser_offset_y} ${laser_offset_z}" rpy="0 0 0" />
  </joint>
    
  <link name="imu_link">
    <visual>
      <geometry>
        <box size="${imu_box_x} ${imu_box_y} ${imu_box_z}"/>
      </geometry>
      <material name="green">
        <color rgba="${green_rgba}"/>
      </material>
    </visual>
    <collision>
      <geometry>
        <box size="${imu_box_x} ${imu_box_y} ${imu_box_z}"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="0.01"/>
      <inertia ixx="1.6e-6" ixy="0" ixz="0" iyy="1.6e-6" iyz="0" izz="1.6e-6"/>
    </inertial>
  </link>

  <joint name="imu_joint" type="fixed">
    <parent link="base_link"/>
    <child link="imu_link"/>
    <origin xyz="${imu_offset_x} ${imu_offset_y} ${imu_offset_z}" rpy="0 0 0"/>
  </joint>
    
  <xacro:wheel_link name="right" parent_link="base_link" x_offset="0" y_offset="-${wheel_offset_y}" z_offset="${wheel_offset_z}"/>
  <xacro:wheel_link name="left" parent_link="base_link" x_offset="0" y_offset="${wheel_offset_y}" z_offset="${wheel_offset_z}"/>

  <xacro:caster_link name="left" parent_link="base_link" x_offset="0.25" y_offset="0.1435" z_offset="-0.13"/>
  <xacro:caster_link name="right" parent_link="base_link" x_offset="0.25" y_offset="-0.1435" z_offset="-0.13"/>
    
  <ros2_control name="RobotCar" type="system">
    <hardware>
      <plugin>robotcar_base/RobotCarHardwareInterface</plugin>

      <param name="serial_port_name">${serial_port_name}</param>
      <param name="serial_baud_rate">${serial_baud_rate}</param>
      <param name="wheel_radius">${ctrl_wheel_radius}</param>
      <param name="wheel_separation">${ctrl_wheel_separation}</param>
    </hardware>
    <joint name="left_wheel_joint">
      <command_interface name="velocity"/>
      <state_interface name="position"/>
      <state_interface name="velocity"/>
    </joint>
    <joint name="right_wheel_joint">
      <command_interface name="velocity"/>
      <state_interface name="position"/>
      <state_interface name="velocity"/>
    </joint>
  </ros2_control>
    
  </robot>