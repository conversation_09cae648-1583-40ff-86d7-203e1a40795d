<?xml version="1.0" ?>
<!-- =================================================================================== -->
<!-- |    This document was autogenerated by xacro from /home/<USER>/test_ws/src/robotcar_base/urdf/robotcar.xacro | -->
<!-- |    EDITING THIS FILE BY HAND IS NOT RECOMMENDED                                 | -->
<!-- =================================================================================== -->
<robot name="agrobot_2d">
  <material name="orange">
    <color rgba="1.0 0.5 0.2 1"/>
  </material>
  <material name="gray">
    <color rgba="0.2 0.2 0.2 1"/>
  </material>
  <material name="blue">
    <color rgba="0.1 0.1 1.0 0.3"/>
  </material>
  <material name="black">
    <color rgba="0.0 0.0 0.0 0.5"/>
  </material>
  <material name="red">
    <color rgba="1 0 0 1"/>
  </material>
  <material name="green">
    <color rgba="0 1 0 0.5"/>
  </material>
  <link name="base_footprint"/>
  <joint name="base_joint" type="fixed">
    <parent link="base_footprint"/>
    <child link="base_link"/>
    <origin rpy="0 0 0" xyz="0 0 0.085"/>
  </joint>
  <link name="base_link">
    <visual>
      <geometry>
        <box size="0.65 0.5 0.25"/>
      </geometry>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <material name="blue">
        <color rgba="0.1 0.1 1.0 0.3"/>
      </material>
    </visual>
    <collision>
      <geometry>
        <box size="0.65 0.5 0.25"/>
      </geometry>
      <origin rpy="0 0 0" xyz="0 0 0"/>
    </collision>
    <inertial>
      <mass value="40"/>
      <inertia ixx="1.54" ixy="0" ixz="0" iyy="1.73" iyz="0" izz="1.25"/>
    </inertial>
  </link>
  <link name="laser">
    <visual>
      <geometry>
        <cylinder length="0.05" radius="0.03"/>
      </geometry>
      <material name="red">
        <color rgba="1 0 0 1"/>
      </material>
    </visual>
    <collision>
      <geometry>
        <cylinder length="0.05" radius="0.03"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="0.1"/>
      <inertia ixx="0.000190416666667" ixy="0" ixz="0" iyy="0.0001904" iyz="0" izz="0.00036"/>
    </inertial>
  </link>
  <joint name="laser_joint" type="fixed">
    <parent link="base_link"/>
    <child link="laser"/>
    <origin rpy="0 0 0" xyz="0.2765 0 0.06"/>
  </joint>
  <link name="imu_link">
    <visual>
      <geometry>
        <box size="0.06 0.04 0.02"/>
      </geometry>
      <material name="green">
        <color rgba="0 1 0 0.5"/>
      </material>
    </visual>
    <collision>
      <geometry>
        <box size="0.06 0.04 0.02"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="0.01"/>
      <inertia ixx="1.6e-6" ixy="0" ixz="0" iyy="1.6e-6" iyz="0" izz="1.6e-6"/>
    </inertial>
  </link>
  <joint name="imu_joint" type="fixed">
    <parent link="base_link"/>
    <child link="imu_link"/>
    <origin rpy="0 0 0" xyz="0 0 0"/>
  </joint>
  <link name="right_wheel_link">
    <visual>
      <origin rpy="1.570796326795 0 0" xyz="0 0 0"/>
      <geometry>
        <cylinder length="0.045" radius="0.085"/>
      </geometry>
      <material name="black"/>
    </visual>
    <collision>
      <origin rpy="1.570796326795 0 0" xyz="0 0 0"/>
      <geometry>
        <cylinder length="0.045" radius="0.085"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="0.2"/>
      <inertia ixx="0.000190416666667" ixy="0" ixz="0" iyy="0.0001904" iyz="0" izz="0.00036"/>
    </inertial>
  </link>
  <joint name="right_wheel_joint" type="continuous">
    <parent link="base_link"/>
    <child link="right_wheel_link"/>
    <origin xyz="0 -0.2 -0.085"/>
    <axis xyz="0 1 0"/>
  </joint>
  <link name="left_wheel_link">
    <visual>
      <origin rpy="1.570796326795 0 0" xyz="0 0 0"/>
      <geometry>
        <cylinder length="0.045" radius="0.085"/>
      </geometry>
      <material name="black"/>
    </visual>
    <collision>
      <origin rpy="1.570796326795 0 0" xyz="0 0 0"/>
      <geometry>
        <cylinder length="0.045" radius="0.085"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="0.2"/>
      <inertia ixx="0.000190416666667" ixy="0" ixz="0" iyy="0.0001904" iyz="0" izz="0.00036"/>
    </inertial>
  </link>
  <joint name="left_wheel_joint" type="continuous">
    <parent link="base_link"/>
    <child link="left_wheel_link"/>
    <origin xyz="0 0.2 -0.085"/>
    <axis xyz="0 1 0"/>
  </joint>
  <link name="left_caster_link">
    <visual>
      <origin rpy="1.570796326795 0 0" xyz="0 0 0"/>
      <geometry>
        <cylinder length="0.02" radius="0.0315"/>
      </geometry>
      <material name="black"/>
    </visual>
    <collision>
      <origin rpy="1.570796326795 0 0" xyz="0 0 0"/>
      <geometry>
        <cylinder length="0.02" radius="0.0315"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="0.02"/>
      <inertia ixx="0.000190416666667" ixy="0" ixz="0" iyy="0.0001904" iyz="0" izz="0.00036"/>
    </inertial>
  </link>
  <joint name="left_caster_joint" type="fixed">
    <parent link="base_link"/>
    <child link="left_caster_link"/>
    <origin xyz="0.25 0.1435 -0.13"/>
    <axis xyz="0 0 0"/>
  </joint>
  <link name="right_caster_link">
    <visual>
      <origin rpy="1.570796326795 0 0" xyz="0 0 0"/>
      <geometry>
        <cylinder length="0.02" radius="0.0315"/>
      </geometry>
      <material name="black"/>
    </visual>
    <collision>
      <origin rpy="1.570796326795 0 0" xyz="0 0 0"/>
      <geometry>
        <cylinder length="0.02" radius="0.0315"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="0.02"/>
      <inertia ixx="0.000190416666667" ixy="0" ixz="0" iyy="0.0001904" iyz="0" izz="0.00036"/>
    </inertial>
  </link>
  <joint name="right_caster_joint" type="fixed">
    <parent link="base_link"/>
    <child link="right_caster_link"/>
    <origin xyz="0.25 -0.1435 -0.13"/>
    <axis xyz="0 0 0"/>
  </joint>
  <ros2_control name="RobotCar" type="system">
    <hardware>
      <plugin>robotcar_base/RobotCarHardwareInterface</plugin>
      <param name="serial_port_name">/dev/STM32</param>
      <param name="serial_baud_rate">115200</param>
      <param name="wheel_radius">0.06</param>
      <param name="wheel_separation">0.34</param>
    </hardware>
    <joint name="left_wheel_joint">
      <command_interface name="velocity"/>
      <state_interface name="position"/>
      <state_interface name="velocity"/>
    </joint>
    <joint name="right_wheel_joint">
      <command_interface name="velocity"/>
      <state_interface name="position"/>
      <state_interface name="velocity"/>
    </joint>
  </ros2_control>
</robot>
