#include "robotcar_base/robotcar_hardware_interface.hpp"
#include <chrono>
#include <cmath>
#include <limits>
#include <memory>
#include <vector>
#include <stdexcept>
#include "hardware_interface/types/hardware_interface_type_values.hpp"
#include "rclcpp/rclcpp.hpp"

namespace robotcar_base
{

// Helper struct for serial communication
#pragma pack (1)
typedef struct _REPORT_DATA_  
{
	unsigned char Head_1;    
    unsigned char Head_2; 
    unsigned char cmd_1; 
    unsigned char flag_1;
    unsigned char flag_2;
	int Speed_X;	
  int Speed_Y;				
	int Speed_Z;			
  float power;		
	unsigned char  Sum; 				
}REPORT_DATA;

typedef struct _CMD_DATA_  
{
	unsigned char Head_1;    
  unsigned char Head_2; 
  unsigned char cmd_1; 
  unsigned char cmd_2;
  unsigned short mode1;
  unsigned short mode2;
	unsigned int  Speed_X;		
  unsigned int  Speed_Y;	
	unsigned int  Speed_Z;			
	unsigned char  Sum; 				
}CMD_DATA;
#pragma pack ()

uint8_t CalcChecksum(uint8_t *pBuf, uint32_t nLength)
{
    uint8_t checksum = 0;
    for(uint32_t i = 0; i < (nLength-1); i++)
    {
        checksum ^= pBuf[i];
    }
    return checksum;
}

hardware_interface::CallbackReturn RobotCarHardwareInterface::on_init(
  const hardware_interface::HardwareInfo & info)
{
    if (hardware_interface::SystemInterface::on_init(info) != hardware_interface::CallbackReturn::SUCCESS)
    {
        return hardware_interface::CallbackReturn::ERROR;
    }

    // Get hardware parameters
    serial_port_name_ = info_.hardware_parameters["serial_port_name"];
    serial_baud_rate_ = std::stoi(info_.hardware_parameters["serial_baud_rate"]);
    wheel_radius_ = std::stod(info_.hardware_parameters["wheel_radius"]);
    wheel_separation_ = std::stod(info_.hardware_parameters["wheel_separation"]);

    // Initialize states and commands
    hw_position_left_ = 0.0;
    hw_velocity_left_ = 0.0;
    hw_position_right_ = 0.0;
    hw_velocity_right_ = 0.0;
    hw_command_velocity_left_ = 0.0;
    hw_command_velocity_right_ = 0.0;
    hw_mode1_.store(0);
    hw_mode2_.store(0);

    // --- Create a node for non-real-time communication (publishers, services) ---
    non_realtime_node_ = rclcpp::Node::make_shared(info_.name + "_non_realtime_node");
    car_info_pub_ = non_realtime_node_->create_publisher<robotcar_base::msg::CarInfo>("/car_info", rclcpp::SystemDefaultsQoS());
    
    // Create the service server for setting control modes
    auto service_callback =
      [this](const std::shared_ptr<robotcar_base::srv::SetControlMode::Request> request,
             std::shared_ptr<robotcar_base::srv::SetControlMode::Response>      response)
      {
        RCLCPP_INFO(
          rclcpp::get_logger("RobotCarHardwareInterface"),
          "Setting control mode: mode1=%d, mode2=%d", request->mode1, request->mode2);
        this->hw_mode1_.store(request->mode1);
        this->hw_mode2_.store(request->mode2);
        response->success = true;
      };
    mode_service_ = non_realtime_node_->create_service<robotcar_base::srv::SetControlMode>("set_control_mode", service_callback);

    node_thread_ = std::thread([this]() { rclcpp::spin(this->non_realtime_node_); });

    RCLCPP_INFO(rclcpp::get_logger("RobotCarHardwareInterface"), "on_init successful");
    return hardware_interface::CallbackReturn::SUCCESS;
}

std::vector<hardware_interface::StateInterface> RobotCarHardwareInterface::export_state_interfaces()
{
  std::vector<hardware_interface::StateInterface> state_interfaces;

  state_interfaces.emplace_back(hardware_interface::StateInterface(info_.joints[0].name, hardware_interface::HW_IF_POSITION, &hw_position_left_));
  state_interfaces.emplace_back(hardware_interface::StateInterface(info_.joints[0].name, hardware_interface::HW_IF_VELOCITY, &hw_velocity_left_));
  state_interfaces.emplace_back(hardware_interface::StateInterface(info_.joints[1].name, hardware_interface::HW_IF_POSITION, &hw_position_right_));
  state_interfaces.emplace_back(hardware_interface::StateInterface(info_.joints[1].name, hardware_interface::HW_IF_VELOCITY, &hw_velocity_right_));

  return state_interfaces;
}

std::vector<hardware_interface::CommandInterface> RobotCarHardwareInterface::export_command_interfaces()
{
  std::vector<hardware_interface::CommandInterface> command_interfaces;
  command_interfaces.emplace_back(hardware_interface::CommandInterface(info_.joints[0].name, hardware_interface::HW_IF_VELOCITY, &hw_command_velocity_left_));
  command_interfaces.emplace_back(hardware_interface::CommandInterface(info_.joints[1].name, hardware_interface::HW_IF_VELOCITY, &hw_command_velocity_right_));
  return command_interfaces;
}

hardware_interface::CallbackReturn RobotCarHardwareInterface::on_activate(
  const rclcpp_lifecycle::State & /*previous_state*/)
{
    RCLCPP_INFO(rclcpp::get_logger("RobotCarHardwareInterface"), "Activating ...please wait...");
    try
    {
        serial_port_.Open(serial_port_name_);

        // Convert integer baud rate to LibSerial::BaudRate enum
        LibSerial::BaudRate baud_rate;
        switch (serial_baud_rate_) {
            case 9600:
                baud_rate = LibSerial::BaudRate::BAUD_9600;
                break;
            case 19200:
                baud_rate = LibSerial::BaudRate::BAUD_19200;
                break;
            case 38400:
                baud_rate = LibSerial::BaudRate::BAUD_38400;
                break;
            case 57600:
                baud_rate = LibSerial::BaudRate::BAUD_57600;
                break;
            case 115200:
                baud_rate = LibSerial::BaudRate::BAUD_115200;
                break;
            case 230400:
                baud_rate = LibSerial::BaudRate::BAUD_230400;
                break;
            default:
                RCLCPP_ERROR(rclcpp::get_logger("RobotCarHardwareInterface"),
                           "Unsupported baud rate: %d. Using 115200 as default.", serial_baud_rate_);
                baud_rate = LibSerial::BaudRate::BAUD_115200;
                break;
        }

        serial_port_.SetBaudRate(baud_rate);
        serial_port_.SetCharacterSize(LibSerial::CharacterSize::CHAR_SIZE_8);
        serial_port_.SetFlowControl(LibSerial::FlowControl::FLOW_CONTROL_NONE);
        serial_port_.SetParity(LibSerial::Parity::PARITY_NONE);
        serial_port_.SetStopBits(LibSerial::StopBits::STOP_BITS_1);
    }
    catch (const LibSerial::OpenFailed& e)
    {
        RCLCPP_FATAL(rclcpp::get_logger("RobotCarHardwareInterface"), "Failed to open serial port %s: %s", serial_port_name_.c_str(), e.what());
        return hardware_interface::CallbackReturn::ERROR;
    }
    RCLCPP_INFO(rclcpp::get_logger("RobotCarHardwareInterface"), "Successfully activated!");
    return hardware_interface::CallbackReturn::SUCCESS;
}

hardware_interface::CallbackReturn RobotCarHardwareInterface::on_deactivate(
  const rclcpp_lifecycle::State & /*previous_state*/)
{
    RCLCPP_INFO(rclcpp::get_logger("RobotCarHardwareInterface"), "Deactivating ...please wait...");
    if (serial_port_.IsOpen())
    {
        serial_port_.Close();
    }
    if (node_thread_.joinable())
    {
        node_thread_.join();
    }
    RCLCPP_INFO(rclcpp::get_logger("RobotCarHardwareInterface"), "Successfully deactivated!");
    return hardware_interface::CallbackReturn::SUCCESS;
}

hardware_interface::return_type RobotCarHardwareInterface::read(
  const rclcpp::Time & /*time*/, const rclcpp::Duration & period)
{
    if (serial_port_.IsDataAvailable())
    {
        std::vector<uint8_t> read_buffer;
        try
        {
            serial_port_.Read(read_buffer, sizeof(REPORT_DATA), 100); // 100 ms timeout
            if (read_buffer.size() == sizeof(REPORT_DATA))
            {
                REPORT_DATA report_data;
                memcpy(&report_data, read_buffer.data(), sizeof(REPORT_DATA));
                
                if (report_data.Head_1 == 0xa0 && report_data.Head_2 == 0x0a && report_data.cmd_1 == 0x55)
                {
                    double vx = static_cast<double>(report_data.Speed_X) / 1000.0;
                    double vth = static_cast<double>(report_data.Speed_Z) / 1000.0;
                    
                    hw_velocity_left_ = (vx - vth * wheel_separation_ / 2.0) / wheel_radius_;
                    hw_velocity_right_ = (vx + vth * wheel_separation_ / 2.0) / wheel_radius_;
                    
                    hw_position_left_ += hw_velocity_left_ * period.seconds();
                    hw_position_right_ += hw_velocity_right_ * period.seconds();

                    auto msg = std::make_unique<robotcar_base::msg::CarInfo>();
                    msg->speed_x = vx;
                    msg->speed_z = vth;
                    msg->power = report_data.power;
                    car_info_pub_->publish(std::move(msg));
                }
            }
        }
        catch (const LibSerial::ReadTimeout&)
        {
             // This is expected if no data is available
        }
    }
    return hardware_interface::return_type::OK;
}

hardware_interface::return_type RobotCarHardwareInterface::write(
  const rclcpp::Time & /*time*/, const rclcpp::Duration & /*period*/)
{
    double vx = (hw_command_velocity_right_ + hw_command_velocity_left_) * wheel_radius_ / 2.0;
    double vth = (hw_command_velocity_right_ - hw_command_velocity_left_) * wheel_radius_ / wheel_separation_;
    
    CMD_DATA cmd_data;
    cmd_data.Head_1 = 0xA0;
    cmd_data.Head_2 = 0x0A;
    cmd_data.cmd_1  = 0xAA;
    cmd_data.cmd_2  = 0x20;
    cmd_data.mode1   = hw_mode1_.load();
    cmd_data.mode2   = hw_mode2_.load();
    cmd_data.Speed_X = static_cast<int>(vx * 1000);
    cmd_data.Speed_Y = 0;
    cmd_data.Speed_Z = static_cast<int>(vth * 1000);
    cmd_data.Sum = CalcChecksum(reinterpret_cast<uint8_t*>(&cmd_data), sizeof(CMD_DATA));
    
    std::vector<uint8_t> write_buffer(sizeof(CMD_DATA));
    memcpy(write_buffer.data(), &cmd_data, sizeof(CMD_DATA));

    serial_port_.Write(write_buffer);

    return hardware_interface::return_type::OK;
}

}  // namespace robotcar_base

#include "pluginlib/class_list_macros.hpp"

PLUGINLIB_EXPORT_CLASS(
  robotcar_base::RobotCarHardwareInterface, hardware_interface::SystemInterface) 