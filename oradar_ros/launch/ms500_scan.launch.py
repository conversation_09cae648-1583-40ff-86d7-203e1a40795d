#!/usr/bin/env python3
from launch import LaunchDescription
from launch_ros.actions import Node

'''
parameters=[
        {'device_model': 'MS500'},
        {'frame_id': 'laser_frame'},
        {'scan_topic': 'scan'},
        {'lidar_ip': '*************'},
        {'lidar_port': 2007},
        {'angle_min': -135.0},
        {'angle_max': 135.0},
        {'range_min': 0.05},
        {'range_max': 30.0},
        {'inverted': False},
        {'motor_speed': 25},
        {'filter_size': 1},
        {'motor_dir': 0}
      ]
'''

def generate_launch_description():
  # LiDAR publisher node
  ordlidar_node = Node(
      package='oradar_ros',
      executable='oradar_scan',
      name='MS500',
      output='screen',
      parameters=[
        {'device_model': 'MS500'},
        {'frame_id': 'laser'},
        {'scan_topic': 'scan'},
        {'lidar_ip': '*************'},
        {'lidar_port': 2007},
        {'angle_min': -135.0},
        {'angle_max': 135.0},
        {'range_min': 0.05},
        {'range_max': 20.0},
        {'inverted': False},
        {'motor_speed': 25},
        {'filter_size': 1},
        {'motor_dir': 0}
      ]
  )

  # base_link to laser_frame tf node - 匹配URDF中的激光雷达位置
  base_link_to_laser_tf_node = Node(
    package='tf2_ros',
    executable='static_transform_publisher',
    name='base_link_to_base_laser',
    arguments=['--x', '0.2765', '--y', '0', '--z', '0.06',
               '--roll', '0', '--pitch', '0', '--yaw', '0',
               '--frame-id', 'base_link', '--child-frame-id', 'laser']
  )


  # Define LaunchDescription variable
  ord = LaunchDescription()

  ord.add_action(ordlidar_node)
  ord.add_action(base_link_to_laser_tf_node)  # 启用TF变换

  return ord