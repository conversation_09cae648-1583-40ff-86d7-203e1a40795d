<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>TCP, UDP and ICMP</title>
<link rel="stylesheet" href="../../../boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.75.2">
<link rel="home" href="../../../index.html" title="Asio">
<link rel="up" href="../networking.html" title="Networking">
<link rel="prev" href="../networking.html" title="Networking">
<link rel="next" href="other_protocols.html" title="Support for Other Protocols">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr><td valign="top"><img alt="asio C++ library" width="250" height="60" src="../../../asio.png"></td></tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="../networking.html"><img src="../../../prev.png" alt="Prev"></a><a accesskey="u" href="../networking.html"><img src="../../../up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../home.png" alt="Home"></a><a accesskey="n" href="other_protocols.html"><img src="../../../next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="asio.overview.networking.protocols"></a><a class="link" href="protocols.html" title="TCP, UDP and ICMP">TCP, UDP and ICMP</a>
</h4></div></div></div>
<p>
          Asio provides off-the-shelf support for the internet protocols TCP, UDP
          and ICMP.
        </p>
<h6>
<a name="asio.overview.networking.protocols.h0"></a>
          <span><a name="asio.overview.networking.protocols.tcp_clients"></a></span><a class="link" href="protocols.html#asio.overview.networking.protocols.tcp_clients">TCP
          Clients</a>
        </h6>
<p>
          Hostname resolution is performed using a resolver, where host and service
          names are looked up and converted into one or more endpoints:
        </p>
<pre class="programlisting">ip::tcp::resolver resolver(my_io_context);
ip::tcp::resolver::query query("www.boost.org", "http");
ip::tcp::resolver::iterator iter = resolver.resolve(query);
ip::tcp::resolver::iterator end; // End marker.
while (iter != end)
{
  ip::tcp::endpoint endpoint = *iter++;
  std::cout &lt;&lt; endpoint &lt;&lt; std::endl;
}
</pre>
<p>
          The list of endpoints obtained above could contain both IPv4 and IPv6 endpoints,
          so a program should try each of them until it finds one that works. This
          keeps the client program independent of a specific IP version.
        </p>
<p>
          To simplify the development of protocol-independent programs, TCP clients
          may establish connections using the free functions <a class="link" href="../../reference/connect.html" title="connect">connect()</a>
          and <a class="link" href="../../reference/async_connect.html" title="async_connect">async_connect()</a>.
          These operations try each endpoint in a list until the socket is successfully
          connected. For example, a single call:
        </p>
<pre class="programlisting">ip::tcp::socket socket(my_io_context);
asio::connect(socket, resolver.resolve(query));
</pre>
<p>
          will synchronously try all endpoints until one is successfully connected.
          Similarly, an asynchronous connect may be performed by writing:
        </p>
<pre class="programlisting">asio::async_connect(socket_, iter,
    boost::bind(&amp;client::handle_connect, this,
      asio::placeholders::error));

// ...

void handle_connect(const error_code&amp; error)
{
  if (!error)
  {
    // Start read or write operations.
  }
  else
  {
    // Handle error.
  }
}
</pre>
<p>
          When a specific endpoint is available, a socket can be created and connected:
        </p>
<pre class="programlisting">ip::tcp::socket socket(my_io_context);
socket.connect(endpoint);
</pre>
<p>
          Data may be read from or written to a connected TCP socket using the <a class="link" href="../../reference/basic_stream_socket/receive.html" title="basic_stream_socket::receive">receive()</a>,
          <a class="link" href="../../reference/basic_stream_socket/async_receive.html" title="basic_stream_socket::async_receive">async_receive()</a>,
          <a class="link" href="../../reference/basic_stream_socket/send.html" title="basic_stream_socket::send">send()</a> or
          <a class="link" href="../../reference/basic_stream_socket/async_send.html" title="basic_stream_socket::async_send">async_send()</a>
          member functions. However, as these could result in <a class="link" href="../core/streams.html" title="Streams, Short Reads and Short Writes">short
          writes or reads</a>, an application will typically use the following
          operations instead: <a class="link" href="../../reference/read.html" title="read">read()</a>,
          <a class="link" href="../../reference/async_read.html" title="async_read">async_read()</a>, <a class="link" href="../../reference/write.html" title="write">write()</a>
          and <a class="link" href="../../reference/async_write.html" title="async_write">async_write()</a>.
        </p>
<h6>
<a name="asio.overview.networking.protocols.h1"></a>
          <span><a name="asio.overview.networking.protocols.tcp_servers"></a></span><a class="link" href="protocols.html#asio.overview.networking.protocols.tcp_servers">TCP
          Servers</a>
        </h6>
<p>
          A program uses an acceptor to accept incoming TCP connections:
        </p>
<pre class="programlisting">ip::tcp::acceptor acceptor(my_io_context, my_endpoint);
...
ip::tcp::socket socket(my_io_context);
acceptor.accept(socket);
</pre>
<p>
          After a socket has been successfully accepted, it may be read from or written
          to as illustrated for TCP clients above.
        </p>
<h6>
<a name="asio.overview.networking.protocols.h2"></a>
          <span><a name="asio.overview.networking.protocols.udp"></a></span><a class="link" href="protocols.html#asio.overview.networking.protocols.udp">UDP</a>
        </h6>
<p>
          UDP hostname resolution is also performed using a resolver:
        </p>
<pre class="programlisting">ip::udp::resolver resolver(my_io_context);
ip::udp::resolver::query query("localhost", "daytime");
ip::udp::resolver::iterator iter = resolver.resolve(query);
...
</pre>
<p>
          A UDP socket is typically bound to a local endpoint. The following code
          will create an IP version 4 UDP socket and bind it to the "any"
          address on port <code class="computeroutput">12345</code>:
        </p>
<pre class="programlisting">ip::udp::endpoint endpoint(ip::udp::v4(), 12345);
ip::udp::socket socket(my_io_context, endpoint);
</pre>
<p>
          Data may be read from or written to an unconnected UDP socket using the
          <a class="link" href="../../reference/basic_datagram_socket/receive_from.html" title="basic_datagram_socket::receive_from">receive_from()</a>,
          <a class="link" href="../../reference/basic_datagram_socket/async_receive_from.html" title="basic_datagram_socket::async_receive_from">async_receive_from()</a>,
          <a class="link" href="../../reference/basic_datagram_socket/send_to.html" title="basic_datagram_socket::send_to">send_to()</a>
          or <a class="link" href="../../reference/basic_datagram_socket/async_send_to.html" title="basic_datagram_socket::async_send_to">async_send_to()</a>
          member functions. For a connected UDP socket, use the <a class="link" href="../../reference/basic_datagram_socket/receive.html" title="basic_datagram_socket::receive">receive()</a>,
          <a class="link" href="../../reference/basic_datagram_socket/async_receive.html" title="basic_datagram_socket::async_receive">async_receive()</a>,
          <a class="link" href="../../reference/basic_datagram_socket/send.html" title="basic_datagram_socket::send">send()</a>
          or <a class="link" href="../../reference/basic_datagram_socket/async_send.html" title="basic_datagram_socket::async_send">async_send()</a>
          member functions.
        </p>
<h6>
<a name="asio.overview.networking.protocols.h3"></a>
          <span><a name="asio.overview.networking.protocols.icmp"></a></span><a class="link" href="protocols.html#asio.overview.networking.protocols.icmp">ICMP</a>
        </h6>
<p>
          As with TCP and UDP, ICMP hostname resolution is performed using a resolver:
        </p>
<pre class="programlisting">ip::icmp::resolver resolver(my_io_context);
ip::icmp::resolver::query query("localhost", "");
ip::icmp::resolver::iterator iter = resolver.resolve(query);
...
</pre>
<p>
          An ICMP socket may be bound to a local endpoint. The following code will
          create an IP version 6 ICMP socket and bind it to the "any" address:
        </p>
<pre class="programlisting">ip::icmp::endpoint endpoint(ip::icmp::v6(), 0);
ip::icmp::socket socket(my_io_context, endpoint);
</pre>
<p>
          The port number is not used for ICMP.
        </p>
<p>
          Data may be read from or written to an unconnected ICMP socket using the
          <a class="link" href="../../reference/basic_raw_socket/receive_from.html" title="basic_raw_socket::receive_from">receive_from()</a>,
          <a class="link" href="../../reference/basic_raw_socket/async_receive_from.html" title="basic_raw_socket::async_receive_from">async_receive_from()</a>,
          <a class="link" href="../../reference/basic_raw_socket/send_to.html" title="basic_raw_socket::send_to">send_to()</a>
          or <a class="link" href="../../reference/basic_raw_socket/async_send_to.html" title="basic_raw_socket::async_send_to">async_send_to()</a>
          member functions.
        </p>
<h6>
<a name="asio.overview.networking.protocols.h4"></a>
          <span><a name="asio.overview.networking.protocols.see_also"></a></span><a class="link" href="protocols.html#asio.overview.networking.protocols.see_also">See
          Also</a>
        </h6>
<p>
          <a class="link" href="../../reference/ip__tcp.html" title="ip::tcp">ip::tcp</a>, <a class="link" href="../../reference/ip__udp.html" title="ip::udp">ip::udp</a>,
          <a class="link" href="../../reference/ip__icmp.html" title="ip::icmp">ip::icmp</a>, <a class="link" href="../../tutorial/tutdaytime1.html" title="Daytime.1 - A synchronous TCP daytime client">daytime
          protocol tutorials</a>, <a class="link" href="../../examples/cpp03_examples.html#asio.examples.cpp03_examples.icmp">ICMP
          ping example</a>.
        </p>
</div>
<table xmlns:rev="http://www.cs.rpi.edu/~gregod/boost/tools/doc/revision" width="100%"><tr>
<td align="left"></td>
<td align="right"><div class="copyright-footer">Copyright © 2003-2021 Christopher
      M. Kohlhoff<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="../networking.html"><img src="../../../prev.png" alt="Prev"></a><a accesskey="u" href="../networking.html"><img src="../../../up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../home.png" alt="Home"></a><a accesskey="n" href="other_protocols.html"><img src="../../../next.png" alt="Next"></a>
</div>
</body>
</html>
