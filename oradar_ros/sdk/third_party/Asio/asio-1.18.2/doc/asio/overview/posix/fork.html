<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Fork</title>
<link rel="stylesheet" href="../../../boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.75.2">
<link rel="home" href="../../../index.html" title="Asio">
<link rel="up" href="../posix.html" title="POSIX-Specific Functionality">
<link rel="prev" href="stream_descriptor.html" title="Stream-Oriented File Descriptors">
<link rel="next" href="../windows.html" title="Windows-Specific Functionality">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr><td valign="top"><img alt="asio C++ library" width="250" height="60" src="../../../asio.png"></td></tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="stream_descriptor.html"><img src="../../../prev.png" alt="Prev"></a><a accesskey="u" href="../posix.html"><img src="../../../up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../home.png" alt="Home"></a><a accesskey="n" href="../windows.html"><img src="../../../next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="asio.overview.posix.fork"></a><a class="link" href="fork.html" title="Fork">Fork</a>
</h4></div></div></div>
<p>
          Asio supports programs that utilise the <code class="computeroutput">fork()</code> system call.
          Provided the program calls <code class="computeroutput">io_context.notify_fork()</code> at the
          appropriate times, Asio will recreate any internal file descriptors (such
          as the "self-pipe trick" descriptor used for waking up a reactor).
          The notification is usually performed as follows:
        </p>
<pre class="programlisting">io_context_.notify_fork(asio::io_context::fork_prepare);
if (fork() == 0)
{
  io_context_.notify_fork(asio::io_context::fork_child);
  ...
}
else
{
  io_context_.notify_fork(asio::io_context::fork_parent);
  ...
}
</pre>
<p>
          User-defined services can also be made fork-aware by overriding the <code class="computeroutput">io_context::service::notify_fork()</code>
          virtual function.
        </p>
<p>
          Note that any file descriptors accessible via Asio's public API (e.g. the
          descriptors underlying <code class="computeroutput">basic_socket&lt;&gt;</code>, <code class="computeroutput">posix::stream_descriptor</code>,
          etc.) are not altered during a fork. It is the program's responsibility
          to manage these as required.
        </p>
<h6>
<a name="asio.overview.posix.fork.h0"></a>
          <span><a name="asio.overview.posix.fork.see_also"></a></span><a class="link" href="fork.html#asio.overview.posix.fork.see_also">See
          Also</a>
        </h6>
<p>
          <a class="link" href="../../reference/io_context/notify_fork.html" title="io_context::notify_fork">io_context::notify_fork()</a>,
          <a class="link" href="../../reference/io_context/fork_event.html" title="io_context::fork_event">io_context::fork_event</a>,
          <a class="link" href="../../reference/execution_context__service/notify_fork.html" title="execution_context::service::notify_fork">io_context::service::notify_fork()</a>,
          <a class="link" href="../../examples/cpp03_examples.html#asio.examples.cpp03_examples.fork">Fork examples</a>.
        </p>
</div>
<table xmlns:rev="http://www.cs.rpi.edu/~gregod/boost/tools/doc/revision" width="100%"><tr>
<td align="left"></td>
<td align="right"><div class="copyright-footer">Copyright © 2003-2021 Christopher
      M. Kohlhoff<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="stream_descriptor.html"><img src="../../../prev.png" alt="Prev"></a><a accesskey="u" href="../posix.html"><img src="../../../up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../home.png" alt="Home"></a><a accesskey="n" href="../windows.html"><img src="../../../next.png" alt="Next"></a>
</div>
</body>
</html>
