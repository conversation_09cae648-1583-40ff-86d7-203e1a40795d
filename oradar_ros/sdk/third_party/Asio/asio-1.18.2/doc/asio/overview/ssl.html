<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>SSL</title>
<link rel="stylesheet" href="../../boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.75.2">
<link rel="home" href="../../index.html" title="Asio">
<link rel="up" href="../overview.html" title="Overview">
<link rel="prev" href="windows/object_handle.html" title="Object HANDLEs">
<link rel="next" href="cpp2011.html" title="C++ 2011 Support">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr><td valign="top"><img alt="asio C++ library" width="250" height="60" src="../../asio.png"></td></tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="windows/object_handle.html"><img src="../../prev.png" alt="Prev"></a><a accesskey="u" href="../overview.html"><img src="../../up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../home.png" alt="Home"></a><a accesskey="n" href="cpp2011.html"><img src="../../next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="asio.overview.ssl"></a><a class="link" href="ssl.html" title="SSL">SSL</a>
</h3></div></div></div>
<p>
        Asio contains classes and class templates for basic SSL support. These classes
        allow encrypted communication to be layered on top of an existing stream,
        such as a TCP socket.
      </p>
<p>
        Before creating an encrypted stream, an application must construct an SSL
        context object. This object is used to set SSL options such as verification
        mode, certificate files, and so on. As an illustration, client-side initialisation
        may look something like:
      </p>
<pre class="programlisting">ssl::context ctx(ssl::context::sslv23);
ctx.set_verify_mode(ssl::verify_peer);
ctx.load_verify_file("ca.pem");
</pre>
<p>
        To use SSL with a TCP socket, one may write:
      </p>
<pre class="programlisting">ssl::stream&lt;ip::tcp::socket&gt; ssl_sock(my_io_context, ctx);
</pre>
<p>
        To perform socket-specific operations, such as establishing an outbound connection
        or accepting an incoming one, the underlying socket must first be obtained
        using the <code class="computeroutput">ssl::stream</code> template's <a class="link" href="../reference/ssl__stream/lowest_layer.html" title="ssl::stream::lowest_layer"><code class="computeroutput">lowest_layer()</code></a>
        member function:
      </p>
<pre class="programlisting">ip::tcp::socket::lowest_layer_type&amp; sock = ssl_sock.lowest_layer();
sock.connect(my_endpoint);
</pre>
<p>
        In some use cases the underlying stream object will need to have a longer
        lifetime than the SSL stream, in which case the template parameter should
        be a reference to the stream type:
      </p>
<pre class="programlisting">ip::tcp::socket sock(my_io_context);
ssl::stream&lt;ip::tcp::socket&amp;&gt; ssl_sock(sock, ctx);
</pre>
<p>
        SSL handshaking must be performed prior to transmitting or receiving data
        over an encrypted connection. This is accomplished using the <code class="computeroutput">ssl::stream</code>
        template's <a class="link" href="../reference/ssl__stream/handshake.html" title="ssl::stream::handshake">handshake()</a>
        or <a class="link" href="../reference/ssl__stream/async_handshake.html" title="ssl::stream::async_handshake">async_handshake()</a>
        member functions.
      </p>
<p>
        Once connected, SSL stream objects are used as synchronous or asynchronous
        read and write streams. This means the objects can be used with any of the
        <a class="link" href="../reference/read.html" title="read">read()</a>, <a class="link" href="../reference/async_read.html" title="async_read">async_read()</a>,
        <a class="link" href="../reference/write.html" title="write">write()</a>, <a class="link" href="../reference/async_write.html" title="async_write">async_write()</a>,
        <a class="link" href="../reference/read_until.html" title="read_until">read_until()</a> or <a class="link" href="../reference/async_read_until.html" title="async_read_until">async_read_until()</a>
        free functions.
      </p>
<h5>
<a name="asio.overview.ssl.h0"></a>
        <span><a name="asio.overview.ssl.certificate_verification"></a></span><a class="link" href="ssl.html#asio.overview.ssl.certificate_verification">Certificate
        Verification</a>
      </h5>
<p>
        Asio provides various methods for configuring the way SSL certificates are
        verified:
      </p>
<div class="itemizedlist"><ul class="itemizedlist" type="disc">
<li class="listitem">
            <a class="link" href="../reference/ssl__context/set_default_verify_paths.html" title="ssl::context::set_default_verify_paths">ssl::context::set_default_verify_paths()</a>
          </li>
<li class="listitem">
            <a class="link" href="../reference/ssl__context/set_verify_mode.html" title="ssl::context::set_verify_mode">ssl::context::set_verify_mode()</a>
          </li>
<li class="listitem">
            <a class="link" href="../reference/ssl__context/set_verify_callback.html" title="ssl::context::set_verify_callback">ssl::context::set_verify_callback()</a>
          </li>
<li class="listitem">
            <a class="link" href="../reference/ssl__context/load_verify_file.html" title="ssl::context::load_verify_file">ssl::context::load_verify_file()</a>
          </li>
<li class="listitem">
            <a class="link" href="../reference/ssl__stream/set_verify_mode.html" title="ssl::stream::set_verify_mode">ssl::stream::set_verify_mode()</a>
          </li>
<li class="listitem">
            <a class="link" href="../reference/ssl__stream/set_verify_callback.html" title="ssl::stream::set_verify_callback">ssl::stream::set_verify_callback()</a>
          </li>
</ul></div>
<p>
        To simplify use cases where certificates are verified according to the rules
        in RFC 6125 (identity verification in the context of Transport Layer Security),
        Asio provides a reusable verification callback as a function object:
      </p>
<div class="itemizedlist"><ul class="itemizedlist" type="disc"><li class="listitem">
            <a class="link" href="../reference/ssl__host_name_verification.html" title="ssl::host_name_verification">ssl::host_name_verification</a>
          </li></ul></div>
<p>
        The following example shows verification of a remote host's certificate according
        to the rules used by HTTPS:
      </p>
<pre class="programlisting">using asio::ip::tcp;
namespace ssl = asio::ssl;
typedef ssl::stream&lt;tcp::socket&gt; ssl_socket;

// Create a context that uses the default paths for
// finding CA certificates.
ssl::context ctx(ssl::context::sslv23);
ctx.set_default_verify_paths();

// Open a socket and connect it to the remote host.
asio::io_context io_context;
ssl_socket sock(io_context, ctx);
tcp::resolver resolver(io_context);
tcp::resolver::query query("host.name", "https");
asio::connect(sock.lowest_layer(), resolver.resolve(query));
sock.lowest_layer().set_option(tcp::no_delay(true));

// Perform SSL handshake and verify the remote host's
// certificate.
sock.set_verify_mode(ssl::verify_peer);
sock.set_verify_callback(ssl::host_name_verification("host.name"));
sock.handshake(ssl_socket::client);

// ... read and write as normal ...
</pre>
<h5>
<a name="asio.overview.ssl.h1"></a>
        <span><a name="asio.overview.ssl.ssl_and_threads"></a></span><a class="link" href="ssl.html#asio.overview.ssl.ssl_and_threads">SSL
        and Threads</a>
      </h5>
<p>
        SSL stream objects perform no locking of their own. Therefore, it is essential
        that all asynchronous SSL operations are performed in an implicit or explicit
        <a class="link" href="core/strands.html" title="Strands: Use Threads Without Explicit Locking">strand</a>. Note that this
        means that no synchronisation is required (and so no locking overhead is
        incurred) in single threaded programs.
      </p>
<h5>
<a name="asio.overview.ssl.h2"></a>
        <span><a name="asio.overview.ssl.see_also"></a></span><a class="link" href="ssl.html#asio.overview.ssl.see_also">See
        Also</a>
      </h5>
<p>
        <a class="link" href="../reference/ssl__context.html" title="ssl::context">ssl::context</a>, <a class="link" href="../reference/ssl__host_name_verification.html" title="ssl::host_name_verification">ssl::host_name_verification</a>,
        <a class="link" href="../reference/ssl__stream.html" title="ssl::stream">ssl::stream</a>, <a class="link" href="../examples/cpp03_examples.html#asio.examples.cpp03_examples.ssl">SSL
        example (C++03)</a>, <a class="link" href="../examples/cpp11_examples.html#asio.examples.cpp11_examples.ssl">SSL
        example (C++11)</a>.
      </p>
<h5>
<a name="asio.overview.ssl.h3"></a>
        <span><a name="asio.overview.ssl.notes"></a></span><a class="link" href="ssl.html#asio.overview.ssl.notes">Notes</a>
      </h5>
<p>
        <a href="http://www.openssl.org" target="_top">OpenSSL</a> is required to make use
        of Asio's SSL support. When an application needs to use OpenSSL functionality
        that is not wrapped by Asio, the underlying OpenSSL types may be obtained
        by calling <a class="link" href="../reference/ssl__context/native_handle.html" title="ssl::context::native_handle"><code class="computeroutput">ssl::context::native_handle()</code></a>
        or <a class="link" href="../reference/ssl__stream/native_handle.html" title="ssl::stream::native_handle"><code class="computeroutput">ssl::stream::native_handle()</code></a>.
      </p>
</div>
<table xmlns:rev="http://www.cs.rpi.edu/~gregod/boost/tools/doc/revision" width="100%"><tr>
<td align="left"></td>
<td align="right"><div class="copyright-footer">Copyright © 2003-2021 Christopher
      M. Kohlhoff<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="windows/object_handle.html"><img src="../../prev.png" alt="Prev"></a><a accesskey="u" href="../overview.html"><img src="../../up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../home.png" alt="Home"></a><a accesskey="n" href="cpp2011.html"><img src="../../next.png" alt="Next"></a>
</div>
</body>
</html>
