<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Futures</title>
<link rel="stylesheet" href="../../../boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.75.2">
<link rel="home" href="../../../index.html" title="Asio">
<link rel="up" href="../cpp2011.html" title="C++ 2011 Support">
<link rel="prev" href="chrono.html" title="Chrono">
<link rel="next" href="../implementation.html" title="Platform-Specific Implementation Notes">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr><td valign="top"><img alt="asio C++ library" width="250" height="60" src="../../../asio.png"></td></tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="chrono.html"><img src="../../../prev.png" alt="Prev"></a><a accesskey="u" href="../cpp2011.html"><img src="../../../up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../home.png" alt="Home"></a><a accesskey="n" href="../implementation.html"><img src="../../../next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="asio.overview.cpp2011.futures"></a><a class="link" href="futures.html" title="Futures">Futures</a>
</h4></div></div></div>
<p>
          The <code class="computeroutput">asio::use_future</code> special value provides first-class support
          for returning a C++11 <code class="computeroutput">std::future</code> from an asynchronous operation's
          initiating function.
        </p>
<p>
          To use <code class="computeroutput">asio::use_future</code>, pass it to an asynchronous operation
          instead of a normal completion handler. For example:
        </p>
<pre class="programlisting">std::future&lt;std::size_t&gt; length =
  my_socket.async_read_some(my_buffer, asio::use_future);
</pre>
<p>
          Where a handler signature has the form:
        </p>
<pre class="programlisting">void handler(asio::error_code ec, result_type result);
</pre>
<p>
          the initiating function returns a <code class="computeroutput">std::future</code> templated on
          <code class="computeroutput">result_type</code>. In the above example, this is <code class="computeroutput">std::size_t</code>.
          If the asynchronous operation fails, the <code class="computeroutput">error_code</code> is converted
          into a <code class="computeroutput">system_error</code> exception and passed back to the caller
          through the future.
        </p>
<p>
          Where a handler signature has the form:
        </p>
<pre class="programlisting">void handler(asio::error_code ec);
</pre>
<p>
          the initiating function returns <code class="computeroutput">std::future&lt;void&gt;</code>. As
          above, an error is passed back in the future as a <code class="computeroutput">system_error</code>
          exception.
        </p>
<p>
          <a class="link" href="../../reference/use_future.html" title="use_future">use_future</a>, <a class="link" href="../../reference/use_future_t.html" title="use_future_t">use_future_t</a>,
          <a class="link" href="../../examples/cpp11_examples.html#asio.examples.cpp11_examples.futures">Futures example (C++11)</a>.
        </p>
</div>
<table xmlns:rev="http://www.cs.rpi.edu/~gregod/boost/tools/doc/revision" width="100%"><tr>
<td align="left"></td>
<td align="right"><div class="copyright-footer">Copyright © 2003-2021 Christopher
      M. Kohlhoff<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="chrono.html"><img src="../../../prev.png" alt="Prev"></a><a accesskey="u" href="../cpp2011.html"><img src="../../../up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../home.png" alt="Home"></a><a accesskey="n" href="../implementation.html"><img src="../../../next.png" alt="Next"></a>
</div>
</body>
</html>
