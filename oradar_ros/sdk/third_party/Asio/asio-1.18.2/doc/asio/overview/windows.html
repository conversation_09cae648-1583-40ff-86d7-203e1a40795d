<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Windows-Specific Functionality</title>
<link rel="stylesheet" href="../../boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.75.2">
<link rel="home" href="../../index.html" title="Asio">
<link rel="up" href="../overview.html" title="Overview">
<link rel="prev" href="posix/fork.html" title="Fork">
<link rel="next" href="windows/stream_handle.html" title="Stream-Oriented HANDLEs">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr><td valign="top"><img alt="asio C++ library" width="250" height="60" src="../../asio.png"></td></tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="posix/fork.html"><img src="../../prev.png" alt="Prev"></a><a accesskey="u" href="../overview.html"><img src="../../up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../home.png" alt="Home"></a><a accesskey="n" href="windows/stream_handle.html"><img src="../../next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="asio.overview.windows"></a><a class="link" href="windows.html" title="Windows-Specific Functionality">Windows-Specific Functionality</a>
</h3></div></div></div>
<p>
        <a class="link" href="windows/stream_handle.html" title="Stream-Oriented HANDLEs">Stream-Oriented HANDLEs</a>
      </p>
<p>
        <a class="link" href="windows/random_access_handle.html" title="Random-Access HANDLEs">Random-Access
        HANDLEs</a>
      </p>
<p>
        <a class="link" href="windows/object_handle.html" title="Object HANDLEs">Object HANDLEs</a>
      </p>
</div>
<table xmlns:rev="http://www.cs.rpi.edu/~gregod/boost/tools/doc/revision" width="100%"><tr>
<td align="left"></td>
<td align="right"><div class="copyright-footer">Copyright © 2003-2021 Christopher
      M. Kohlhoff<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="posix/fork.html"><img src="../../prev.png" alt="Prev"></a><a accesskey="u" href="../overview.html"><img src="../../up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../home.png" alt="Home"></a><a accesskey="n" href="windows/stream_handle.html"><img src="../../next.png" alt="Next"></a>
</div>
</body>
</html>
