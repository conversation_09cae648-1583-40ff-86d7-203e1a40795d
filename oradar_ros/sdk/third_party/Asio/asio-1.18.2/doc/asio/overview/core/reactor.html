<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Reactor-Style Operations</title>
<link rel="stylesheet" href="../../../boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.75.2">
<link rel="home" href="../../../index.html" title="Asio">
<link rel="up" href="../core.html" title="Core Concepts and Functionality">
<link rel="prev" href="streams.html" title="Streams, Short Reads and Short Writes">
<link rel="next" href="line_based.html" title="Line-Based Operations">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr><td valign="top"><img alt="asio C++ library" width="250" height="60" src="../../../asio.png"></td></tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="streams.html"><img src="../../../prev.png" alt="Prev"></a><a accesskey="u" href="../core.html"><img src="../../../up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../home.png" alt="Home"></a><a accesskey="n" href="line_based.html"><img src="../../../next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="asio.overview.core.reactor"></a><a class="link" href="reactor.html" title="Reactor-Style Operations">Reactor-Style Operations</a>
</h4></div></div></div>
<p>
          Sometimes a program must be integrated with a third-party library that
          wants to perform the I/O operations itself. To facilitate this, Asio includes
          synchronous and asynchronous operations that may be used to wait for a
          socket to become ready to read, ready to write, or to have a pending error
          condition.
        </p>
<p>
          As an example, to perform a non-blocking read something like the following
          may be used:
        </p>
<pre class="programlisting"><span class="identifier">ip</span><span class="special">::</span><span class="identifier">tcp</span><span class="special">::</span><span class="identifier">socket</span> <span class="identifier">socket</span><span class="special">(</span><span class="identifier">my_io_context</span><span class="special">);</span>
<span class="special">...</span>
<span class="identifier">socket</span><span class="special">.</span><span class="identifier">non_blocking</span><span class="special">(</span><span class="keyword">true</span><span class="special">);</span>
<span class="special">...</span>
<span class="identifier">socket</span><span class="special">.</span><span class="identifier">async_wait</span><span class="special">(</span><span class="identifier">ip</span><span class="special">::</span><span class="identifier">tcp</span><span class="special">::</span><span class="identifier">socket</span><span class="special">::</span><span class="identifier">wait_read</span><span class="special">,</span> <span class="identifier">read_handler</span><span class="special">);</span>
<span class="special">...</span>
<span class="keyword">void</span> <span class="identifier">read_handler</span><span class="special">(</span><span class="identifier">asio</span><span class="special">::</span><span class="identifier">error_code</span> <span class="identifier">ec</span><span class="special">)</span>
<span class="special">{</span>
  <span class="keyword">if</span> <span class="special">(!</span><span class="identifier">ec</span><span class="special">)</span>
  <span class="special">{</span>
    <span class="identifier">std</span><span class="special">::</span><span class="identifier">vector</span><span class="special">&lt;</span><span class="keyword">char</span><span class="special">&gt;</span> <span class="identifier">buf</span><span class="special">(</span><span class="identifier">socket</span><span class="special">.</span><span class="identifier">available</span><span class="special">());</span>
    <span class="identifier">socket</span><span class="special">.</span><span class="identifier">read_some</span><span class="special">(</span><span class="identifier">buffer</span><span class="special">(</span><span class="identifier">buf</span><span class="special">));</span>
  <span class="special">}</span>
<span class="special">}</span>
</pre>
<p>
          These operations are supported for sockets on all platforms, and for the
          POSIX stream-oriented descriptor classes.
        </p>
<h6>
<a name="asio.overview.core.reactor.h0"></a>
          <span><a name="asio.overview.core.reactor.see_also"></a></span><a class="link" href="reactor.html#asio.overview.core.reactor.see_also">See
          Also</a>
        </h6>
<p>
          <a class="link" href="../../reference/basic_socket/wait.html" title="basic_socket::wait">basic_socket::wait()</a>,
          <a class="link" href="../../reference/basic_socket/async_wait.html" title="basic_socket::async_wait">basic_socket::async_wait()</a>,
          <a class="link" href="../../reference/basic_socket/non_blocking.html" title="basic_socket::non_blocking">basic_socket::non_blocking()</a>,
          <a class="link" href="../../reference/basic_socket/native_non_blocking.html" title="basic_socket::native_non_blocking">basic_socket::native_non_blocking()</a>,
          <a class="link" href="../../examples/cpp03_examples.html#asio.examples.cpp03_examples.nonblocking">nonblocking example</a>.
        </p>
</div>
<table xmlns:rev="http://www.cs.rpi.edu/~gregod/boost/tools/doc/revision" width="100%"><tr>
<td align="left"></td>
<td align="right"><div class="copyright-footer">Copyright © 2003-2021 Christopher
      M. Kohlhoff<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="streams.html"><img src="../../../prev.png" alt="Prev"></a><a accesskey="u" href="../core.html"><img src="../../../up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../home.png" alt="Home"></a><a accesskey="n" href="line_based.html"><img src="../../../next.png" alt="Next"></a>
</div>
</body>
</html>
