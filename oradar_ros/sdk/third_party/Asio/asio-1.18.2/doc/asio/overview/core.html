<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Core Concepts and Functionality</title>
<link rel="stylesheet" href="../../boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.75.2">
<link rel="home" href="../../index.html" title="Asio">
<link rel="up" href="../overview.html" title="Overview">
<link rel="prev" href="rationale.html" title="Rationale">
<link rel="next" href="core/basics.html" title="Basic Asio Anatomy">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr><td valign="top"><img alt="asio C++ library" width="250" height="60" src="../../asio.png"></td></tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="rationale.html"><img src="../../prev.png" alt="Prev"></a><a accesskey="u" href="../overview.html"><img src="../../up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../home.png" alt="Home"></a><a accesskey="n" href="core/basics.html"><img src="../../next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="asio.overview.core"></a><a class="link" href="core.html" title="Core Concepts and Functionality">Core Concepts and Functionality</a>
</h3></div></div></div>
<div class="itemizedlist"><ul class="itemizedlist" type="disc">
<li class="listitem">
            <a class="link" href="core/basics.html" title="Basic Asio Anatomy">Basic Asio Anatomy</a>
          </li>
<li class="listitem">
            <a class="link" href="core/async.html" title="The Proactor Design Pattern: Concurrency Without Threads">The Proactor Design Pattern:
            Concurrency Without Threads</a>
          </li>
<li class="listitem">
            <a class="link" href="core/threads.html" title="Threads and Asio">Threads and Asio</a>
          </li>
<li class="listitem">
            <a class="link" href="core/strands.html" title="Strands: Use Threads Without Explicit Locking">Strands: Use Threads Without
            Explicit Locking</a>
          </li>
<li class="listitem">
            <a class="link" href="core/buffers.html" title="Buffers">Buffers</a>
          </li>
<li class="listitem">
            <a class="link" href="core/streams.html" title="Streams, Short Reads and Short Writes">Streams, Short Reads and Short
            Writes</a>
          </li>
<li class="listitem">
            <a class="link" href="core/reactor.html" title="Reactor-Style Operations">Reactor-Style Operations</a>
          </li>
<li class="listitem">
            <a class="link" href="core/line_based.html" title="Line-Based Operations">Line-Based Operations</a>
          </li>
<li class="listitem">
            <a class="link" href="core/allocation.html" title="Custom Memory Allocation">Custom Memory Allocation</a>
          </li>
<li class="listitem">
            <a class="link" href="core/handler_tracking.html" title="Handler Tracking">Handler Tracking</a>
          </li>
<li class="listitem">
            <a class="link" href="core/concurrency_hint.html" title="Concurrency Hints">Concurrency Hints</a>
          </li>
<li class="listitem">
            <a class="link" href="core/coroutine.html" title="Stackless Coroutines">Stackless Coroutines</a>
          </li>
<li class="listitem">
            <a class="link" href="core/spawn.html" title="Stackful Coroutines">Stackful Coroutines</a>
          </li>
<li class="listitem">
            <a class="link" href="core/coroutines_ts.html" title="Coroutines TS Support">Coroutines TS Support</a>
          </li>
</ul></div>
</div>
<table xmlns:rev="http://www.cs.rpi.edu/~gregod/boost/tools/doc/revision" width="100%"><tr>
<td align="left"></td>
<td align="right"><div class="copyright-footer">Copyright © 2003-2021 Christopher
      M. Kohlhoff<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="rationale.html"><img src="../../prev.png" alt="Prev"></a><a accesskey="u" href="../overview.html"><img src="../../up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../home.png" alt="Home"></a><a accesskey="n" href="core/basics.html"><img src="../../next.png" alt="Next"></a>
</div>
</body>
</html>
