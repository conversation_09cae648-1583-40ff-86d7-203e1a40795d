<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Stream-Oriented File Descriptors</title>
<link rel="stylesheet" href="../../../boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.75.2">
<link rel="home" href="../../../index.html" title="Asio">
<link rel="up" href="../posix.html" title="POSIX-Specific Functionality">
<link rel="prev" href="local.html" title="UNIX Domain Sockets">
<link rel="next" href="fork.html" title="Fork">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr><td valign="top"><img alt="asio C++ library" width="250" height="60" src="../../../asio.png"></td></tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="local.html"><img src="../../../prev.png" alt="Prev"></a><a accesskey="u" href="../posix.html"><img src="../../../up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../home.png" alt="Home"></a><a accesskey="n" href="fork.html"><img src="../../../next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="asio.overview.posix.stream_descriptor"></a><a class="link" href="stream_descriptor.html" title="Stream-Oriented File Descriptors">Stream-Oriented
        File Descriptors</a>
</h4></div></div></div>
<p>
          Asio includes classes added to permit synchronous and asynchronous read
          and write operations to be performed on POSIX file descriptors, such as
          pipes, standard input and output, and various devices.
        </p>
<p>
          These classes also provide limited support for regular files. This support
          assumes that the underlying read and write operations provided by the operating
          system never fail with <code class="computeroutput">EAGAIN</code> or <code class="computeroutput">EWOULDBLOCK</code>.
          (This assumption normally holds for buffered file I/O.) Synchronous and
          asynchronous read and write operations on file descriptors will succeed
          but the I/O will always be performed immediately. Wait operations, and
          operations involving <code class="computeroutput">asio::null_buffers</code>, are not portably
          supported.
        </p>
<p>
          For example, to perform read and write operations on standard input and
          output, the following objects may be created:
        </p>
<pre class="programlisting">posix::stream_descriptor in(my_io_context, ::dup(STDIN_FILENO));
posix::stream_descriptor out(my_io_context, ::dup(STDOUT_FILENO));
</pre>
<p>
          These are then used as synchronous or asynchronous read and write streams.
          This means the objects can be used with any of the <a class="link" href="../../reference/read.html" title="read">read()</a>,
          <a class="link" href="../../reference/async_read.html" title="async_read">async_read()</a>, <a class="link" href="../../reference/write.html" title="write">write()</a>,
          <a class="link" href="../../reference/async_write.html" title="async_write">async_write()</a>, <a class="link" href="../../reference/read_until.html" title="read_until">read_until()</a> or <a class="link" href="../../reference/async_read_until.html" title="async_read_until">async_read_until()</a>
          free functions.
        </p>
<h6>
<a name="asio.overview.posix.stream_descriptor.h0"></a>
          <span><a name="asio.overview.posix.stream_descriptor.see_also"></a></span><a class="link" href="stream_descriptor.html#asio.overview.posix.stream_descriptor.see_also">See
          Also</a>
        </h6>
<p>
          <a class="link" href="../../reference/posix__stream_descriptor.html" title="posix::stream_descriptor">posix::stream_descriptor</a>,
          <a class="link" href="../../examples/cpp03_examples.html#asio.examples.cpp03_examples.chat">Chat example (C++03)</a>,
          <a class="link" href="../../examples/cpp11_examples.html#asio.examples.cpp11_examples.chat">Chat example (C++11)</a>.
        </p>
<h6>
<a name="asio.overview.posix.stream_descriptor.h1"></a>
          <span><a name="asio.overview.posix.stream_descriptor.notes"></a></span><a class="link" href="stream_descriptor.html#asio.overview.posix.stream_descriptor.notes">Notes</a>
        </h6>
<p>
          POSIX stream descriptors are only available at compile time if supported
          by the target operating system. A program may test for the macro <code class="computeroutput">ASIO_HAS_POSIX_STREAM_DESCRIPTOR</code>
          to determine whether they are supported.
        </p>
</div>
<table xmlns:rev="http://www.cs.rpi.edu/~gregod/boost/tools/doc/revision" width="100%"><tr>
<td align="left"></td>
<td align="right"><div class="copyright-footer">Copyright © 2003-2021 Christopher
      M. Kohlhoff<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="local.html"><img src="../../../prev.png" alt="Prev"></a><a accesskey="u" href="../posix.html"><img src="../../../up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../home.png" alt="Home"></a><a accesskey="n" href="fork.html"><img src="../../../next.png" alt="Next"></a>
</div>
</body>
</html>
