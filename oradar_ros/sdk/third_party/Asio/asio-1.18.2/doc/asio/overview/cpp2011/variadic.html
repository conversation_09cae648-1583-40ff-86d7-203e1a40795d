<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Variadic Templates</title>
<link rel="stylesheet" href="../../../boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.75.2">
<link rel="home" href="../../../index.html" title="Asio">
<link rel="up" href="../cpp2011.html" title="C++ 2011 Support">
<link rel="prev" href="move_handlers.html" title="Movable Handlers">
<link rel="next" href="array.html" title="Array Container">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr><td valign="top"><img alt="asio C++ library" width="250" height="60" src="../../../asio.png"></td></tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="move_handlers.html"><img src="../../../prev.png" alt="Prev"></a><a accesskey="u" href="../cpp2011.html"><img src="../../../up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../home.png" alt="Home"></a><a accesskey="n" href="array.html"><img src="../../../next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="asio.overview.cpp2011.variadic"></a><a class="link" href="variadic.html" title="Variadic Templates">Variadic Templates</a>
</h4></div></div></div>
<p>
          When supported by a compiler, Asio can use variadic templates to implement
          the <a class="link" href="../../reference/basic_socket_streambuf/connect.html" title="basic_socket_streambuf::connect">basic_socket_streambuf::connect()</a>
          and <a class="link" href="../../reference/basic_socket_iostream/connect.html" title="basic_socket_iostream::connect">basic_socket_iostream::connect()</a>
          functions.
        </p>
<p>
          Support for variadic templates is automatically enabled for <code class="literal">g++</code>
          4.3 and later, when the <code class="literal">-std=c++0x</code> or <code class="literal">-std=gnu++0x</code>
          compiler options are used. It may be disabled by defining <code class="computeroutput">ASIO_DISABLE_VARIADIC_TEMPLATES</code>,
          or explicitly enabled for other compilers by defining <code class="computeroutput">ASIO_HAS_VARIADIC_TEMPLATES</code>.
        </p>
</div>
<table xmlns:rev="http://www.cs.rpi.edu/~gregod/boost/tools/doc/revision" width="100%"><tr>
<td align="left"></td>
<td align="right"><div class="copyright-footer">Copyright © 2003-2021 Christopher
      M. Kohlhoff<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="move_handlers.html"><img src="../../../prev.png" alt="Prev"></a><a accesskey="u" href="../cpp2011.html"><img src="../../../up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../home.png" alt="Home"></a><a accesskey="n" href="array.html"><img src="../../../next.png" alt="Next"></a>
</div>
</body>
</html>
