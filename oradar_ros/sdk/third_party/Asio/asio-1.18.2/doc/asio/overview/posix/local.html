<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>UNIX Domain Sockets</title>
<link rel="stylesheet" href="../../../boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.75.2">
<link rel="home" href="../../../index.html" title="Asio">
<link rel="up" href="../posix.html" title="POSIX-Specific Functionality">
<link rel="prev" href="../posix.html" title="POSIX-Specific Functionality">
<link rel="next" href="stream_descriptor.html" title="Stream-Oriented File Descriptors">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr><td valign="top"><img alt="asio C++ library" width="250" height="60" src="../../../asio.png"></td></tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="../posix.html"><img src="../../../prev.png" alt="Prev"></a><a accesskey="u" href="../posix.html"><img src="../../../up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../home.png" alt="Home"></a><a accesskey="n" href="stream_descriptor.html"><img src="../../../next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="asio.overview.posix.local"></a><a class="link" href="local.html" title="UNIX Domain Sockets">UNIX Domain Sockets</a>
</h4></div></div></div>
<p>
          Asio provides basic support for UNIX domain sockets (also known as local
          sockets). The simplest use involves creating a pair of connected sockets.
          The following code:
        </p>
<pre class="programlisting">local::stream_protocol::socket socket1(my_io_context);
local::stream_protocol::socket socket2(my_io_context);
local::connect_pair(socket1, socket2);
</pre>
<p>
          will create a pair of stream-oriented sockets. To do the same for datagram-oriented
          sockets, use:
        </p>
<pre class="programlisting">local::datagram_protocol::socket socket1(my_io_context);
local::datagram_protocol::socket socket2(my_io_context);
local::connect_pair(socket1, socket2);
</pre>
<p>
          A UNIX domain socket server may be created by binding an acceptor to an
          endpoint, in much the same way as one does for a TCP server:
        </p>
<pre class="programlisting">::unlink("/tmp/foobar"); // Remove previous binding.
local::stream_protocol::endpoint ep("/tmp/foobar");
local::stream_protocol::acceptor acceptor(my_io_context, ep);
local::stream_protocol::socket socket(my_io_context);
acceptor.accept(socket);
</pre>
<p>
          A client that connects to this server might look like:
        </p>
<pre class="programlisting">local::stream_protocol::endpoint ep("/tmp/foobar");
local::stream_protocol::socket socket(my_io_context);
socket.connect(ep);
</pre>
<p>
          Transmission of file descriptors or credentials across UNIX domain sockets
          is not directly supported within Asio, but may be achieved by accessing
          the socket's underlying descriptor using the <a class="link" href="../../reference/basic_socket/native_handle.html" title="basic_socket::native_handle">native_handle()</a>
          member function.
        </p>
<h6>
<a name="asio.overview.posix.local.h0"></a>
          <span><a name="asio.overview.posix.local.see_also"></a></span><a class="link" href="local.html#asio.overview.posix.local.see_also">See
          Also</a>
        </h6>
<p>
          <a class="link" href="../../reference/local__connect_pair.html" title="local::connect_pair">local::connect_pair</a>,
          <a class="link" href="../../reference/local__datagram_protocol.html" title="local::datagram_protocol">local::datagram_protocol</a>,
          <a class="link" href="../../reference/local__datagram_protocol/endpoint.html" title="local::datagram_protocol::endpoint">local::datagram_protocol::endpoint</a>,
          <a class="link" href="../../reference/local__datagram_protocol/socket.html" title="local::datagram_protocol::socket">local::datagram_protocol::socket</a>,
          <a class="link" href="../../reference/local__stream_protocol.html" title="local::stream_protocol">local::stream_protocol</a>,
          <a class="link" href="../../reference/local__stream_protocol/acceptor.html" title="local::stream_protocol::acceptor">local::stream_protocol::acceptor</a>,
          <a class="link" href="../../reference/local__stream_protocol/endpoint.html" title="local::stream_protocol::endpoint">local::stream_protocol::endpoint</a>,
          <a class="link" href="../../reference/local__stream_protocol/iostream.html" title="local::stream_protocol::iostream">local::stream_protocol::iostream</a>,
          <a class="link" href="../../reference/local__stream_protocol/socket.html" title="local::stream_protocol::socket">local::stream_protocol::socket</a>,
          <a class="link" href="../../examples/cpp03_examples.html#asio.examples.cpp03_examples.unix_domain_sockets">UNIX domain
          sockets examples</a>.
        </p>
<h6>
<a name="asio.overview.posix.local.h1"></a>
          <span><a name="asio.overview.posix.local.notes"></a></span><a class="link" href="local.html#asio.overview.posix.local.notes">Notes</a>
        </h6>
<p>
          UNIX domain sockets are only available at compile time if supported by
          the target operating system. A program may test for the macro <code class="computeroutput">ASIO_HAS_LOCAL_SOCKETS</code>
          to determine whether they are supported.
        </p>
</div>
<table xmlns:rev="http://www.cs.rpi.edu/~gregod/boost/tools/doc/revision" width="100%"><tr>
<td align="left"></td>
<td align="right"><div class="copyright-footer">Copyright © 2003-2021 Christopher
      M. Kohlhoff<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="../posix.html"><img src="../../../prev.png" alt="Prev"></a><a accesskey="u" href="../posix.html"><img src="../../../up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../home.png" alt="Home"></a><a accesskey="n" href="stream_descriptor.html"><img src="../../../next.png" alt="Next"></a>
</div>
</body>
</html>
