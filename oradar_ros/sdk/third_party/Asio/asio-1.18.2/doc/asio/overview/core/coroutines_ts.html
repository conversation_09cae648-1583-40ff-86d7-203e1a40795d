<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Coroutines TS Support</title>
<link rel="stylesheet" href="../../../boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.75.2">
<link rel="home" href="../../../index.html" title="Asio">
<link rel="up" href="../core.html" title="Core Concepts and Functionality">
<link rel="prev" href="spawn.html" title="Stackful Coroutines">
<link rel="next" href="../networking.html" title="Networking">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr><td valign="top"><img alt="asio C++ library" width="250" height="60" src="../../../asio.png"></td></tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="spawn.html"><img src="../../../prev.png" alt="Prev"></a><a accesskey="u" href="../core.html"><img src="../../../up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../home.png" alt="Home"></a><a accesskey="n" href="../networking.html"><img src="../../../next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="asio.overview.core.coroutines_ts"></a><a class="link" href="coroutines_ts.html" title="Coroutines TS Support">Coroutines TS Support</a>
</h4></div></div></div>
<p>
          Support for the Coroutines TS is provided via the <a class="link" href="../../reference/awaitable.html" title="awaitable"><code class="computeroutput">awaitable</code></a>
          class template, the <a class="link" href="../../reference/use_awaitable_t.html" title="use_awaitable_t"><code class="computeroutput">use_awaitable</code></a>
          completion token, and the <a class="link" href="../../reference/co_spawn.html" title="co_spawn"><code class="computeroutput">co_spawn()</code></a>
          function. These facilities allow programs to implement asynchronous logic
          in a synchronous manner, in conjunction with the <code class="computeroutput">co_await</code>
          keyword, as shown in the following example:
        </p>
<pre class="programlisting">asio::co_spawn(executor, echo(std::move(socket)), asio::detached);

// ...

asio::awaitable&lt;void&gt; echo(tcp::socket socket)
{
  try
  {
    char data[1024];
    for (;;)
    {
      std::size_t n = co_await socket.async_read_some(asio::buffer(data), asio::use_awaitable);
      co_await async_write(socket, asio::buffer(data, n), asio::use_awaitable);
    }
  }
  catch (std::exception&amp; e)
  {
    std::printf("echo Exception: %s\n", e.what());
  }
}
</pre>
<p>
          The first argument to <code class="computeroutput">co_spawn()</code> is an <a class="link" href="../../reference/Executor1.html" title="Executor requirements">executor</a>
          that determines the context in which the coroutine is permitted to execute.
          For example, a server's per-client object may consist of multiple coroutines;
          they should all run on the same <code class="computeroutput">strand</code> so that no explicit
          synchronisation is required.
        </p>
<p>
          The second argument is an <a class="link" href="../../reference/awaitable.html" title="awaitable"><code class="computeroutput">awaitable&lt;R&gt;</code></a>,
          that is the result of the coroutine's entry point function, and in the
          above example is the result of the call to <code class="computeroutput">echo</code>. (Alternatively,
          this argument can be a function object that returns the <a class="link" href="../../reference/awaitable.html" title="awaitable"><code class="computeroutput">awaitable&lt;R&gt;</code></a>.)
          The template parameter <code class="computeroutput">R</code> is the type of return value produced
          by the coroutine. In the above example, the coroutine returns <code class="computeroutput">void</code>.
        </p>
<p>
          The third argument is a completion token, and this is used by <code class="computeroutput">co_spawn()</code>
          to produce a completion handler with signature <code class="computeroutput">void(std::exception_ptr,
          R)</code>. This completion handler is invoked with the result of the coroutine
          once it has finished. In the above example we pass a completion token type,
          <a class="link" href="../../reference/detached.html" title="detached"><code class="computeroutput">asio::detached</code></a>,
          which is used to explicitly ignore the result of an asynchronous operation.
        </p>
<p>
          In this example the body of the coroutine is implemented in the <code class="computeroutput">echo</code>
          function. When the <code class="computeroutput">use_awaitable</code> completion token is passed
          to an asynchronous operation, the operation's initiating function returns
          an <code class="computeroutput">awaitable</code> that may be used with the <code class="computeroutput">co_await</code>
          keyword:
        </p>
<pre class="programlisting">std::size_t n = co_await socket.async_read_some(asio::buffer(data), asio::use_awaitable);
</pre>
<p>
          Where an asynchronous operation's handler signature has the form:
        </p>
<pre class="programlisting">void handler(asio::error_code ec, result_type result);
</pre>
<p>
          the resulting type of the <code class="computeroutput">co_await</code> expression is <code class="computeroutput">result_type</code>.
          In the <code class="computeroutput">async_read_some</code> example above, this is <code class="computeroutput">size_t</code>.
          If the asynchronous operation fails, the <code class="computeroutput">error_code</code> is converted
          into a <code class="computeroutput">system_error</code> exception and thrown.
        </p>
<p>
          Where a handler signature has the form:
        </p>
<pre class="programlisting">void handler(asio::error_code ec);
</pre>
<p>
          the <code class="computeroutput">co_await</code> expression produces a <code class="computeroutput">void</code> result.
          As above, an error is passed back to the coroutine as a <code class="computeroutput">system_error</code>
          exception.
        </p>
<h6>
<a name="asio.overview.core.coroutines_ts.h0"></a>
          <span><a name="asio.overview.core.coroutines_ts.see_also"></a></span><a class="link" href="coroutines_ts.html#asio.overview.core.coroutines_ts.see_also">See
          Also</a>
        </h6>
<p>
          <a class="link" href="../../reference/co_spawn.html" title="co_spawn">co_spawn</a>, <a class="link" href="../../reference/detached.html" title="detached">detached</a>,
          <a class="link" href="../../reference/redirect_error.html" title="redirect_error">redirect_error</a>, <a class="link" href="../../reference/awaitable.html" title="awaitable">awaitable</a>, <a class="link" href="../../reference/use_awaitable_t.html" title="use_awaitable_t">use_awaitable_t</a>,
          <a class="link" href="../../reference/use_awaitable.html" title="use_awaitable">use_awaitable</a>, <a class="link" href="../../reference/this_coro__executor.html" title="this_coro::executor">this_coro::executor</a>,
          <a class="link" href="../../examples/cpp17_examples.html#asio.examples.cpp17_examples.coroutines_ts_support">Coroutines
          TS examples</a>, <a class="link" href="spawn.html" title="Stackful Coroutines">Stackful Coroutines</a>,
          <a class="link" href="coroutine.html" title="Stackless Coroutines">Stackless Coroutines</a>.
        </p>
</div>
<table xmlns:rev="http://www.cs.rpi.edu/~gregod/boost/tools/doc/revision" width="100%"><tr>
<td align="left"></td>
<td align="right"><div class="copyright-footer">Copyright © 2003-2021 Christopher
      M. Kohlhoff<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="spawn.html"><img src="../../../prev.png" alt="Prev"></a><a accesskey="u" href="../core.html"><img src="../../../up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../home.png" alt="Home"></a><a accesskey="n" href="../networking.html"><img src="../../../next.png" alt="Next"></a>
</div>
</body>
</html>
