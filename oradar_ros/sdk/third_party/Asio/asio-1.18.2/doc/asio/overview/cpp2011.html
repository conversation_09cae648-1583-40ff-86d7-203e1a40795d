<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>C++ 2011 Support</title>
<link rel="stylesheet" href="../../boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.75.2">
<link rel="home" href="../../index.html" title="Asio">
<link rel="up" href="../overview.html" title="Overview">
<link rel="prev" href="ssl.html" title="SSL">
<link rel="next" href="cpp2011/system_error.html" title="System Errors and Error Codes">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr><td valign="top"><img alt="asio C++ library" width="250" height="60" src="../../asio.png"></td></tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="ssl.html"><img src="../../prev.png" alt="Prev"></a><a accesskey="u" href="../overview.html"><img src="../../up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../home.png" alt="Home"></a><a accesskey="n" href="cpp2011/system_error.html"><img src="../../next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="asio.overview.cpp2011"></a><a class="link" href="cpp2011.html" title="C++ 2011 Support">C++ 2011 Support</a>
</h3></div></div></div>
<p>
        <a class="link" href="cpp2011/system_error.html" title="System Errors and Error Codes">System Errors and Error
        Codes</a>
      </p>
<p>
        <a class="link" href="cpp2011/move_objects.html" title="Movable I/O Objects">Movable I/O Objects</a>
      </p>
<p>
        <a class="link" href="cpp2011/move_handlers.html" title="Movable Handlers">Movable Handlers</a>
      </p>
<p>
        <a class="link" href="cpp2011/variadic.html" title="Variadic Templates">Variadic Templates</a>
      </p>
<p>
        <a class="link" href="cpp2011/array.html" title="Array Container">Array Container</a>
      </p>
<p>
        <a class="link" href="cpp2011/atomic.html" title="Atomics">Atomics</a>
      </p>
<p>
        <a class="link" href="cpp2011/shared_ptr.html" title="Shared Pointers">Shared Pointers</a>
      </p>
<p>
        <a class="link" href="cpp2011/chrono.html" title="Chrono">Chrono</a>
      </p>
<p>
        <a class="link" href="cpp2011/futures.html" title="Futures">Futures</a>
      </p>
</div>
<table xmlns:rev="http://www.cs.rpi.edu/~gregod/boost/tools/doc/revision" width="100%"><tr>
<td align="left"></td>
<td align="right"><div class="copyright-footer">Copyright © 2003-2021 Christopher
      M. Kohlhoff<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="ssl.html"><img src="../../prev.png" alt="Prev"></a><a accesskey="u" href="../overview.html"><img src="../../up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../home.png" alt="Home"></a><a accesskey="n" href="cpp2011/system_error.html"><img src="../../next.png" alt="Next"></a>
</div>
</body>
</html>
