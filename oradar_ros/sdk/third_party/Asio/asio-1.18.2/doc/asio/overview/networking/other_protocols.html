<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Support for Other Protocols</title>
<link rel="stylesheet" href="../../../boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.75.2">
<link rel="home" href="../../../index.html" title="Asio">
<link rel="up" href="../networking.html" title="Networking">
<link rel="prev" href="protocols.html" title="TCP, UDP and ICMP">
<link rel="next" href="iostreams.html" title="Socket Iostreams">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr><td valign="top"><img alt="asio C++ library" width="250" height="60" src="../../../asio.png"></td></tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="protocols.html"><img src="../../../prev.png" alt="Prev"></a><a accesskey="u" href="../networking.html"><img src="../../../up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../home.png" alt="Home"></a><a accesskey="n" href="iostreams.html"><img src="../../../next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="asio.overview.networking.other_protocols"></a><a class="link" href="other_protocols.html" title="Support for Other Protocols">Support for
        Other Protocols</a>
</h4></div></div></div>
<p>
          Support for other socket protocols (such as Bluetooth or IRCOMM sockets)
          can be added by implementing the <a class="link" href="../../reference/Protocol.html" title="Protocol requirements">protocol
          type requirements</a>. However, in many cases these protocols may also
          be used with Asio's generic protocol support. For this, Asio provides the
          following four classes:
        </p>
<div class="itemizedlist"><ul class="itemizedlist" type="disc">
<li class="listitem">
              <a class="link" href="../../reference/generic__datagram_protocol.html" title="generic::datagram_protocol"><code class="computeroutput">generic::datagram_protocol</code></a>
            </li>
<li class="listitem">
              <a class="link" href="../../reference/generic__raw_protocol.html" title="generic::raw_protocol"><code class="computeroutput">generic::raw_protocol</code></a>
            </li>
<li class="listitem">
              <a class="link" href="../../reference/generic__seq_packet_protocol.html" title="generic::seq_packet_protocol"><code class="computeroutput">generic::seq_packet_protocol</code></a>
            </li>
<li class="listitem">
              <a class="link" href="../../reference/generic__stream_protocol.html" title="generic::stream_protocol"><code class="computeroutput">generic::stream_protocol</code></a>
            </li>
</ul></div>
<p>
          These classes implement the <a class="link" href="../../reference/Protocol.html" title="Protocol requirements">protocol
          type requirements</a>, but allow the user to specify the address family
          (e.g. <code class="computeroutput">AF_INET</code>) and protocol type (e.g. <code class="computeroutput">IPPROTO_TCP</code>)
          at runtime. For example:
        </p>
<pre class="programlisting">asio::generic::stream_protocol::socket my_socket(my_io_context);
my_socket.open(asio::generic::stream_protocol(AF_INET, IPPROTO_TCP));
...
</pre>
<p>
          An endpoint class template, <a class="link" href="../../reference/generic__basic_endpoint.html" title="generic::basic_endpoint"><code class="computeroutput">asio::generic::basic_endpoint</code></a>,
          is included to support these protocol classes. This endpoint can hold any
          other endpoint type, provided its native representation fits into a <code class="computeroutput">sockaddr_storage</code>
          object. This class will also convert from other types that implement the
          <a class="link" href="../../reference/Endpoint.html" title="Endpoint requirements">endpoint</a> type requirements:
        </p>
<pre class="programlisting">asio::ip::tcp::endpoint my_endpoint1 = ...;
asio::generic::stream_protocol::endpoint my_endpoint2(my_endpoint1);
</pre>
<p>
          The conversion is implicit, so as to support the following use cases:
        </p>
<pre class="programlisting">asio::generic::stream_protocol::socket my_socket(my_io_context);
asio::ip::tcp::endpoint my_endpoint = ...;
my_socket.connect(my_endpoint);
</pre>
<h6>
<a name="asio.overview.networking.other_protocols.h0"></a>
          <span><a name="asio.overview.networking.other_protocols.c__11_move_construction"></a></span><a class="link" href="other_protocols.html#asio.overview.networking.other_protocols.c__11_move_construction">C++11
          Move Construction</a>
        </h6>
<p>
          When using C++11, it is possible to perform move construction from a socket
          (or acceptor) object to convert to the more generic protocol's socket (or
          acceptor) type. If the protocol conversion is valid:
        </p>
<pre class="programlisting">Protocol1 p1 = ...;
Protocol2 p2(p1);
</pre>
<p>
          then the corresponding socket conversion is allowed:
        </p>
<pre class="programlisting">Protocol1::socket my_socket1(my_io_context);
...
Protocol2::socket my_socket2(std::move(my_socket1));
</pre>
<p>
          For example, one possible conversion is from a TCP socket to a generic
          stream-oriented socket:
        </p>
<pre class="programlisting">asio::ip::tcp::socket my_socket1(my_io_context);
...
asio::generic::stream_protocol::socket my_socket2(std::move(my_socket1));
</pre>
<p>
          These conversions are also available for move-assignment.
        </p>
<p>
          These conversions are not limited to the above generic protocol classes.
          User-defined protocols may take advantage of this feature by similarly
          ensuring the conversion from <code class="computeroutput">Protocol1</code> to <code class="computeroutput">Protocol2</code>
          is valid, as above.
        </p>
<h6>
<a name="asio.overview.networking.other_protocols.h1"></a>
          <span><a name="asio.overview.networking.other_protocols.accepting_generic_sockets"></a></span><a class="link" href="other_protocols.html#asio.overview.networking.other_protocols.accepting_generic_sockets">Accepting
          Generic Sockets</a>
        </h6>
<p>
          As a convenience, a socket acceptor's <code class="computeroutput">accept()</code> and <code class="computeroutput">async_accept()</code>
          functions can directly accept into a different protocol's socket type,
          provided the corresponding protocol conversion is valid. For example, the
          following is supported because the protocol <code class="computeroutput">asio::ip::tcp</code>
          is convertible to <code class="computeroutput">asio::generic::stream_protocol</code>:
        </p>
<pre class="programlisting">asio::ip::tcp::acceptor my_acceptor(my_io_context);
...
asio::generic::stream_protocol::socket my_socket(my_io_context);
my_acceptor.accept(my_socket);
</pre>
<h6>
<a name="asio.overview.networking.other_protocols.h2"></a>
          <span><a name="asio.overview.networking.other_protocols.see_also"></a></span><a class="link" href="other_protocols.html#asio.overview.networking.other_protocols.see_also">See
          Also</a>
        </h6>
<p>
          <a class="link" href="../../reference/generic__datagram_protocol.html" title="generic::datagram_protocol"><code class="computeroutput">generic::datagram_protocol</code></a>,
          <a class="link" href="../../reference/generic__raw_protocol.html" title="generic::raw_protocol"><code class="computeroutput">generic::raw_protocol</code></a>,
          <a class="link" href="../../reference/generic__seq_packet_protocol.html" title="generic::seq_packet_protocol"><code class="computeroutput">generic::seq_packet_protocol</code></a>,
          <a class="link" href="../../reference/generic__stream_protocol.html" title="generic::stream_protocol"><code class="computeroutput">generic::stream_protocol</code></a>,
          <a class="link" href="../../reference/Protocol.html" title="Protocol requirements">protocol type requirements</a>.
        </p>
</div>
<table xmlns:rev="http://www.cs.rpi.edu/~gregod/boost/tools/doc/revision" width="100%"><tr>
<td align="left"></td>
<td align="right"><div class="copyright-footer">Copyright © 2003-2021 Christopher
      M. Kohlhoff<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="protocols.html"><img src="../../../prev.png" alt="Prev"></a><a accesskey="u" href="../networking.html"><img src="../../../up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../home.png" alt="Home"></a><a accesskey="n" href="iostreams.html"><img src="../../../next.png" alt="Next"></a>
</div>
</body>
</html>
