<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Chrono</title>
<link rel="stylesheet" href="../../../boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.75.2">
<link rel="home" href="../../../index.html" title="Asio">
<link rel="up" href="../cpp2011.html" title="C++ 2011 Support">
<link rel="prev" href="shared_ptr.html" title="Shared Pointers">
<link rel="next" href="futures.html" title="Futures">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr><td valign="top"><img alt="asio C++ library" width="250" height="60" src="../../../asio.png"></td></tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="shared_ptr.html"><img src="../../../prev.png" alt="Prev"></a><a accesskey="u" href="../cpp2011.html"><img src="../../../up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../home.png" alt="Home"></a><a accesskey="n" href="futures.html"><img src="../../../next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="asio.overview.cpp2011.chrono"></a><a class="link" href="chrono.html" title="Chrono">Chrono</a>
</h4></div></div></div>
<p>
          Asio provides timers based on the <code class="computeroutput">std::chrono</code> facilities via
          the <a class="link" href="../../reference/basic_waitable_timer.html" title="basic_waitable_timer">basic_waitable_timer</a>
          class template. The typedefs <a class="link" href="../../reference/system_timer.html" title="system_timer">system_timer</a>,
          <a class="link" href="../../reference/steady_timer.html" title="steady_timer">steady_timer</a> and <a class="link" href="../../reference/high_resolution_timer.html" title="high_resolution_timer">high_resolution_timer</a>
          utilise the standard clocks <code class="computeroutput">system_clock</code>, <code class="computeroutput">steady_clock</code>
          and <code class="computeroutput">high_resolution_clock</code> respectively.
        </p>
<p>
          Support for the <code class="computeroutput">std::chrono</code> facilities is automatically enabled
          for <code class="literal">g++</code> 4.6 and later, when the <code class="literal">-std=c++0x</code>
          or <code class="literal">-std=gnu++0x</code> compiler options are used. (Note that,
          for <code class="literal">g++</code>, the draft-standard <code class="computeroutput">monotonic_clock</code>
          is used in place of <code class="computeroutput">steady_clock</code>.) Support may be disabled
          by defining <code class="computeroutput">ASIO_DISABLE_STD_CHRONO</code>, or explicitly enabled
          for other compilers by defining <code class="computeroutput">ASIO_HAS_STD_CHRONO</code>.
        </p>
<p>
          When standard <code class="computeroutput">chrono</code> is unavailable, Asio will otherwise use
          the Boost.Chrono library. The <a class="link" href="../../reference/basic_waitable_timer.html" title="basic_waitable_timer">basic_waitable_timer</a>
          class template may be used with either.
        </p>
</div>
<table xmlns:rev="http://www.cs.rpi.edu/~gregod/boost/tools/doc/revision" width="100%"><tr>
<td align="left"></td>
<td align="right"><div class="copyright-footer">Copyright © 2003-2021 Christopher
      M. Kohlhoff<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="shared_ptr.html"><img src="../../../prev.png" alt="Prev"></a><a accesskey="u" href="../cpp2011.html"><img src="../../../up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../home.png" alt="Home"></a><a accesskey="n" href="futures.html"><img src="../../../next.png" alt="Next"></a>
</div>
</body>
</html>
