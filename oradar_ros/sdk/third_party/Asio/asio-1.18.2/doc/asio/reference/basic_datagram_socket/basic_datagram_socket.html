<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>basic_datagram_socket::basic_datagram_socket</title>
<link rel="stylesheet" href="../../../boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.75.2">
<link rel="home" href="../../../index.html" title="Asio">
<link rel="up" href="../basic_datagram_socket.html" title="basic_datagram_socket">
<link rel="prev" href="available/overload2.html" title="basic_datagram_socket::available (2 of 2 overloads)">
<link rel="next" href="basic_datagram_socket/overload1.html" title="basic_datagram_socket::basic_datagram_socket (1 of 10 overloads)">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr><td valign="top"><img alt="asio C++ library" width="250" height="60" src="../../../asio.png"></td></tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="available/overload2.html"><img src="../../../prev.png" alt="Prev"></a><a accesskey="u" href="../basic_datagram_socket.html"><img src="../../../up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../home.png" alt="Home"></a><a accesskey="n" href="basic_datagram_socket/overload1.html"><img src="../../../next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="asio.reference.basic_datagram_socket.basic_datagram_socket"></a><a class="link" href="basic_datagram_socket.html" title="basic_datagram_socket::basic_datagram_socket">basic_datagram_socket::basic_datagram_socket</a>
</h4></div></div></div>
<p>
          <a class="indexterm" name="asio.indexterm.basic_datagram_socket.basic_datagram_socket"></a> 
Construct
          a <a class="link" href="../basic_datagram_socket.html" title="basic_datagram_socket"><code class="computeroutput">basic_datagram_socket</code></a>
          without opening it.
        </p>
<pre class="programlisting">explicit <a class="link" href="basic_datagram_socket/overload1.html" title="basic_datagram_socket::basic_datagram_socket (1 of 10 overloads)">basic_datagram_socket</a>(
    const executor_type &amp; ex);
  <span class="emphasis"><em>» <a class="link" href="basic_datagram_socket/overload1.html" title="basic_datagram_socket::basic_datagram_socket (1 of 10 overloads)">more...</a></em></span>

template&lt;
    typename ExecutionContext&gt;
explicit <a class="link" href="basic_datagram_socket/overload2.html" title="basic_datagram_socket::basic_datagram_socket (2 of 10 overloads)">basic_datagram_socket</a>(
    ExecutionContext &amp; context,
    typename constraint&lt; is_convertible&lt; ExecutionContext &amp;, execution_context &amp; &gt;::value &gt;::type  = 0);
  <span class="emphasis"><em>» <a class="link" href="basic_datagram_socket/overload2.html" title="basic_datagram_socket::basic_datagram_socket (2 of 10 overloads)">more...</a></em></span>
</pre>
<p>
          Construct and open a <a class="link" href="../basic_datagram_socket.html" title="basic_datagram_socket"><code class="computeroutput">basic_datagram_socket</code></a>.
        </p>
<pre class="programlisting"><a class="link" href="basic_datagram_socket/overload3.html" title="basic_datagram_socket::basic_datagram_socket (3 of 10 overloads)">basic_datagram_socket</a>(
    const executor_type &amp; ex,
    const protocol_type &amp; protocol);
  <span class="emphasis"><em>» <a class="link" href="basic_datagram_socket/overload3.html" title="basic_datagram_socket::basic_datagram_socket (3 of 10 overloads)">more...</a></em></span>

template&lt;
    typename ExecutionContext&gt;
<a class="link" href="basic_datagram_socket/overload4.html" title="basic_datagram_socket::basic_datagram_socket (4 of 10 overloads)">basic_datagram_socket</a>(
    ExecutionContext &amp; context,
    const protocol_type &amp; protocol,
    typename constraint&lt; is_convertible&lt; ExecutionContext &amp;, execution_context &amp; &gt;::value, defaulted_constraint &gt;::type  = defaulted_constraint());
  <span class="emphasis"><em>» <a class="link" href="basic_datagram_socket/overload4.html" title="basic_datagram_socket::basic_datagram_socket (4 of 10 overloads)">more...</a></em></span>
</pre>
<p>
          Construct a <a class="link" href="../basic_datagram_socket.html" title="basic_datagram_socket"><code class="computeroutput">basic_datagram_socket</code></a>,
          opening it and binding it to the given local endpoint.
        </p>
<pre class="programlisting"><a class="link" href="basic_datagram_socket/overload5.html" title="basic_datagram_socket::basic_datagram_socket (5 of 10 overloads)">basic_datagram_socket</a>(
    const executor_type &amp; ex,
    const endpoint_type &amp; endpoint);
  <span class="emphasis"><em>» <a class="link" href="basic_datagram_socket/overload5.html" title="basic_datagram_socket::basic_datagram_socket (5 of 10 overloads)">more...</a></em></span>

template&lt;
    typename ExecutionContext&gt;
<a class="link" href="basic_datagram_socket/overload6.html" title="basic_datagram_socket::basic_datagram_socket (6 of 10 overloads)">basic_datagram_socket</a>(
    ExecutionContext &amp; context,
    const endpoint_type &amp; endpoint,
    typename constraint&lt; is_convertible&lt; ExecutionContext &amp;, execution_context &amp; &gt;::value &gt;::type  = 0);
  <span class="emphasis"><em>» <a class="link" href="basic_datagram_socket/overload6.html" title="basic_datagram_socket::basic_datagram_socket (6 of 10 overloads)">more...</a></em></span>
</pre>
<p>
          Construct a <a class="link" href="../basic_datagram_socket.html" title="basic_datagram_socket"><code class="computeroutput">basic_datagram_socket</code></a>
          on an existing native socket.
        </p>
<pre class="programlisting"><a class="link" href="basic_datagram_socket/overload7.html" title="basic_datagram_socket::basic_datagram_socket (7 of 10 overloads)">basic_datagram_socket</a>(
    const executor_type &amp; ex,
    const protocol_type &amp; protocol,
    const native_handle_type &amp; native_socket);
  <span class="emphasis"><em>» <a class="link" href="basic_datagram_socket/overload7.html" title="basic_datagram_socket::basic_datagram_socket (7 of 10 overloads)">more...</a></em></span>

template&lt;
    typename ExecutionContext&gt;
<a class="link" href="basic_datagram_socket/overload8.html" title="basic_datagram_socket::basic_datagram_socket (8 of 10 overloads)">basic_datagram_socket</a>(
    ExecutionContext &amp; context,
    const protocol_type &amp; protocol,
    const native_handle_type &amp; native_socket,
    typename constraint&lt; is_convertible&lt; ExecutionContext &amp;, execution_context &amp; &gt;::value &gt;::type  = 0);
  <span class="emphasis"><em>» <a class="link" href="basic_datagram_socket/overload8.html" title="basic_datagram_socket::basic_datagram_socket (8 of 10 overloads)">more...</a></em></span>
</pre>
<p>
          Move-construct a <a class="link" href="../basic_datagram_socket.html" title="basic_datagram_socket"><code class="computeroutput">basic_datagram_socket</code></a>
          from another.
        </p>
<pre class="programlisting"><a class="link" href="basic_datagram_socket/overload9.html" title="basic_datagram_socket::basic_datagram_socket (9 of 10 overloads)">basic_datagram_socket</a>(
    basic_datagram_socket &amp;&amp; other);
  <span class="emphasis"><em>» <a class="link" href="basic_datagram_socket/overload9.html" title="basic_datagram_socket::basic_datagram_socket (9 of 10 overloads)">more...</a></em></span>
</pre>
<p>
          Move-construct a <a class="link" href="../basic_datagram_socket.html" title="basic_datagram_socket"><code class="computeroutput">basic_datagram_socket</code></a>
          from a socket of another protocol type.
        </p>
<pre class="programlisting">template&lt;
    typename <a class="link" href="../Protocol.html" title="Protocol requirements">Protocol1</a>,
    typename <a class="link" href="../Executor1.html" title="Executor requirements">Executor1</a>&gt;
<a class="link" href="basic_datagram_socket/overload10.html" title="basic_datagram_socket::basic_datagram_socket (10 of 10 overloads)">basic_datagram_socket</a>(
    basic_datagram_socket&lt; Protocol1, Executor1 &gt; &amp;&amp; other,
    typename constraint&lt; is_convertible&lt; Protocol1, Protocol &gt;::value &amp;&amp;is_convertible&lt; Executor1, Executor &gt;::value &gt;::type  = 0);
  <span class="emphasis"><em>» <a class="link" href="basic_datagram_socket/overload10.html" title="basic_datagram_socket::basic_datagram_socket (10 of 10 overloads)">more...</a></em></span>
</pre>
</div>
<table xmlns:rev="http://www.cs.rpi.edu/~gregod/boost/tools/doc/revision" width="100%"><tr>
<td align="left"></td>
<td align="right"><div class="copyright-footer">Copyright © 2003-2021 Christopher
      M. Kohlhoff<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="available/overload2.html"><img src="../../../prev.png" alt="Prev"></a><a accesskey="u" href="../basic_datagram_socket.html"><img src="../../../up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../home.png" alt="Home"></a><a accesskey="n" href="basic_datagram_socket/overload1.html"><img src="../../../next.png" alt="Next"></a>
</div>
</body>
</html>
