<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>basic_io_object::get_io_context</title>
<link rel="stylesheet" href="../../../boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.75.2">
<link rel="home" href="../../../index.html" title="Asio">
<link rel="up" href="../basic_io_object.html" title="basic_io_object">
<link rel="prev" href="get_implementation/overload2.html" title="basic_io_object::get_implementation (2 of 2 overloads)">
<link rel="next" href="get_io_service.html" title="basic_io_object::get_io_service">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr><td valign="top"><img alt="asio C++ library" width="250" height="60" src="../../../asio.png"></td></tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="get_implementation/overload2.html"><img src="../../../prev.png" alt="Prev"></a><a accesskey="u" href="../basic_io_object.html"><img src="../../../up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../home.png" alt="Home"></a><a accesskey="n" href="get_io_service.html"><img src="../../../next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="asio.reference.basic_io_object.get_io_context"></a><a class="link" href="get_io_context.html" title="basic_io_object::get_io_context">basic_io_object::get_io_context</a>
</h4></div></div></div>
<p>
          <a class="indexterm" name="asio.indexterm.basic_io_object.get_io_context"></a> 
(Deprecated:
          Use <code class="computeroutput">get_executor()</code>.) Get the <a class="link" href="../io_context.html" title="io_context"><code class="computeroutput">io_context</code></a>
          associated with the object.
        </p>
<pre class="programlisting">asio::io_context &amp; get_io_context();
</pre>
<p>
          This function may be used to obtain the <a class="link" href="../io_context.html" title="io_context"><code class="computeroutput">io_context</code></a>
          object that the I/O object uses to dispatch handlers for asynchronous operations.
        </p>
<h6>
<a name="asio.reference.basic_io_object.get_io_context.h0"></a>
          <span><a name="asio.reference.basic_io_object.get_io_context.return_value"></a></span><a class="link" href="get_io_context.html#asio.reference.basic_io_object.get_io_context.return_value">Return
          Value</a>
        </h6>
<p>
          A reference to the <a class="link" href="../io_context.html" title="io_context"><code class="computeroutput">io_context</code></a>
          object that the I/O object will use to dispatch handlers. Ownership is
          not transferred to the caller.
        </p>
</div>
<table xmlns:rev="http://www.cs.rpi.edu/~gregod/boost/tools/doc/revision" width="100%"><tr>
<td align="left"></td>
<td align="right"><div class="copyright-footer">Copyright © 2003-2021 Christopher
      M. Kohlhoff<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="get_implementation/overload2.html"><img src="../../../prev.png" alt="Prev"></a><a accesskey="u" href="../basic_io_object.html"><img src="../../../up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../home.png" alt="Home"></a><a accesskey="n" href="get_io_service.html"><img src="../../../next.png" alt="Next"></a>
</div>
</body>
</html>
