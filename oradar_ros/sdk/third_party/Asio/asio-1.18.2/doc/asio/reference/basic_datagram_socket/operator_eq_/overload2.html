<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>basic_datagram_socket::operator= (2 of 2 overloads)</title>
<link rel="stylesheet" href="../../../../boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.75.2">
<link rel="home" href="../../../../index.html" title="Asio">
<link rel="up" href="../operator_eq_.html" title="basic_datagram_socket::operator=">
<link rel="prev" href="overload1.html" title="basic_datagram_socket::operator= (1 of 2 overloads)">
<link rel="next" href="../out_of_band_inline.html" title="basic_datagram_socket::out_of_band_inline">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr><td valign="top"><img alt="asio C++ library" width="250" height="60" src="../../../../asio.png"></td></tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="overload1.html"><img src="../../../../prev.png" alt="Prev"></a><a accesskey="u" href="../operator_eq_.html"><img src="../../../../up.png" alt="Up"></a><a accesskey="h" href="../../../../index.html"><img src="../../../../home.png" alt="Home"></a><a accesskey="n" href="../out_of_band_inline.html"><img src="../../../../next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h5 class="title">
<a name="asio.reference.basic_datagram_socket.operator_eq_.overload2"></a><a class="link" href="overload2.html" title="basic_datagram_socket::operator= (2 of 2 overloads)">basic_datagram_socket::operator=
          (2 of 2 overloads)</a>
</h5></div></div></div>
<p>
            Move-assign a <a class="link" href="../../basic_datagram_socket.html" title="basic_datagram_socket"><code class="computeroutput">basic_datagram_socket</code></a>
            from a socket of another protocol type.
          </p>
<pre class="programlisting">template&lt;
    typename <a class="link" href="../../Protocol.html" title="Protocol requirements">Protocol1</a>,
    typename <a class="link" href="../../Executor1.html" title="Executor requirements">Executor1</a>&gt;
constraint&lt; is_convertible&lt; Protocol1, Protocol &gt;::value &amp;&amp;is_convertible&lt; Executor1, Executor &gt;::value, basic_datagram_socket &amp; &gt;::type operator=(
    basic_datagram_socket&lt; Protocol1, Executor1 &gt; &amp;&amp; other);
</pre>
<p>
            This assignment operator moves a datagram socket from one object to another.
          </p>
<h6>
<a name="asio.reference.basic_datagram_socket.operator_eq_.overload2.h0"></a>
            <span><a name="asio.reference.basic_datagram_socket.operator_eq_.overload2.parameters"></a></span><a class="link" href="overload2.html#asio.reference.basic_datagram_socket.operator_eq_.overload2.parameters">Parameters</a>
          </h6>
<div class="variablelist">
<p class="title"><b></b></p>
<dl>
<dt><span class="term">other</span></dt>
<dd><p>
                  The other <a class="link" href="../../basic_datagram_socket.html" title="basic_datagram_socket"><code class="computeroutput">basic_datagram_socket</code></a>
                  object from which the move will occur.
                </p></dd>
</dl>
</div>
<h6>
<a name="asio.reference.basic_datagram_socket.operator_eq_.overload2.h1"></a>
            <span><a name="asio.reference.basic_datagram_socket.operator_eq_.overload2.remarks"></a></span><a class="link" href="overload2.html#asio.reference.basic_datagram_socket.operator_eq_.overload2.remarks">Remarks</a>
          </h6>
<p>
            Following the move, the moved-from object is in the same state as if
            constructed using the <code class="computeroutput">basic_datagram_socket(const executor_type&amp;)</code>
            constructor.
          </p>
</div>
<table xmlns:rev="http://www.cs.rpi.edu/~gregod/boost/tools/doc/revision" width="100%"><tr>
<td align="left"></td>
<td align="right"><div class="copyright-footer">Copyright © 2003-2021 Christopher
      M. Kohlhoff<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="overload1.html"><img src="../../../../prev.png" alt="Prev"></a><a accesskey="u" href="../operator_eq_.html"><img src="../../../../up.png" alt="Up"></a><a accesskey="h" href="../../../../index.html"><img src="../../../../home.png" alt="Home"></a><a accesskey="n" href="../out_of_band_inline.html"><img src="../../../../next.png" alt="Next"></a>
</div>
</body>
</html>
