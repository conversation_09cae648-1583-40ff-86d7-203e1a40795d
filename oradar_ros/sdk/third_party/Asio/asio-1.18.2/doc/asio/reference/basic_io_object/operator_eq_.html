<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>basic_io_object::operator=</title>
<link rel="stylesheet" href="../../../boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.75.2">
<link rel="home" href="../../../index.html" title="Asio">
<link rel="up" href="../basic_io_object.html" title="basic_io_object">
<link rel="prev" href="implementation_type.html" title="basic_io_object::implementation_type">
<link rel="next" href="service_type.html" title="basic_io_object::service_type">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr><td valign="top"><img alt="asio C++ library" width="250" height="60" src="../../../asio.png"></td></tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="implementation_type.html"><img src="../../../prev.png" alt="Prev"></a><a accesskey="u" href="../basic_io_object.html"><img src="../../../up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../home.png" alt="Home"></a><a accesskey="n" href="service_type.html"><img src="../../../next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="asio.reference.basic_io_object.operator_eq_"></a><a class="link" href="operator_eq_.html" title="basic_io_object::operator=">basic_io_object::operator=</a>
</h4></div></div></div>
<p>
          <a class="indexterm" name="asio.indexterm.basic_io_object.operator_eq_"></a> 
Move-assign
          a <a class="link" href="../basic_io_object.html" title="basic_io_object"><code class="computeroutput">basic_io_object</code></a>.
        </p>
<pre class="programlisting">basic_io_object &amp; operator=(
    basic_io_object &amp;&amp; other);
</pre>
<p>
          Performs:
        </p>
<pre class="programlisting">get_service().move_assign(get_implementation(),
    other.get_service(), other.get_implementation());
</pre>
<h6>
<a name="asio.reference.basic_io_object.operator_eq_.h0"></a>
          <span><a name="asio.reference.basic_io_object.operator_eq_.remarks"></a></span><a class="link" href="operator_eq_.html#asio.reference.basic_io_object.operator_eq_.remarks">Remarks</a>
        </h6>
<p>
          Available only for services that support movability,
        </p>
</div>
<table xmlns:rev="http://www.cs.rpi.edu/~gregod/boost/tools/doc/revision" width="100%"><tr>
<td align="left"></td>
<td align="right"><div class="copyright-footer">Copyright © 2003-2021 Christopher
      M. Kohlhoff<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="implementation_type.html"><img src="../../../prev.png" alt="Prev"></a><a accesskey="u" href="../basic_io_object.html"><img src="../../../up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../home.png" alt="Home"></a><a accesskey="n" href="service_type.html"><img src="../../../next.png" alt="Next"></a>
</div>
</body>
</html>
