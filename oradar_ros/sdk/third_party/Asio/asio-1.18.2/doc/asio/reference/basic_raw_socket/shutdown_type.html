<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>basic_raw_socket::shutdown_type</title>
<link rel="stylesheet" href="../../../boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.75.2">
<link rel="home" href="../../../index.html" title="Asio">
<link rel="up" href="../basic_raw_socket.html" title="basic_raw_socket">
<link rel="prev" href="shutdown/overload2.html" title="basic_raw_socket::shutdown (2 of 2 overloads)">
<link rel="next" href="wait.html" title="basic_raw_socket::wait">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr><td valign="top"><img alt="asio C++ library" width="250" height="60" src="../../../asio.png"></td></tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="shutdown/overload2.html"><img src="../../../prev.png" alt="Prev"></a><a accesskey="u" href="../basic_raw_socket.html"><img src="../../../up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../home.png" alt="Home"></a><a accesskey="n" href="wait.html"><img src="../../../next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="asio.reference.basic_raw_socket.shutdown_type"></a><a class="link" href="shutdown_type.html" title="basic_raw_socket::shutdown_type">basic_raw_socket::shutdown_type</a>
</h4></div></div></div>
<p>
          <span class="emphasis"><em>Inherited from socket_base.</em></span>
        </p>
<p>
          <a class="indexterm" name="asio.indexterm.basic_raw_socket.shutdown_type"></a> 
Different
          ways a socket may be shutdown.
        </p>
<pre class="programlisting">enum shutdown_type
</pre>
<p>
          <a class="indexterm" name="asio.indexterm.basic_raw_socket.shutdown_type.shutdown_receive"></a>
 <a class="indexterm" name="asio.indexterm.basic_raw_socket.shutdown_type.shutdown_send"></a>
 <a class="indexterm" name="asio.indexterm.basic_raw_socket.shutdown_type.shutdown_both"></a>
        </p>
<h6>
<a name="asio.reference.basic_raw_socket.shutdown_type.h0"></a>
          <span><a name="asio.reference.basic_raw_socket.shutdown_type.values"></a></span><a class="link" href="shutdown_type.html#asio.reference.basic_raw_socket.shutdown_type.values">Values</a>
        </h6>
<div class="variablelist">
<p class="title"><b></b></p>
<dl>
<dt><span class="term">shutdown_receive</span></dt>
<dd><p>
                Shutdown the receive side of the socket.
              </p></dd>
<dt><span class="term">shutdown_send</span></dt>
<dd><p>
                Shutdown the send side of the socket.
              </p></dd>
<dt><span class="term">shutdown_both</span></dt>
<dd><p>
                Shutdown both send and receive on the socket.
              </p></dd>
</dl>
</div>
</div>
<table xmlns:rev="http://www.cs.rpi.edu/~gregod/boost/tools/doc/revision" width="100%"><tr>
<td align="left"></td>
<td align="right"><div class="copyright-footer">Copyright © 2003-2021 Christopher
      M. Kohlhoff<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="shutdown/overload2.html"><img src="../../../prev.png" alt="Prev"></a><a accesskey="u" href="../basic_raw_socket.html"><img src="../../../up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../home.png" alt="Home"></a><a accesskey="n" href="wait.html"><img src="../../../next.png" alt="Next"></a>
</div>
</body>
</html>
