<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>basic_deadline_timer::operator=</title>
<link rel="stylesheet" href="../../../boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.75.2">
<link rel="home" href="../../../index.html" title="Asio">
<link rel="up" href="../basic_deadline_timer.html" title="basic_deadline_timer">
<link rel="prev" href="get_executor.html" title="basic_deadline_timer::get_executor">
<link rel="next" href="time_type.html" title="basic_deadline_timer::time_type">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr><td valign="top"><img alt="asio C++ library" width="250" height="60" src="../../../asio.png"></td></tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="get_executor.html"><img src="../../../prev.png" alt="Prev"></a><a accesskey="u" href="../basic_deadline_timer.html"><img src="../../../up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../home.png" alt="Home"></a><a accesskey="n" href="time_type.html"><img src="../../../next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="asio.reference.basic_deadline_timer.operator_eq_"></a><a class="link" href="operator_eq_.html" title="basic_deadline_timer::operator=">basic_deadline_timer::operator=</a>
</h4></div></div></div>
<p>
          <a class="indexterm" name="asio.indexterm.basic_deadline_timer.operator_eq_"></a> 
Move-assign
          a <a class="link" href="../basic_deadline_timer.html" title="basic_deadline_timer"><code class="computeroutput">basic_deadline_timer</code></a>
          from another.
        </p>
<pre class="programlisting">basic_deadline_timer &amp; operator=(
    basic_deadline_timer &amp;&amp; other);
</pre>
<p>
          This assignment operator moves a timer from one object to another. Cancels
          any outstanding asynchronous operations associated with the target object.
        </p>
<h6>
<a name="asio.reference.basic_deadline_timer.operator_eq_.h0"></a>
          <span><a name="asio.reference.basic_deadline_timer.operator_eq_.parameters"></a></span><a class="link" href="operator_eq_.html#asio.reference.basic_deadline_timer.operator_eq_.parameters">Parameters</a>
        </h6>
<div class="variablelist">
<p class="title"><b></b></p>
<dl>
<dt><span class="term">other</span></dt>
<dd><p>
                The other <a class="link" href="../basic_deadline_timer.html" title="basic_deadline_timer"><code class="computeroutput">basic_deadline_timer</code></a>
                object from which the move will occur.
              </p></dd>
</dl>
</div>
<h6>
<a name="asio.reference.basic_deadline_timer.operator_eq_.h1"></a>
          <span><a name="asio.reference.basic_deadline_timer.operator_eq_.remarks"></a></span><a class="link" href="operator_eq_.html#asio.reference.basic_deadline_timer.operator_eq_.remarks">Remarks</a>
        </h6>
<p>
          Following the move, the moved-from object is in the same state as if constructed
          using the <code class="computeroutput">basic_deadline_timer(const executor_type&amp;)</code> constructor.
        </p>
</div>
<table xmlns:rev="http://www.cs.rpi.edu/~gregod/boost/tools/doc/revision" width="100%"><tr>
<td align="left"></td>
<td align="right"><div class="copyright-footer">Copyright © 2003-2021 Christopher
      M. Kohlhoff<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="get_executor.html"><img src="../../../prev.png" alt="Prev"></a><a accesskey="u" href="../basic_deadline_timer.html"><img src="../../../up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../home.png" alt="Home"></a><a accesskey="n" href="time_type.html"><img src="../../../next.png" alt="Next"></a>
</div>
</body>
</html>
