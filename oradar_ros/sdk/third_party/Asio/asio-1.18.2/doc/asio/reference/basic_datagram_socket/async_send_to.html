<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>basic_datagram_socket::async_send_to</title>
<link rel="stylesheet" href="../../../boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.75.2">
<link rel="home" href="../../../index.html" title="Asio">
<link rel="up" href="../basic_datagram_socket.html" title="basic_datagram_socket">
<link rel="prev" href="async_send/overload2.html" title="basic_datagram_socket::async_send (2 of 2 overloads)">
<link rel="next" href="async_send_to/overload1.html" title="basic_datagram_socket::async_send_to (1 of 2 overloads)">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr><td valign="top"><img alt="asio C++ library" width="250" height="60" src="../../../asio.png"></td></tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="async_send/overload2.html"><img src="../../../prev.png" alt="Prev"></a><a accesskey="u" href="../basic_datagram_socket.html"><img src="../../../up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../home.png" alt="Home"></a><a accesskey="n" href="async_send_to/overload1.html"><img src="../../../next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="asio.reference.basic_datagram_socket.async_send_to"></a><a class="link" href="async_send_to.html" title="basic_datagram_socket::async_send_to">basic_datagram_socket::async_send_to</a>
</h4></div></div></div>
<p>
          <a class="indexterm" name="asio.indexterm.basic_datagram_socket.async_send_to"></a> 
Start
          an asynchronous send.
        </p>
<pre class="programlisting">template&lt;
    typename <a class="link" href="../ConstBufferSequence.html" title="Constant buffer sequence requirements">ConstBufferSequence</a>,
    typename <a class="link" href="../WriteHandler.html" title="Write handler requirements">WriteHandler</a> = <a class="link" href="../asynchronous_operations.html#asio.reference.asynchronous_operations.default_completion_tokens"><span class="emphasis"><em>DEFAULT</em></span></a>&gt;
<a class="link" href="../asynchronous_operations.html#asio.reference.asynchronous_operations.automatic_deduction_of_initiating_function_return_type"><span class="emphasis"><em>DEDUCED</em></span></a> <a class="link" href="async_send_to/overload1.html" title="basic_datagram_socket::async_send_to (1 of 2 overloads)">async_send_to</a>(
    const ConstBufferSequence &amp; buffers,
    const endpoint_type &amp; destination,
    WriteHandler &amp;&amp; handler = <a class="link" href="../asynchronous_operations.html#asio.reference.asynchronous_operations.default_completion_tokens"><span class="emphasis"><em>DEFAULT</em></span></a>);
  <span class="emphasis"><em>» <a class="link" href="async_send_to/overload1.html" title="basic_datagram_socket::async_send_to (1 of 2 overloads)">more...</a></em></span>

template&lt;
    typename <a class="link" href="../ConstBufferSequence.html" title="Constant buffer sequence requirements">ConstBufferSequence</a>,
    typename <a class="link" href="../WriteHandler.html" title="Write handler requirements">WriteHandler</a> = <a class="link" href="../asynchronous_operations.html#asio.reference.asynchronous_operations.default_completion_tokens"><span class="emphasis"><em>DEFAULT</em></span></a>&gt;
<a class="link" href="../asynchronous_operations.html#asio.reference.asynchronous_operations.automatic_deduction_of_initiating_function_return_type"><span class="emphasis"><em>DEDUCED</em></span></a> <a class="link" href="async_send_to/overload2.html" title="basic_datagram_socket::async_send_to (2 of 2 overloads)">async_send_to</a>(
    const ConstBufferSequence &amp; buffers,
    const endpoint_type &amp; destination,
    socket_base::message_flags flags,
    WriteHandler &amp;&amp; handler = <a class="link" href="../asynchronous_operations.html#asio.reference.asynchronous_operations.default_completion_tokens"><span class="emphasis"><em>DEFAULT</em></span></a>);
  <span class="emphasis"><em>» <a class="link" href="async_send_to/overload2.html" title="basic_datagram_socket::async_send_to (2 of 2 overloads)">more...</a></em></span>
</pre>
</div>
<table xmlns:rev="http://www.cs.rpi.edu/~gregod/boost/tools/doc/revision" width="100%"><tr>
<td align="left"></td>
<td align="right"><div class="copyright-footer">Copyright © 2003-2021 Christopher
      M. Kohlhoff<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="async_send/overload2.html"><img src="../../../prev.png" alt="Prev"></a><a accesskey="u" href="../basic_datagram_socket.html"><img src="../../../up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../home.png" alt="Home"></a><a accesskey="n" href="async_send_to/overload1.html"><img src="../../../next.png" alt="Next"></a>
</div>
</body>
</html>
