<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>asio_handler_invoke (1 of 2 overloads)</title>
<link rel="stylesheet" href="../../../boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.75.2">
<link rel="home" href="../../../index.html" title="Asio">
<link rel="up" href="../asio_handler_invoke.html" title="asio_handler_invoke">
<link rel="prev" href="../asio_handler_invoke.html" title="asio_handler_invoke">
<link rel="next" href="overload2.html" title="asio_handler_invoke (2 of 2 overloads)">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr><td valign="top"><img alt="asio C++ library" width="250" height="60" src="../../../asio.png"></td></tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="../asio_handler_invoke.html"><img src="../../../prev.png" alt="Prev"></a><a accesskey="u" href="../asio_handler_invoke.html"><img src="../../../up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../home.png" alt="Home"></a><a accesskey="n" href="overload2.html"><img src="../../../next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="asio.reference.asio_handler_invoke.overload1"></a><a class="link" href="overload1.html" title="asio_handler_invoke (1 of 2 overloads)">asio_handler_invoke
        (1 of 2 overloads)</a>
</h4></div></div></div>
<p>
          Default handler invocation hook used for non-const function objects.
        </p>
<pre class="programlisting">template&lt;
    typename Function&gt;
asio_handler_invoke_is_deprecated asio_handler_invoke(
    Function &amp; function,
    ... );
</pre>
</div>
<table xmlns:rev="http://www.cs.rpi.edu/~gregod/boost/tools/doc/revision" width="100%"><tr>
<td align="left"></td>
<td align="right"><div class="copyright-footer">Copyright © 2003-2021 Christopher
      M. Kohlhoff<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="../asio_handler_invoke.html"><img src="../../../prev.png" alt="Prev"></a><a accesskey="u" href="../asio_handler_invoke.html"><img src="../../../up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../home.png" alt="Home"></a><a accesskey="n" href="overload2.html"><img src="../../../next.png" alt="Next"></a>
</div>
</body>
</html>
