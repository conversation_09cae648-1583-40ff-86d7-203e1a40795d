<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>basic_deadline_timer::expires_at (1 of 3 overloads)</title>
<link rel="stylesheet" href="../../../../boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.75.2">
<link rel="home" href="../../../../index.html" title="Asio">
<link rel="up" href="../expires_at.html" title="basic_deadline_timer::expires_at">
<link rel="prev" href="../expires_at.html" title="basic_deadline_timer::expires_at">
<link rel="next" href="overload2.html" title="basic_deadline_timer::expires_at (2 of 3 overloads)">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr><td valign="top"><img alt="asio C++ library" width="250" height="60" src="../../../../asio.png"></td></tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="../expires_at.html"><img src="../../../../prev.png" alt="Prev"></a><a accesskey="u" href="../expires_at.html"><img src="../../../../up.png" alt="Up"></a><a accesskey="h" href="../../../../index.html"><img src="../../../../home.png" alt="Home"></a><a accesskey="n" href="overload2.html"><img src="../../../../next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h5 class="title">
<a name="asio.reference.basic_deadline_timer.expires_at.overload1"></a><a class="link" href="overload1.html" title="basic_deadline_timer::expires_at (1 of 3 overloads)">basic_deadline_timer::expires_at
          (1 of 3 overloads)</a>
</h5></div></div></div>
<p>
            Get the timer's expiry time as an absolute time.
          </p>
<pre class="programlisting">time_type expires_at() const;
</pre>
<p>
            This function may be used to obtain the timer's current expiry time.
            Whether the timer has expired or not does not affect this value.
          </p>
</div>
<table xmlns:rev="http://www.cs.rpi.edu/~gregod/boost/tools/doc/revision" width="100%"><tr>
<td align="left"></td>
<td align="right"><div class="copyright-footer">Copyright © 2003-2021 Christopher
      M. Kohlhoff<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="../expires_at.html"><img src="../../../../prev.png" alt="Prev"></a><a accesskey="u" href="../expires_at.html"><img src="../../../../up.png" alt="Up"></a><a accesskey="h" href="../../../../index.html"><img src="../../../../home.png" alt="Home"></a><a accesskey="n" href="overload2.html"><img src="../../../../next.png" alt="Next"></a>
</div>
</body>
</html>
