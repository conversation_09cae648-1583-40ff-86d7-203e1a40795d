<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>async_read_until (8 of 12 overloads)</title>
<link rel="stylesheet" href="../../../boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.75.2">
<link rel="home" href="../../../index.html" title="Asio">
<link rel="up" href="../async_read_until.html" title="async_read_until">
<link rel="prev" href="overload7.html" title="async_read_until (7 of 12 overloads)">
<link rel="next" href="overload9.html" title="async_read_until (9 of 12 overloads)">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr><td valign="top"><img alt="asio C++ library" width="250" height="60" src="../../../asio.png"></td></tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="overload7.html"><img src="../../../prev.png" alt="Prev"></a><a accesskey="u" href="../async_read_until.html"><img src="../../../up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../home.png" alt="Home"></a><a accesskey="n" href="overload9.html"><img src="../../../next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="asio.reference.async_read_until.overload8"></a><a class="link" href="overload8.html" title="async_read_until (8 of 12 overloads)">async_read_until
        (8 of 12 overloads)</a>
</h4></div></div></div>
<p>
          Start an asynchronous operation to read data into a streambuf until a function
          object indicates a match.
        </p>
<pre class="programlisting">template&lt;
    typename <a class="link" href="../AsyncReadStream.html" title="Buffer-oriented asynchronous read stream requirements">AsyncReadStream</a>,
    typename Allocator,
    typename MatchCondition,
    typename <a class="link" href="../ReadHandler.html" title="Read handler requirements">ReadHandler</a> = <a class="link" href="../asynchronous_operations.html#asio.reference.asynchronous_operations.default_completion_tokens"><span class="emphasis"><em>DEFAULT</em></span></a>&gt;
<a class="link" href="../asynchronous_operations.html#asio.reference.asynchronous_operations.automatic_deduction_of_initiating_function_return_type"><span class="emphasis"><em>DEDUCED</em></span></a> async_read_until(
    AsyncReadStream &amp; s,
    asio::basic_streambuf&lt; Allocator &gt; &amp; b,
    MatchCondition match_condition,
    ReadHandler &amp;&amp; handler = <a class="link" href="../asynchronous_operations.html#asio.reference.asynchronous_operations.default_completion_tokens"><span class="emphasis"><em>DEFAULT</em></span></a>,
    typename constraint&lt; is_match_condition&lt; MatchCondition &gt;::value &gt;::type  = 0);
</pre>
<p>
          This function is used to asynchronously read data into the specified streambuf
          until a user-defined match condition function object, when applied to the
          data contained in the streambuf, indicates a successful match. The function
          call always returns immediately. The asynchronous operation will continue
          until one of the following conditions is true:
        </p>
<div class="itemizedlist"><ul class="itemizedlist" type="disc">
<li class="listitem">
              The match condition function object returns a std::pair where the second
              element evaluates to true.
            </li>
<li class="listitem">
              An error occurred.
            </li>
</ul></div>
<p>
          This operation is implemented in terms of zero or more calls to the stream's
          async_read_some function, and is known as a <span class="emphasis"><em>composed operation</em></span>.
          If the match condition function object already indicates a match, this
          asynchronous operation completes immediately. The program must ensure that
          the stream performs no other read operations (such as async_read, async_read_until,
          the stream's async_read_some function, or any other composed operations
          that perform reads) until this operation completes.
        </p>
<h6>
<a name="asio.reference.async_read_until.overload8.h0"></a>
          <span><a name="asio.reference.async_read_until.overload8.parameters"></a></span><a class="link" href="overload8.html#asio.reference.async_read_until.overload8.parameters">Parameters</a>
        </h6>
<div class="variablelist">
<p class="title"><b></b></p>
<dl>
<dt><span class="term">s</span></dt>
<dd><p>
                The stream from which the data is to be read. The type must support
                the AsyncReadStream concept.
              </p></dd>
<dt><span class="term">b</span></dt>
<dd><p>
                A streambuf object into which the data will be read.
              </p></dd>
<dt><span class="term">match_condition</span></dt>
<dd>
<p>
                The function object to be called to determine whether a match exists.
                The signature of the function object must be:
</p>
<pre class="programlisting">pair&lt;iterator, bool&gt; match_condition(iterator begin, iterator end);
</pre>
<p>
                where <code class="computeroutput">iterator</code> represents the type:
</p>
<pre class="programlisting">buffers_iterator&lt;basic_streambuf&lt;Allocator&gt;::const_buffers_type&gt;
</pre>
<p>
                The iterator parameters <code class="computeroutput">begin</code> and <code class="computeroutput">end</code> define
                the range of bytes to be scanned to determine whether there is a
                match. The <code class="computeroutput">first</code> member of the return value is an iterator
                marking one-past-the-end of the bytes that have been consumed by
                the match function. This iterator is used to calculate the <code class="computeroutput">begin</code>
                parameter for any subsequent invocation of the match condition. The
                <code class="computeroutput">second</code> member of the return value is true if a match
                has been found, false otherwise.
              </p>
</dd>
<dt><span class="term">handler</span></dt>
<dd>
<p>
                The handler to be called when the read operation completes. Copies
                will be made of the handler as required. The function signature of
                the handler must be:
</p>
<pre class="programlisting">void handler(
  // Result of operation.
  const asio::error_code&amp; error,

  // The number of bytes in the streambuf's get
  // area that have been fully consumed by the
  // match function. O if an error occurred.
  std::size_t bytes_transferred
);
</pre>
<p>
                Regardless of whether the asynchronous operation completes immediately
                or not, the handler will not be invoked from within this function.
                On immediate completion, invocation of the handler will be performed
                in a manner equivalent to using <a class="link" href="../post.html" title="post"><code class="computeroutput">post</code></a>.
              </p>
</dd>
</dl>
</div>
<h6>
<a name="asio.reference.async_read_until.overload8.h1"></a>
          <span><a name="asio.reference.async_read_until.overload8.remarks"></a></span><a class="link" href="overload8.html#asio.reference.async_read_until.overload8.remarks">Remarks</a>
        </h6>
<p>
          After a successful async_read_until operation, the streambuf may contain
          additional data beyond that which matched the function object. An application
          will typically leave that data in the streambuf for a subsequent async_read_until
          operation to examine.
        </p>
<p>
          The default implementation of the <code class="computeroutput">is_match_condition</code> type
          trait evaluates to true for function pointers and function objects with
          a <code class="computeroutput">result_type</code> typedef. It must be specialised for other user-defined
          function objects.
        </p>
<h6>
<a name="asio.reference.async_read_until.overload8.h2"></a>
          <span><a name="asio.reference.async_read_until.overload8.examples"></a></span><a class="link" href="overload8.html#asio.reference.async_read_until.overload8.examples">Examples</a>
        </h6>
<p>
          To asynchronously read data into a streambuf until whitespace is encountered:
        </p>
<pre class="programlisting">typedef asio::buffers_iterator&lt;
    asio::streambuf::const_buffers_type&gt; iterator;

std::pair&lt;iterator, bool&gt;
match_whitespace(iterator begin, iterator end)
{
  iterator i = begin;
  while (i != end)
    if (std::isspace(*i++))
      return std::make_pair(i, true);
  return std::make_pair(i, false);
}
...
void handler(const asio::error_code&amp; e, std::size_t size);
...
asio::streambuf b;
asio::async_read_until(s, b, match_whitespace, handler);
</pre>
<p>
          To asynchronously read data into a streambuf until a matching character
          is found:
        </p>
<pre class="programlisting">class match_char
{
public:
  explicit match_char(char c) : c_(c) {}

  template &lt;typename Iterator&gt;
  std::pair&lt;Iterator, bool&gt; operator()(
      Iterator begin, Iterator end) const
  {
    Iterator i = begin;
    while (i != end)
      if (c_ == *i++)
        return std::make_pair(i, true);
    return std::make_pair(i, false);
  }

private:
  char c_;
};

namespace asio {
  template &lt;&gt; struct is_match_condition&lt;match_char&gt;
    : public boost::true_type {};
} // namespace asio
...
void handler(const asio::error_code&amp; e, std::size_t size);
...
asio::streambuf b;
asio::async_read_until(s, b, match_char('a'), handler);
</pre>
</div>
<table xmlns:rev="http://www.cs.rpi.edu/~gregod/boost/tools/doc/revision" width="100%"><tr>
<td align="left"></td>
<td align="right"><div class="copyright-footer">Copyright © 2003-2021 Christopher
      M. Kohlhoff<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="overload7.html"><img src="../../../prev.png" alt="Prev"></a><a accesskey="u" href="../async_read_until.html"><img src="../../../up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../home.png" alt="Home"></a><a accesskey="n" href="overload9.html"><img src="../../../next.png" alt="Next"></a>
</div>
</body>
</html>
