<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>basic_deadline_timer::rebind_executor::other</title>
<link rel="stylesheet" href="../../../boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.75.2">
<link rel="home" href="../../../index.html" title="Asio">
<link rel="up" href="../basic_deadline_timer__rebind_executor.html" title="basic_deadline_timer::rebind_executor">
<link rel="prev" href="../basic_deadline_timer__rebind_executor.html" title="basic_deadline_timer::rebind_executor">
<link rel="next" href="../basic_io_object.html" title="basic_io_object">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr><td valign="top"><img alt="asio C++ library" width="250" height="60" src="../../../asio.png"></td></tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="../basic_deadline_timer__rebind_executor.html"><img src="../../../prev.png" alt="Prev"></a><a accesskey="u" href="../basic_deadline_timer__rebind_executor.html"><img src="../../../up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../home.png" alt="Home"></a><a accesskey="n" href="../basic_io_object.html"><img src="../../../next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="asio.reference.basic_deadline_timer__rebind_executor.other"></a><a class="link" href="other.html" title="basic_deadline_timer::rebind_executor::other">basic_deadline_timer::rebind_executor::other</a>
</h4></div></div></div>
<p>
          <a class="indexterm" name="asio.indexterm.basic_deadline_timer__rebind_executor.other"></a> 
The
          timer type when rebound to the specified executor.
        </p>
<pre class="programlisting">typedef basic_deadline_timer&lt; Time, TimeTraits, Executor1 &gt; other;
</pre>
<h6>
<a name="asio.reference.basic_deadline_timer__rebind_executor.other.h0"></a>
          <span><a name="asio.reference.basic_deadline_timer__rebind_executor.other.types"></a></span><a class="link" href="other.html#asio.reference.basic_deadline_timer__rebind_executor.other.types">Types</a>
        </h6>
<div class="informaltable"><table class="table">
<colgroup>
<col>
<col>
</colgroup>
<thead><tr>
<th>
                  <p>
                    Name
                  </p>
                </th>
<th>
                  <p>
                    Description
                  </p>
                </th>
</tr></thead>
<tbody>
<tr>
<td>
                  <p>
                    <a class="link" href="../basic_deadline_timer__rebind_executor.html" title="basic_deadline_timer::rebind_executor"><span class="bold"><strong>rebind_executor</strong></span></a>
                  </p>
                </td>
<td>
                  <p>
                    Rebinds the timer type to another executor.
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a class="link" href="../basic_deadline_timer/duration_type.html" title="basic_deadline_timer::duration_type"><span class="bold"><strong>duration_type</strong></span></a>
                  </p>
                </td>
<td>
                  <p>
                    The duration type.
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a class="link" href="../basic_deadline_timer/executor_type.html" title="basic_deadline_timer::executor_type"><span class="bold"><strong>executor_type</strong></span></a>
                  </p>
                </td>
<td>
                  <p>
                    The type of the executor associated with the object.
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a class="link" href="../basic_deadline_timer/time_type.html" title="basic_deadline_timer::time_type"><span class="bold"><strong>time_type</strong></span></a>
                  </p>
                </td>
<td>
                  <p>
                    The time type.
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a class="link" href="../basic_deadline_timer/traits_type.html" title="basic_deadline_timer::traits_type"><span class="bold"><strong>traits_type</strong></span></a>
                  </p>
                </td>
<td>
                  <p>
                    The time traits type.
                  </p>
                </td>
</tr>
</tbody>
</table></div>
<h6>
<a name="asio.reference.basic_deadline_timer__rebind_executor.other.h1"></a>
          <span><a name="asio.reference.basic_deadline_timer__rebind_executor.other.member_functions"></a></span><a class="link" href="other.html#asio.reference.basic_deadline_timer__rebind_executor.other.member_functions">Member
          Functions</a>
        </h6>
<div class="informaltable"><table class="table">
<colgroup>
<col>
<col>
</colgroup>
<thead><tr>
<th>
                  <p>
                    Name
                  </p>
                </th>
<th>
                  <p>
                    Description
                  </p>
                </th>
</tr></thead>
<tbody>
<tr>
<td>
                  <p>
                    <a class="link" href="../basic_deadline_timer/async_wait.html" title="basic_deadline_timer::async_wait"><span class="bold"><strong>async_wait</strong></span></a>
                  </p>
                </td>
<td>
                  <p>
                    Start an asynchronous wait on the timer.
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a class="link" href="../basic_deadline_timer/basic_deadline_timer.html" title="basic_deadline_timer::basic_deadline_timer"><span class="bold"><strong>basic_deadline_timer</strong></span></a> <span class="silver">[constructor]</span>
                  </p>
                </td>
<td>
                  <p>
                    Constructor. <br> <span class="silver"> —</span><br> Constructor to set a particular expiry
                    time as an absolute time. <br> <span class="silver"> —</span><br> Constructor to set a
                    particular expiry time relative to now. <br> <span class="silver"> —</span><br> Move-construct
                    a basic_deadline_timer from another.
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a class="link" href="../basic_deadline_timer/cancel.html" title="basic_deadline_timer::cancel"><span class="bold"><strong>cancel</strong></span></a>
                  </p>
                </td>
<td>
                  <p>
                    Cancel any asynchronous operations that are waiting on the timer.
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a class="link" href="../basic_deadline_timer/cancel_one.html" title="basic_deadline_timer::cancel_one"><span class="bold"><strong>cancel_one</strong></span></a>
                  </p>
                </td>
<td>
                  <p>
                    Cancels one asynchronous operation that is waiting on the timer.
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a class="link" href="../basic_deadline_timer/expires_at.html" title="basic_deadline_timer::expires_at"><span class="bold"><strong>expires_at</strong></span></a>
                  </p>
                </td>
<td>
                  <p>
                    Get the timer's expiry time as an absolute time. <br> <span class="silver"> —</span><br>
                    Set the timer's expiry time as an absolute time.
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a class="link" href="../basic_deadline_timer/expires_from_now.html" title="basic_deadline_timer::expires_from_now"><span class="bold"><strong>expires_from_now</strong></span></a>
                  </p>
                </td>
<td>
                  <p>
                    Get the timer's expiry time relative to now. <br> <span class="silver"> —</span><br> Set
                    the timer's expiry time relative to now.
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a class="link" href="../basic_deadline_timer/get_executor.html" title="basic_deadline_timer::get_executor"><span class="bold"><strong>get_executor</strong></span></a>
                  </p>
                </td>
<td>
                  <p>
                    Get the executor associated with the object.
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a class="link" href="../basic_deadline_timer/operator_eq_.html" title="basic_deadline_timer::operator="><span class="bold"><strong>operator=</strong></span></a>
                  </p>
                </td>
<td>
                  <p>
                    Move-assign a basic_deadline_timer from another.
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a class="link" href="../basic_deadline_timer/wait.html" title="basic_deadline_timer::wait"><span class="bold"><strong>wait</strong></span></a>
                  </p>
                </td>
<td>
                  <p>
                    Perform a blocking wait on the timer.
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a class="link" href="../basic_deadline_timer/_basic_deadline_timer.html" title="basic_deadline_timer::~basic_deadline_timer"><span class="bold"><strong>~basic_deadline_timer</strong></span></a> <span class="silver">[destructor]</span>
                  </p>
                </td>
<td>
                  <p>
                    Destroys the timer.
                  </p>
                </td>
</tr>
</tbody>
</table></div>
<p>
          The <a class="link" href="../basic_deadline_timer.html" title="basic_deadline_timer"><code class="computeroutput">basic_deadline_timer</code></a>
          class template provides the ability to perform a blocking or asynchronous
          wait for a timer to expire.
        </p>
<p>
          A deadline timer is always in one of two states: "expired" or
          "not expired". If the <code class="computeroutput">wait()</code> or <code class="computeroutput">async_wait()</code>
          function is called on an expired timer, the wait operation will complete
          immediately.
        </p>
<p>
          Most applications will use the <a class="link" href="../deadline_timer.html" title="deadline_timer"><code class="computeroutput">deadline_timer</code></a>
          typedef.
        </p>
<h6>
<a name="asio.reference.basic_deadline_timer__rebind_executor.other.h2"></a>
          <span><a name="asio.reference.basic_deadline_timer__rebind_executor.other.thread_safety"></a></span><a class="link" href="other.html#asio.reference.basic_deadline_timer__rebind_executor.other.thread_safety">Thread
          Safety</a>
        </h6>
<p>
          <span class="emphasis"><em>Distinct</em></span> <span class="emphasis"><em>objects:</em></span> Safe.
        </p>
<p>
          <span class="emphasis"><em>Shared</em></span> <span class="emphasis"><em>objects:</em></span> Unsafe.
        </p>
<h6>
<a name="asio.reference.basic_deadline_timer__rebind_executor.other.h3"></a>
          <span><a name="asio.reference.basic_deadline_timer__rebind_executor.other.examples"></a></span><a class="link" href="other.html#asio.reference.basic_deadline_timer__rebind_executor.other.examples">Examples</a>
        </h6>
<p>
          Performing a blocking wait:
        </p>
<pre class="programlisting">// Construct a timer without setting an expiry time.
asio::deadline_timer timer(my_context);

// Set an expiry time relative to now.
timer.expires_from_now(boost::posix_time::seconds(5));

// Wait for the timer to expire.
timer.wait();
</pre>
<p>
          Performing an asynchronous wait:
        </p>
<pre class="programlisting">void handler(const asio::error_code&amp; error)
{
  if (!error)
  {
    // Timer expired.
  }
}

...

// Construct a timer with an absolute expiry time.
asio::deadline_timer timer(my_context,
    boost::posix_time::time_from_string("2005-12-07 23:59:59.000"));

// Start an asynchronous wait.
timer.async_wait(handler);
</pre>
<h6>
<a name="asio.reference.basic_deadline_timer__rebind_executor.other.h4"></a>
          <span><a name="asio.reference.basic_deadline_timer__rebind_executor.other.changing_an_active_deadline_timer_s_expiry_time"></a></span><a class="link" href="other.html#asio.reference.basic_deadline_timer__rebind_executor.other.changing_an_active_deadline_timer_s_expiry_time">Changing
          an active deadline_timer's expiry time</a>
        </h6>
<p>
          Changing the expiry time of a timer while there are pending asynchronous
          waits causes those wait operations to be cancelled. To ensure that the
          action associated with the timer is performed only once, use something
          like this: used:
        </p>
<pre class="programlisting">void on_some_event()
{
  if (my_timer.expires_from_now(seconds(5)) &gt; 0)
  {
    // We managed to cancel the timer. Start new asynchronous wait.
    my_timer.async_wait(on_timeout);
  }
  else
  {
    // Too late, timer has already expired!
  }
}

void on_timeout(const asio::error_code&amp; e)
{
  if (e != asio::error::operation_aborted)
  {
    // Timer was not cancelled, take necessary action.
  }
}
</pre>
<div class="itemizedlist"><ul class="itemizedlist" type="disc">
<li class="listitem">
              The <code class="computeroutput">asio::basic_deadline_timer::expires_from_now()</code> function
              cancels any pending asynchronous waits, and returns the number of asynchronous
              waits that were cancelled. If it returns 0 then you were too late and
              the wait handler has already been executed, or will soon be executed.
              If it returns 1 then the wait handler was successfully cancelled.
            </li>
<li class="listitem">
              If a wait handler is cancelled, the <a class="link" href="../error_code.html" title="error_code"><code class="computeroutput">error_code</code></a>
              passed to it contains the value <code class="computeroutput">asio::error::operation_aborted</code>.
            </li>
</ul></div>
<h6>
<a name="asio.reference.basic_deadline_timer__rebind_executor.other.h5"></a>
          <span><a name="asio.reference.basic_deadline_timer__rebind_executor.other.requirements"></a></span><a class="link" href="other.html#asio.reference.basic_deadline_timer__rebind_executor.other.requirements">Requirements</a>
        </h6>
<p>
          <span class="emphasis"><em>Header: </em></span><code class="literal">asio/basic_deadline_timer.hpp</code>
        </p>
<p>
          <span class="emphasis"><em>Convenience header: </em></span><code class="literal">asio.hpp</code>
        </p>
</div>
<table xmlns:rev="http://www.cs.rpi.edu/~gregod/boost/tools/doc/revision" width="100%"><tr>
<td align="left"></td>
<td align="right"><div class="copyright-footer">Copyright © 2003-2021 Christopher
      M. Kohlhoff<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="../basic_deadline_timer__rebind_executor.html"><img src="../../../prev.png" alt="Prev"></a><a accesskey="u" href="../basic_deadline_timer__rebind_executor.html"><img src="../../../up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../home.png" alt="Home"></a><a accesskey="n" href="../basic_io_object.html"><img src="../../../next.png" alt="Next"></a>
</div>
</body>
</html>
