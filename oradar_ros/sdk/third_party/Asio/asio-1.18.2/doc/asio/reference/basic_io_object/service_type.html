<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>basic_io_object::service_type</title>
<link rel="stylesheet" href="../../../boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.75.2">
<link rel="home" href="../../../index.html" title="Asio">
<link rel="up" href="../basic_io_object.html" title="basic_io_object">
<link rel="prev" href="operator_eq_.html" title="basic_io_object::operator=">
<link rel="next" href="_basic_io_object.html" title="basic_io_object::~basic_io_object">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr><td valign="top"><img alt="asio C++ library" width="250" height="60" src="../../../asio.png"></td></tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="operator_eq_.html"><img src="../../../prev.png" alt="Prev"></a><a accesskey="u" href="../basic_io_object.html"><img src="../../../up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../home.png" alt="Home"></a><a accesskey="n" href="_basic_io_object.html"><img src="../../../next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="asio.reference.basic_io_object.service_type"></a><a class="link" href="service_type.html" title="basic_io_object::service_type">basic_io_object::service_type</a>
</h4></div></div></div>
<p>
          <a class="indexterm" name="asio.indexterm.basic_io_object.service_type"></a> 
The
          type of the service that will be used to provide I/O operations.
        </p>
<pre class="programlisting">typedef IoObjectService service_type;
</pre>
<h6>
<a name="asio.reference.basic_io_object.service_type.h0"></a>
          <span><a name="asio.reference.basic_io_object.service_type.requirements"></a></span><a class="link" href="service_type.html#asio.reference.basic_io_object.service_type.requirements">Requirements</a>
        </h6>
<p>
          <span class="emphasis"><em>Header: </em></span><code class="literal">asio/basic_io_object.hpp</code>
        </p>
<p>
          <span class="emphasis"><em>Convenience header: </em></span><code class="literal">asio.hpp</code>
        </p>
</div>
<table xmlns:rev="http://www.cs.rpi.edu/~gregod/boost/tools/doc/revision" width="100%"><tr>
<td align="left"></td>
<td align="right"><div class="copyright-footer">Copyright © 2003-2021 Christopher
      M. Kohlhoff<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="operator_eq_.html"><img src="../../../prev.png" alt="Prev"></a><a accesskey="u" href="../basic_io_object.html"><img src="../../../up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../home.png" alt="Home"></a><a accesskey="n" href="_basic_io_object.html"><img src="../../../next.png" alt="Next"></a>
</div>
</body>
</html>
