<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>basic_io_object::executor_type</title>
<link rel="stylesheet" href="../../../boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.75.2">
<link rel="home" href="../../../index.html" title="Asio">
<link rel="up" href="../basic_io_object.html" title="basic_io_object">
<link rel="prev" href="basic_io_object/overload3.html" title="basic_io_object::basic_io_object (3 of 3 overloads)">
<link rel="next" href="get_executor.html" title="basic_io_object::get_executor">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr><td valign="top"><img alt="asio C++ library" width="250" height="60" src="../../../asio.png"></td></tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="basic_io_object/overload3.html"><img src="../../../prev.png" alt="Prev"></a><a accesskey="u" href="../basic_io_object.html"><img src="../../../up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../home.png" alt="Home"></a><a accesskey="n" href="get_executor.html"><img src="../../../next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="asio.reference.basic_io_object.executor_type"></a><a class="link" href="executor_type.html" title="basic_io_object::executor_type">basic_io_object::executor_type</a>
</h4></div></div></div>
<p>
          <a class="indexterm" name="asio.indexterm.basic_io_object.executor_type"></a> 
The
          type of the executor associated with the object.
        </p>
<pre class="programlisting">typedef asio::io_context::executor_type executor_type;
</pre>
<h6>
<a name="asio.reference.basic_io_object.executor_type.h0"></a>
          <span><a name="asio.reference.basic_io_object.executor_type.requirements"></a></span><a class="link" href="executor_type.html#asio.reference.basic_io_object.executor_type.requirements">Requirements</a>
        </h6>
<p>
          <span class="emphasis"><em>Header: </em></span><code class="literal">asio/basic_io_object.hpp</code>
        </p>
<p>
          <span class="emphasis"><em>Convenience header: </em></span><code class="literal">asio.hpp</code>
        </p>
</div>
<table xmlns:rev="http://www.cs.rpi.edu/~gregod/boost/tools/doc/revision" width="100%"><tr>
<td align="left"></td>
<td align="right"><div class="copyright-footer">Copyright © 2003-2021 Christopher
      M. Kohlhoff<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="basic_io_object/overload3.html"><img src="../../../prev.png" alt="Prev"></a><a accesskey="u" href="../basic_io_object.html"><img src="../../../up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../home.png" alt="Home"></a><a accesskey="n" href="get_executor.html"><img src="../../../next.png" alt="Next"></a>
</div>
</body>
</html>
