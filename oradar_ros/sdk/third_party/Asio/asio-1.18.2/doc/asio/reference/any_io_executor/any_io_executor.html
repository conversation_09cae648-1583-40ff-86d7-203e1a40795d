<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>any_io_executor::any_io_executor</title>
<link rel="stylesheet" href="../../../boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.75.2">
<link rel="home" href="../../../index.html" title="Asio">
<link rel="up" href="../any_io_executor.html" title="any_io_executor">
<link rel="prev" href="../any_io_executor.html" title="any_io_executor">
<link rel="next" href="any_io_executor/overload1.html" title="any_io_executor::any_io_executor (1 of 6 overloads)">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr><td valign="top"><img alt="asio C++ library" width="250" height="60" src="../../../asio.png"></td></tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="../any_io_executor.html"><img src="../../../prev.png" alt="Prev"></a><a accesskey="u" href="../any_io_executor.html"><img src="../../../up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../home.png" alt="Home"></a><a accesskey="n" href="any_io_executor/overload1.html"><img src="../../../next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="asio.reference.any_io_executor.any_io_executor"></a><a class="link" href="any_io_executor.html" title="any_io_executor::any_io_executor">any_io_executor::any_io_executor</a>
</h4></div></div></div>
<p>
          <a class="indexterm" name="asio.indexterm.any_io_executor.any_io_executor"></a> 
Default
          constructor.
        </p>
<pre class="programlisting"><a class="link" href="any_io_executor/overload1.html" title="any_io_executor::any_io_executor (1 of 6 overloads)">any_io_executor</a>();
  <span class="emphasis"><em>» <a class="link" href="any_io_executor/overload1.html" title="any_io_executor::any_io_executor (1 of 6 overloads)">more...</a></em></span>
</pre>
<p>
          Construct in an empty state. Equivalent effects to default constructor.
        </p>
<pre class="programlisting"><a class="link" href="any_io_executor/overload2.html" title="any_io_executor::any_io_executor (2 of 6 overloads)">any_io_executor</a>(
    nullptr_t );
  <span class="emphasis"><em>» <a class="link" href="any_io_executor/overload2.html" title="any_io_executor::any_io_executor (2 of 6 overloads)">more...</a></em></span>
</pre>
<p>
          Copy constructor.
        </p>
<pre class="programlisting"><a class="link" href="any_io_executor/overload3.html" title="any_io_executor::any_io_executor (3 of 6 overloads)">any_io_executor</a>(
    const any_io_executor &amp; e);
  <span class="emphasis"><em>» <a class="link" href="any_io_executor/overload3.html" title="any_io_executor::any_io_executor (3 of 6 overloads)">more...</a></em></span>
</pre>
<p>
          Move constructor.
        </p>
<pre class="programlisting"><a class="link" href="any_io_executor/overload4.html" title="any_io_executor::any_io_executor (4 of 6 overloads)">any_io_executor</a>(
    any_io_executor &amp;&amp; e);
  <span class="emphasis"><em>» <a class="link" href="any_io_executor/overload4.html" title="any_io_executor::any_io_executor (4 of 6 overloads)">more...</a></em></span>
</pre>
<p>
          Construct to point to the same target as another any_executor.
        </p>
<pre class="programlisting">template&lt;
    class... OtherSupportableProperties&gt;
<a class="link" href="any_io_executor/overload5.html" title="any_io_executor::any_io_executor (5 of 6 overloads)">any_io_executor</a>(
    execution::any_executor&lt; OtherSupportableProperties...&gt; e);
  <span class="emphasis"><em>» <a class="link" href="any_io_executor/overload5.html" title="any_io_executor::any_io_executor (5 of 6 overloads)">more...</a></em></span>
</pre>
<p>
          Construct a polymorphic wrapper for the specified executor.
        </p>
<pre class="programlisting">template&lt;
    ASIO_EXECUTION_EXECUTOR <a class="link" href="../Executor1.html" title="Executor requirements">Executor</a>&gt;
<a class="link" href="any_io_executor/overload6.html" title="any_io_executor::any_io_executor (6 of 6 overloads)">any_io_executor</a>(
    Executor e);
  <span class="emphasis"><em>» <a class="link" href="any_io_executor/overload6.html" title="any_io_executor::any_io_executor (6 of 6 overloads)">more...</a></em></span>
</pre>
</div>
<table xmlns:rev="http://www.cs.rpi.edu/~gregod/boost/tools/doc/revision" width="100%"><tr>
<td align="left"></td>
<td align="right"><div class="copyright-footer">Copyright © 2003-2021 Christopher
      M. Kohlhoff<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="../any_io_executor.html"><img src="../../../prev.png" alt="Prev"></a><a accesskey="u" href="../any_io_executor.html"><img src="../../../up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../home.png" alt="Home"></a><a accesskey="n" href="any_io_executor/overload1.html"><img src="../../../next.png" alt="Next"></a>
</div>
</body>
</html>
