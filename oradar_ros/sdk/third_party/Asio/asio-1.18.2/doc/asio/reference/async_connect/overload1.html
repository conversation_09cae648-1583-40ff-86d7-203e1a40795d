<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>async_connect (1 of 6 overloads)</title>
<link rel="stylesheet" href="../../../boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.75.2">
<link rel="home" href="../../../index.html" title="Asio">
<link rel="up" href="../async_connect.html" title="async_connect">
<link rel="prev" href="../async_connect.html" title="async_connect">
<link rel="next" href="overload2.html" title="async_connect (2 of 6 overloads)">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr><td valign="top"><img alt="asio C++ library" width="250" height="60" src="../../../asio.png"></td></tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="../async_connect.html"><img src="../../../prev.png" alt="Prev"></a><a accesskey="u" href="../async_connect.html"><img src="../../../up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../home.png" alt="Home"></a><a accesskey="n" href="overload2.html"><img src="../../../next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="asio.reference.async_connect.overload1"></a><a class="link" href="overload1.html" title="async_connect (1 of 6 overloads)">async_connect
        (1 of 6 overloads)</a>
</h4></div></div></div>
<p>
          Asynchronously establishes a socket connection by trying each endpoint
          in a sequence.
        </p>
<pre class="programlisting">template&lt;
    typename <a class="link" href="../Protocol.html" title="Protocol requirements">Protocol</a>,
    typename <a class="link" href="../Executor1.html" title="Executor requirements">Executor</a>,
    typename <a class="link" href="../EndpointSequence.html" title="Endpoint sequence requirements">EndpointSequence</a>,
    typename <a class="link" href="../RangeConnectHandler.html" title="Range connect handler requirements">RangeConnectHandler</a> = <a class="link" href="../asynchronous_operations.html#asio.reference.asynchronous_operations.default_completion_tokens"><span class="emphasis"><em>DEFAULT</em></span></a>&gt;
<a class="link" href="../asynchronous_operations.html#asio.reference.asynchronous_operations.automatic_deduction_of_initiating_function_return_type"><span class="emphasis"><em>DEDUCED</em></span></a> async_connect(
    basic_socket&lt; Protocol, Executor &gt; &amp; s,
    const EndpointSequence &amp; endpoints,
    RangeConnectHandler &amp;&amp; handler = <a class="link" href="../asynchronous_operations.html#asio.reference.asynchronous_operations.default_completion_tokens"><span class="emphasis"><em>DEFAULT</em></span></a>,
    typename constraint&lt; is_endpoint_sequence&lt; EndpointSequence &gt;::value &gt;::type  = 0);
</pre>
<p>
          This function attempts to connect a socket to one of a sequence of endpoints.
          It does this by repeated calls to the socket's <code class="computeroutput">async_connect</code>
          member function, once for each endpoint in the sequence, until a connection
          is successfully established.
        </p>
<h6>
<a name="asio.reference.async_connect.overload1.h0"></a>
          <span><a name="asio.reference.async_connect.overload1.parameters"></a></span><a class="link" href="overload1.html#asio.reference.async_connect.overload1.parameters">Parameters</a>
        </h6>
<div class="variablelist">
<p class="title"><b></b></p>
<dl>
<dt><span class="term">s</span></dt>
<dd><p>
                The socket to be connected. If the socket is already open, it will
                be closed.
              </p></dd>
<dt><span class="term">endpoints</span></dt>
<dd><p>
                A sequence of endpoints.
              </p></dd>
<dt><span class="term">handler</span></dt>
<dd>
<p>
                The handler to be called when the connect operation completes. Copies
                will be made of the handler as required. The function signature of
                the handler must be:
</p>
<pre class="programlisting">void handler(
  // Result of operation. if the sequence is empty, set to
  // asio::error::not_found. Otherwise, contains the
  // error from the last connection attempt.
  const asio::error_code&amp; error,

  // On success, the successfully connected endpoint.
  // Otherwise, a default-constructed endpoint.
  const typename Protocol::endpoint&amp; endpoint
);
</pre>
<p>
                Regardless of whether the asynchronous operation completes immediately
                or not, the handler will not be invoked from within this function.
                On immediate completion, invocation of the handler will be performed
                in a manner equivalent to using <a class="link" href="../post.html" title="post"><code class="computeroutput">post</code></a>.
              </p>
</dd>
</dl>
</div>
<h6>
<a name="asio.reference.async_connect.overload1.h1"></a>
          <span><a name="asio.reference.async_connect.overload1.example"></a></span><a class="link" href="overload1.html#asio.reference.async_connect.overload1.example">Example</a>
        </h6>
<pre class="programlisting">tcp::resolver r(my_context);
tcp::resolver::query q("host", "service");
tcp::socket s(my_context);

// ...

r.async_resolve(q, resolve_handler);

// ...

void resolve_handler(
    const asio::error_code&amp; ec,
    tcp::resolver::results_type results)
{
  if (!ec)
  {
    asio::async_connect(s, results, connect_handler);
  }
}

// ...

void connect_handler(
    const asio::error_code&amp; ec,
    const tcp::endpoint&amp; endpoint)
{
  // ...
}
</pre>
</div>
<table xmlns:rev="http://www.cs.rpi.edu/~gregod/boost/tools/doc/revision" width="100%"><tr>
<td align="left"></td>
<td align="right"><div class="copyright-footer">Copyright © 2003-2021 Christopher
      M. Kohlhoff<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="../async_connect.html"><img src="../../../prev.png" alt="Prev"></a><a accesskey="u" href="../async_connect.html"><img src="../../../up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../home.png" alt="Home"></a><a accesskey="n" href="overload2.html"><img src="../../../next.png" alt="Next"></a>
</div>
</body>
</html>
