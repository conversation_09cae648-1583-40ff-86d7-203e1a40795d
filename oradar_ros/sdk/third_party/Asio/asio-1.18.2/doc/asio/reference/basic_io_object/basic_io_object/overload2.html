<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>basic_io_object::basic_io_object (2 of 3 overloads)</title>
<link rel="stylesheet" href="../../../../boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.75.2">
<link rel="home" href="../../../../index.html" title="Asio">
<link rel="up" href="../basic_io_object.html" title="basic_io_object::basic_io_object">
<link rel="prev" href="overload1.html" title="basic_io_object::basic_io_object (1 of 3 overloads)">
<link rel="next" href="overload3.html" title="basic_io_object::basic_io_object (3 of 3 overloads)">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr><td valign="top"><img alt="asio C++ library" width="250" height="60" src="../../../../asio.png"></td></tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="overload1.html"><img src="../../../../prev.png" alt="Prev"></a><a accesskey="u" href="../basic_io_object.html"><img src="../../../../up.png" alt="Up"></a><a accesskey="h" href="../../../../index.html"><img src="../../../../home.png" alt="Home"></a><a accesskey="n" href="overload3.html"><img src="../../../../next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h5 class="title">
<a name="asio.reference.basic_io_object.basic_io_object.overload2"></a><a class="link" href="overload2.html" title="basic_io_object::basic_io_object (2 of 3 overloads)">basic_io_object::basic_io_object
          (2 of 3 overloads)</a>
</h5></div></div></div>
<p>
            Move-construct a <a class="link" href="../../basic_io_object.html" title="basic_io_object"><code class="computeroutput">basic_io_object</code></a>.
          </p>
<pre class="programlisting">basic_io_object(
    basic_io_object &amp;&amp; other);
</pre>
<p>
            Performs:
          </p>
<pre class="programlisting">get_service().move_construct(
    get_implementation(), other.get_implementation());
</pre>
<h6>
<a name="asio.reference.basic_io_object.basic_io_object.overload2.h0"></a>
            <span><a name="asio.reference.basic_io_object.basic_io_object.overload2.remarks"></a></span><a class="link" href="overload2.html#asio.reference.basic_io_object.basic_io_object.overload2.remarks">Remarks</a>
          </h6>
<p>
            Available only for services that support movability,
          </p>
</div>
<table xmlns:rev="http://www.cs.rpi.edu/~gregod/boost/tools/doc/revision" width="100%"><tr>
<td align="left"></td>
<td align="right"><div class="copyright-footer">Copyright © 2003-2021 Christopher
      M. Kohlhoff<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="overload1.html"><img src="../../../../prev.png" alt="Prev"></a><a accesskey="u" href="../basic_io_object.html"><img src="../../../../up.png" alt="Up"></a><a accesskey="h" href="../../../../index.html"><img src="../../../../home.png" alt="Home"></a><a accesskey="n" href="overload3.html"><img src="../../../../next.png" alt="Next"></a>
</div>
</body>
</html>
