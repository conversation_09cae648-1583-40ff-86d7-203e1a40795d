<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>any_io_executor::operator=</title>
<link rel="stylesheet" href="../../../boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.75.2">
<link rel="home" href="../../../index.html" title="Asio">
<link rel="up" href="../any_io_executor.html" title="any_io_executor">
<link rel="prev" href="operator_not__eq_/overload3.html" title="any_io_executor::operator!= (3 of 3 overloads)">
<link rel="next" href="operator_eq_/overload1.html" title="any_io_executor::operator= (1 of 3 overloads)">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr><td valign="top"><img alt="asio C++ library" width="250" height="60" src="../../../asio.png"></td></tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="operator_not__eq_/overload3.html"><img src="../../../prev.png" alt="Prev"></a><a accesskey="u" href="../any_io_executor.html"><img src="../../../up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../home.png" alt="Home"></a><a accesskey="n" href="operator_eq_/overload1.html"><img src="../../../next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="asio.reference.any_io_executor.operator_eq_"></a><a class="link" href="operator_eq_.html" title="any_io_executor::operator=">any_io_executor::operator=</a>
</h4></div></div></div>
<p>
          <a class="indexterm" name="asio.indexterm.any_io_executor.operator_eq_"></a> 
Assignment
          operator.
        </p>
<pre class="programlisting">any_io_executor &amp; <a class="link" href="operator_eq_/overload1.html" title="any_io_executor::operator= (1 of 3 overloads)">operator=</a>(
    const any_io_executor &amp; e);
  <span class="emphasis"><em>» <a class="link" href="operator_eq_/overload1.html" title="any_io_executor::operator= (1 of 3 overloads)">more...</a></em></span>
</pre>
<p>
          Move assignment operator.
        </p>
<pre class="programlisting">any_io_executor &amp; <a class="link" href="operator_eq_/overload2.html" title="any_io_executor::operator= (2 of 3 overloads)">operator=</a>(
    any_io_executor &amp;&amp; e);
  <span class="emphasis"><em>» <a class="link" href="operator_eq_/overload2.html" title="any_io_executor::operator= (2 of 3 overloads)">more...</a></em></span>
</pre>
<p>
          Assignment operator that sets the polymorphic wrapper to the empty state.
        </p>
<pre class="programlisting">any_io_executor &amp; <a class="link" href="operator_eq_/overload3.html" title="any_io_executor::operator= (3 of 3 overloads)">operator=</a>(
    nullptr_t );
  <span class="emphasis"><em>» <a class="link" href="operator_eq_/overload3.html" title="any_io_executor::operator= (3 of 3 overloads)">more...</a></em></span>
</pre>
</div>
<table xmlns:rev="http://www.cs.rpi.edu/~gregod/boost/tools/doc/revision" width="100%"><tr>
<td align="left"></td>
<td align="right"><div class="copyright-footer">Copyright © 2003-2021 Christopher
      M. Kohlhoff<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="operator_not__eq_/overload3.html"><img src="../../../prev.png" alt="Prev"></a><a accesskey="u" href="../any_io_executor.html"><img src="../../../up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../home.png" alt="Home"></a><a accesskey="n" href="operator_eq_/overload1.html"><img src="../../../next.png" alt="Next"></a>
</div>
</body>
</html>
