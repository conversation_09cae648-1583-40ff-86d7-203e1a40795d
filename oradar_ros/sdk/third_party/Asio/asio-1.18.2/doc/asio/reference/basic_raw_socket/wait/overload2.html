<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>basic_raw_socket::wait (2 of 2 overloads)</title>
<link rel="stylesheet" href="../../../../boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.75.2">
<link rel="home" href="../../../../index.html" title="Asio">
<link rel="up" href="../wait.html" title="basic_raw_socket::wait">
<link rel="prev" href="overload1.html" title="basic_raw_socket::wait (1 of 2 overloads)">
<link rel="next" href="../wait_type.html" title="basic_raw_socket::wait_type">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr><td valign="top"><img alt="asio C++ library" width="250" height="60" src="../../../../asio.png"></td></tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="overload1.html"><img src="../../../../prev.png" alt="Prev"></a><a accesskey="u" href="../wait.html"><img src="../../../../up.png" alt="Up"></a><a accesskey="h" href="../../../../index.html"><img src="../../../../home.png" alt="Home"></a><a accesskey="n" href="../wait_type.html"><img src="../../../../next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h5 class="title">
<a name="asio.reference.basic_raw_socket.wait.overload2"></a><a class="link" href="overload2.html" title="basic_raw_socket::wait (2 of 2 overloads)">basic_raw_socket::wait
          (2 of 2 overloads)</a>
</h5></div></div></div>
<p>
            <span class="emphasis"><em>Inherited from basic_socket.</em></span>
          </p>
<p>
            Wait for the socket to become ready to read, ready to write, or to have
            pending error conditions.
          </p>
<pre class="programlisting">void wait(
    wait_type w,
    asio::error_code &amp; ec);
</pre>
<p>
            This function is used to perform a blocking wait for a socket to enter
            a ready to read, write or error condition state.
          </p>
<h6>
<a name="asio.reference.basic_raw_socket.wait.overload2.h0"></a>
            <span><a name="asio.reference.basic_raw_socket.wait.overload2.parameters"></a></span><a class="link" href="overload2.html#asio.reference.basic_raw_socket.wait.overload2.parameters">Parameters</a>
          </h6>
<div class="variablelist">
<p class="title"><b></b></p>
<dl>
<dt><span class="term">w</span></dt>
<dd><p>
                  Specifies the desired socket state.
                </p></dd>
<dt><span class="term">ec</span></dt>
<dd><p>
                  Set to indicate what error occurred, if any.
                </p></dd>
</dl>
</div>
<h6>
<a name="asio.reference.basic_raw_socket.wait.overload2.h1"></a>
            <span><a name="asio.reference.basic_raw_socket.wait.overload2.example"></a></span><a class="link" href="overload2.html#asio.reference.basic_raw_socket.wait.overload2.example">Example</a>
          </h6>
<p>
            Waiting for a socket to become readable.
          </p>
<pre class="programlisting">asio::ip::tcp::socket socket(my_context);
...
asio::error_code ec;
socket.wait(asio::ip::tcp::socket::wait_read, ec);
</pre>
</div>
<table xmlns:rev="http://www.cs.rpi.edu/~gregod/boost/tools/doc/revision" width="100%"><tr>
<td align="left"></td>
<td align="right"><div class="copyright-footer">Copyright © 2003-2021 Christopher
      M. Kohlhoff<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="overload1.html"><img src="../../../../prev.png" alt="Prev"></a><a accesskey="u" href="../wait.html"><img src="../../../../up.png" alt="Up"></a><a accesskey="h" href="../../../../index.html"><img src="../../../../home.png" alt="Home"></a><a accesskey="n" href="../wait_type.html"><img src="../../../../next.png" alt="Next"></a>
</div>
</body>
</html>
