<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>basic_deadline_timer::async_wait</title>
<link rel="stylesheet" href="../../../boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.75.2">
<link rel="home" href="../../../index.html" title="Asio">
<link rel="up" href="../basic_deadline_timer.html" title="basic_deadline_timer">
<link rel="prev" href="../basic_deadline_timer.html" title="basic_deadline_timer">
<link rel="next" href="basic_deadline_timer.html" title="basic_deadline_timer::basic_deadline_timer">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr><td valign="top"><img alt="asio C++ library" width="250" height="60" src="../../../asio.png"></td></tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="../basic_deadline_timer.html"><img src="../../../prev.png" alt="Prev"></a><a accesskey="u" href="../basic_deadline_timer.html"><img src="../../../up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../home.png" alt="Home"></a><a accesskey="n" href="basic_deadline_timer.html"><img src="../../../next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="asio.reference.basic_deadline_timer.async_wait"></a><a class="link" href="async_wait.html" title="basic_deadline_timer::async_wait">basic_deadline_timer::async_wait</a>
</h4></div></div></div>
<p>
          <a class="indexterm" name="asio.indexterm.basic_deadline_timer.async_wait"></a> 
Start
          an asynchronous wait on the timer.
        </p>
<pre class="programlisting">template&lt;
    typename <a class="link" href="../WaitHandler.html" title="Wait handler requirements">WaitHandler</a> = <a class="link" href="../asynchronous_operations.html#asio.reference.asynchronous_operations.default_completion_tokens"><span class="emphasis"><em>DEFAULT</em></span></a>&gt;
<a class="link" href="../asynchronous_operations.html#asio.reference.asynchronous_operations.automatic_deduction_of_initiating_function_return_type"><span class="emphasis"><em>DEDUCED</em></span></a> async_wait(
    WaitHandler &amp;&amp; handler = <a class="link" href="../asynchronous_operations.html#asio.reference.asynchronous_operations.default_completion_tokens"><span class="emphasis"><em>DEFAULT</em></span></a>);
</pre>
<p>
          This function may be used to initiate an asynchronous wait against the
          timer. It always returns immediately.
        </p>
<p>
          For each call to <code class="computeroutput">async_wait()</code>, the supplied handler will be
          called exactly once. The handler will be called when:
        </p>
<div class="itemizedlist"><ul class="itemizedlist" type="disc">
<li class="listitem">
              The timer has expired.
            </li>
<li class="listitem">
              The timer was cancelled, in which case the handler is passed the error
              code <code class="computeroutput">asio::error::operation_aborted</code>.
            </li>
</ul></div>
<h6>
<a name="asio.reference.basic_deadline_timer.async_wait.h0"></a>
          <span><a name="asio.reference.basic_deadline_timer.async_wait.parameters"></a></span><a class="link" href="async_wait.html#asio.reference.basic_deadline_timer.async_wait.parameters">Parameters</a>
        </h6>
<div class="variablelist">
<p class="title"><b></b></p>
<dl>
<dt><span class="term">handler</span></dt>
<dd>
<p>
                The handler to be called when the timer expires. Copies will be made
                of the handler as required. The function signature of the handler
                must be:
</p>
<pre class="programlisting">void handler(
  const asio::error_code&amp; error // Result of operation.
);
</pre>
<p>
                Regardless of whether the asynchronous operation completes immediately
                or not, the handler will not be invoked from within this function.
                On immediate completion, invocation of the handler will be performed
                in a manner equivalent to using <a class="link" href="../post.html" title="post"><code class="computeroutput">post</code></a>.
              </p>
</dd>
</dl>
</div>
</div>
<table xmlns:rev="http://www.cs.rpi.edu/~gregod/boost/tools/doc/revision" width="100%"><tr>
<td align="left"></td>
<td align="right"><div class="copyright-footer">Copyright © 2003-2021 Christopher
      M. Kohlhoff<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="../basic_deadline_timer.html"><img src="../../../prev.png" alt="Prev"></a><a accesskey="u" href="../basic_deadline_timer.html"><img src="../../../up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../home.png" alt="Home"></a><a accesskey="n" href="basic_deadline_timer.html"><img src="../../../next.png" alt="Next"></a>
</div>
</body>
</html>
