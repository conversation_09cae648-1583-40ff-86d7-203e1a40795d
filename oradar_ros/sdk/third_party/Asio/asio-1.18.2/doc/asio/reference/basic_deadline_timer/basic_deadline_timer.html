<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>basic_deadline_timer::basic_deadline_timer</title>
<link rel="stylesheet" href="../../../boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.75.2">
<link rel="home" href="../../../index.html" title="Asio">
<link rel="up" href="../basic_deadline_timer.html" title="basic_deadline_timer">
<link rel="prev" href="async_wait.html" title="basic_deadline_timer::async_wait">
<link rel="next" href="basic_deadline_timer/overload1.html" title="basic_deadline_timer::basic_deadline_timer (1 of 7 overloads)">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr><td valign="top"><img alt="asio C++ library" width="250" height="60" src="../../../asio.png"></td></tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="async_wait.html"><img src="../../../prev.png" alt="Prev"></a><a accesskey="u" href="../basic_deadline_timer.html"><img src="../../../up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../home.png" alt="Home"></a><a accesskey="n" href="basic_deadline_timer/overload1.html"><img src="../../../next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="asio.reference.basic_deadline_timer.basic_deadline_timer"></a><a class="link" href="basic_deadline_timer.html" title="basic_deadline_timer::basic_deadline_timer">basic_deadline_timer::basic_deadline_timer</a>
</h4></div></div></div>
<p>
          <a class="indexterm" name="asio.indexterm.basic_deadline_timer.basic_deadline_timer"></a> 
Constructor.
        </p>
<pre class="programlisting">explicit <a class="link" href="basic_deadline_timer/overload1.html" title="basic_deadline_timer::basic_deadline_timer (1 of 7 overloads)">basic_deadline_timer</a>(
    const executor_type &amp; ex);
  <span class="emphasis"><em>» <a class="link" href="basic_deadline_timer/overload1.html" title="basic_deadline_timer::basic_deadline_timer (1 of 7 overloads)">more...</a></em></span>

template&lt;
    typename ExecutionContext&gt;
explicit <a class="link" href="basic_deadline_timer/overload2.html" title="basic_deadline_timer::basic_deadline_timer (2 of 7 overloads)">basic_deadline_timer</a>(
    ExecutionContext &amp; context,
    typename constraint&lt; is_convertible&lt; ExecutionContext &amp;, execution_context &amp; &gt;::value &gt;::type  = 0);
  <span class="emphasis"><em>» <a class="link" href="basic_deadline_timer/overload2.html" title="basic_deadline_timer::basic_deadline_timer (2 of 7 overloads)">more...</a></em></span>
</pre>
<p>
          Constructor to set a particular expiry time as an absolute time.
        </p>
<pre class="programlisting"><a class="link" href="basic_deadline_timer/overload3.html" title="basic_deadline_timer::basic_deadline_timer (3 of 7 overloads)">basic_deadline_timer</a>(
    const executor_type &amp; ex,
    const time_type &amp; expiry_time);
  <span class="emphasis"><em>» <a class="link" href="basic_deadline_timer/overload3.html" title="basic_deadline_timer::basic_deadline_timer (3 of 7 overloads)">more...</a></em></span>

template&lt;
    typename ExecutionContext&gt;
<a class="link" href="basic_deadline_timer/overload4.html" title="basic_deadline_timer::basic_deadline_timer (4 of 7 overloads)">basic_deadline_timer</a>(
    ExecutionContext &amp; context,
    const time_type &amp; expiry_time,
    typename constraint&lt; is_convertible&lt; ExecutionContext &amp;, execution_context &amp; &gt;::value &gt;::type  = 0);
  <span class="emphasis"><em>» <a class="link" href="basic_deadline_timer/overload4.html" title="basic_deadline_timer::basic_deadline_timer (4 of 7 overloads)">more...</a></em></span>
</pre>
<p>
          Constructor to set a particular expiry time relative to now.
        </p>
<pre class="programlisting"><a class="link" href="basic_deadline_timer/overload5.html" title="basic_deadline_timer::basic_deadline_timer (5 of 7 overloads)">basic_deadline_timer</a>(
    const executor_type &amp; ex,
    const duration_type &amp; expiry_time);
  <span class="emphasis"><em>» <a class="link" href="basic_deadline_timer/overload5.html" title="basic_deadline_timer::basic_deadline_timer (5 of 7 overloads)">more...</a></em></span>

template&lt;
    typename ExecutionContext&gt;
<a class="link" href="basic_deadline_timer/overload6.html" title="basic_deadline_timer::basic_deadline_timer (6 of 7 overloads)">basic_deadline_timer</a>(
    ExecutionContext &amp; context,
    const duration_type &amp; expiry_time,
    typename constraint&lt; is_convertible&lt; ExecutionContext &amp;, execution_context &amp; &gt;::value &gt;::type  = 0);
  <span class="emphasis"><em>» <a class="link" href="basic_deadline_timer/overload6.html" title="basic_deadline_timer::basic_deadline_timer (6 of 7 overloads)">more...</a></em></span>
</pre>
<p>
          Move-construct a <a class="link" href="../basic_deadline_timer.html" title="basic_deadline_timer"><code class="computeroutput">basic_deadline_timer</code></a>
          from another.
        </p>
<pre class="programlisting"><a class="link" href="basic_deadline_timer/overload7.html" title="basic_deadline_timer::basic_deadline_timer (7 of 7 overloads)">basic_deadline_timer</a>(
    basic_deadline_timer &amp;&amp; other);
  <span class="emphasis"><em>» <a class="link" href="basic_deadline_timer/overload7.html" title="basic_deadline_timer::basic_deadline_timer (7 of 7 overloads)">more...</a></em></span>
</pre>
</div>
<table xmlns:rev="http://www.cs.rpi.edu/~gregod/boost/tools/doc/revision" width="100%"><tr>
<td align="left"></td>
<td align="right"><div class="copyright-footer">Copyright © 2003-2021 Christopher
      M. Kohlhoff<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="async_wait.html"><img src="../../../prev.png" alt="Prev"></a><a accesskey="u" href="../basic_deadline_timer.html"><img src="../../../up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../home.png" alt="Home"></a><a accesskey="n" href="basic_deadline_timer/overload1.html"><img src="../../../next.png" alt="Next"></a>
</div>
</body>
</html>
