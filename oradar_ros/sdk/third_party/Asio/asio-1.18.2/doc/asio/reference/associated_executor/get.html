<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>associated_executor::get</title>
<link rel="stylesheet" href="../../../boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.75.2">
<link rel="home" href="../../../index.html" title="Asio">
<link rel="up" href="../associated_executor.html" title="associated_executor">
<link rel="prev" href="../associated_executor.html" title="associated_executor">
<link rel="next" href="type.html" title="associated_executor::type">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr><td valign="top"><img alt="asio C++ library" width="250" height="60" src="../../../asio.png"></td></tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="../associated_executor.html"><img src="../../../prev.png" alt="Prev"></a><a accesskey="u" href="../associated_executor.html"><img src="../../../up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../home.png" alt="Home"></a><a accesskey="n" href="type.html"><img src="../../../next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="asio.reference.associated_executor.get"></a><a class="link" href="get.html" title="associated_executor::get">associated_executor::get</a>
</h4></div></div></div>
<p>
          <a class="indexterm" name="asio.indexterm.associated_executor.get"></a> 
If <code class="computeroutput">T</code>
          has a nested type <code class="computeroutput">executor_type</code>, returns <code class="computeroutput">t.get_executor()</code>.
          Otherwise returns <code class="computeroutput">ex</code>.
        </p>
<pre class="programlisting">static type get(
    const T &amp; t,
    const Executor &amp; ex = Executor());
</pre>
</div>
<table xmlns:rev="http://www.cs.rpi.edu/~gregod/boost/tools/doc/revision" width="100%"><tr>
<td align="left"></td>
<td align="right"><div class="copyright-footer">Copyright © 2003-2021 Christopher
      M. Kohlhoff<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="../associated_executor.html"><img src="../../../prev.png" alt="Prev"></a><a accesskey="u" href="../associated_executor.html"><img src="../../../up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../home.png" alt="Home"></a><a accesskey="n" href="type.html"><img src="../../../next.png" alt="Next"></a>
</div>
</body>
</html>
