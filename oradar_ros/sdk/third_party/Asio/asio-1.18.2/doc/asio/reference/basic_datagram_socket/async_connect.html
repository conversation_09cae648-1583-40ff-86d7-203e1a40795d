<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>basic_datagram_socket::async_connect</title>
<link rel="stylesheet" href="../../../boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.75.2">
<link rel="home" href="../../../index.html" title="Asio">
<link rel="up" href="../basic_datagram_socket.html" title="basic_datagram_socket">
<link rel="prev" href="assign/overload2.html" title="basic_datagram_socket::assign (2 of 2 overloads)">
<link rel="next" href="async_receive.html" title="basic_datagram_socket::async_receive">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr><td valign="top"><img alt="asio C++ library" width="250" height="60" src="../../../asio.png"></td></tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="assign/overload2.html"><img src="../../../prev.png" alt="Prev"></a><a accesskey="u" href="../basic_datagram_socket.html"><img src="../../../up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../home.png" alt="Home"></a><a accesskey="n" href="async_receive.html"><img src="../../../next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="asio.reference.basic_datagram_socket.async_connect"></a><a class="link" href="async_connect.html" title="basic_datagram_socket::async_connect">basic_datagram_socket::async_connect</a>
</h4></div></div></div>
<p>
          <span class="emphasis"><em>Inherited from basic_socket.</em></span>
        </p>
<p>
          <a class="indexterm" name="asio.indexterm.basic_datagram_socket.async_connect"></a> 
Start
          an asynchronous connect.
        </p>
<pre class="programlisting">template&lt;
    typename <a class="link" href="../ConnectHandler.html" title="Connect handler requirements">ConnectHandler</a> = <a class="link" href="../asynchronous_operations.html#asio.reference.asynchronous_operations.default_completion_tokens"><span class="emphasis"><em>DEFAULT</em></span></a>&gt;
<a class="link" href="../asynchronous_operations.html#asio.reference.asynchronous_operations.automatic_deduction_of_initiating_function_return_type"><span class="emphasis"><em>DEDUCED</em></span></a> async_connect(
    const endpoint_type &amp; peer_endpoint,
    ConnectHandler &amp;&amp; handler = <a class="link" href="../asynchronous_operations.html#asio.reference.asynchronous_operations.default_completion_tokens"><span class="emphasis"><em>DEFAULT</em></span></a>);
</pre>
<p>
          This function is used to asynchronously connect a socket to the specified
          remote endpoint. The function call always returns immediately.
        </p>
<p>
          The socket is automatically opened if it is not already open. If the connect
          fails, and the socket was automatically opened, the socket is not returned
          to the closed state.
        </p>
<h6>
<a name="asio.reference.basic_datagram_socket.async_connect.h0"></a>
          <span><a name="asio.reference.basic_datagram_socket.async_connect.parameters"></a></span><a class="link" href="async_connect.html#asio.reference.basic_datagram_socket.async_connect.parameters">Parameters</a>
        </h6>
<div class="variablelist">
<p class="title"><b></b></p>
<dl>
<dt><span class="term">peer_endpoint</span></dt>
<dd><p>
                The remote endpoint to which the socket will be connected. Copies
                will be made of the endpoint object as required.
              </p></dd>
<dt><span class="term">handler</span></dt>
<dd>
<p>
                The handler to be called when the connection operation completes.
                Copies will be made of the handler as required. The function signature
                of the handler must be:
</p>
<pre class="programlisting">void handler(
  const asio::error_code&amp; error // Result of operation
);
</pre>
<p>
                Regardless of whether the asynchronous operation completes immediately
                or not, the handler will not be invoked from within this function.
                On immediate completion, invocation of the handler will be performed
                in a manner equivalent to using <a class="link" href="../post.html" title="post"><code class="computeroutput">post</code></a>.
              </p>
</dd>
</dl>
</div>
<h6>
<a name="asio.reference.basic_datagram_socket.async_connect.h1"></a>
          <span><a name="asio.reference.basic_datagram_socket.async_connect.example"></a></span><a class="link" href="async_connect.html#asio.reference.basic_datagram_socket.async_connect.example">Example</a>
        </h6>
<pre class="programlisting">void connect_handler(const asio::error_code&amp; error)
{
  if (!error)
  {
    // Connect succeeded.
  }
}

...

asio::ip::tcp::socket socket(my_context);
asio::ip::tcp::endpoint endpoint(
    asio::ip::address::from_string("*******"), 12345);
socket.async_connect(endpoint, connect_handler);
</pre>
</div>
<table xmlns:rev="http://www.cs.rpi.edu/~gregod/boost/tools/doc/revision" width="100%"><tr>
<td align="left"></td>
<td align="right"><div class="copyright-footer">Copyright © 2003-2021 Christopher
      M. Kohlhoff<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="assign/overload2.html"><img src="../../../prev.png" alt="Prev"></a><a accesskey="u" href="../basic_datagram_socket.html"><img src="../../../up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../home.png" alt="Home"></a><a accesskey="n" href="async_receive.html"><img src="../../../next.png" alt="Next"></a>
</div>
</body>
</html>
