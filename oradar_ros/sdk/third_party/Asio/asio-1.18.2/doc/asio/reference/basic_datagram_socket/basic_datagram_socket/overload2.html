<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>basic_datagram_socket::basic_datagram_socket (2 of 10 overloads)</title>
<link rel="stylesheet" href="../../../../boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.75.2">
<link rel="home" href="../../../../index.html" title="Asio">
<link rel="up" href="../basic_datagram_socket.html" title="basic_datagram_socket::basic_datagram_socket">
<link rel="prev" href="overload1.html" title="basic_datagram_socket::basic_datagram_socket (1 of 10 overloads)">
<link rel="next" href="overload3.html" title="basic_datagram_socket::basic_datagram_socket (3 of 10 overloads)">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr><td valign="top"><img alt="asio C++ library" width="250" height="60" src="../../../../asio.png"></td></tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="overload1.html"><img src="../../../../prev.png" alt="Prev"></a><a accesskey="u" href="../basic_datagram_socket.html"><img src="../../../../up.png" alt="Up"></a><a accesskey="h" href="../../../../index.html"><img src="../../../../home.png" alt="Home"></a><a accesskey="n" href="overload3.html"><img src="../../../../next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h5 class="title">
<a name="asio.reference.basic_datagram_socket.basic_datagram_socket.overload2"></a><a class="link" href="overload2.html" title="basic_datagram_socket::basic_datagram_socket (2 of 10 overloads)">basic_datagram_socket::basic_datagram_socket
          (2 of 10 overloads)</a>
</h5></div></div></div>
<p>
            Construct a <a class="link" href="../../basic_datagram_socket.html" title="basic_datagram_socket"><code class="computeroutput">basic_datagram_socket</code></a>
            without opening it.
          </p>
<pre class="programlisting">template&lt;
    typename ExecutionContext&gt;
basic_datagram_socket(
    ExecutionContext &amp; context,
    typename constraint&lt; is_convertible&lt; ExecutionContext &amp;, execution_context &amp; &gt;::value &gt;::type  = 0);
</pre>
<p>
            This constructor creates a datagram socket without opening it. The <code class="computeroutput">open()</code>
            function must be called before data can be sent or received on the socket.
          </p>
<h6>
<a name="asio.reference.basic_datagram_socket.basic_datagram_socket.overload2.h0"></a>
            <span><a name="asio.reference.basic_datagram_socket.basic_datagram_socket.overload2.parameters"></a></span><a class="link" href="overload2.html#asio.reference.basic_datagram_socket.basic_datagram_socket.overload2.parameters">Parameters</a>
          </h6>
<div class="variablelist">
<p class="title"><b></b></p>
<dl>
<dt><span class="term">context</span></dt>
<dd><p>
                  An execution context which provides the I/O executor that the socket
                  will use, by default, to dispatch handlers for any asynchronous
                  operations performed on the socket.
                </p></dd>
</dl>
</div>
</div>
<table xmlns:rev="http://www.cs.rpi.edu/~gregod/boost/tools/doc/revision" width="100%"><tr>
<td align="left"></td>
<td align="right"><div class="copyright-footer">Copyright © 2003-2021 Christopher
      M. Kohlhoff<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="overload1.html"><img src="../../../../prev.png" alt="Prev"></a><a accesskey="u" href="../basic_datagram_socket.html"><img src="../../../../up.png" alt="Up"></a><a accesskey="h" href="../../../../index.html"><img src="../../../../home.png" alt="Home"></a><a accesskey="n" href="overload3.html"><img src="../../../../next.png" alt="Next"></a>
</div>
</body>
</html>
