<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>basic_deadline_timer::expires_from_now</title>
<link rel="stylesheet" href="../../../boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.75.2">
<link rel="home" href="../../../index.html" title="Asio">
<link rel="up" href="../basic_deadline_timer.html" title="basic_deadline_timer">
<link rel="prev" href="expires_at/overload3.html" title="basic_deadline_timer::expires_at (3 of 3 overloads)">
<link rel="next" href="expires_from_now/overload1.html" title="basic_deadline_timer::expires_from_now (1 of 3 overloads)">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr><td valign="top"><img alt="asio C++ library" width="250" height="60" src="../../../asio.png"></td></tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="expires_at/overload3.html"><img src="../../../prev.png" alt="Prev"></a><a accesskey="u" href="../basic_deadline_timer.html"><img src="../../../up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../home.png" alt="Home"></a><a accesskey="n" href="expires_from_now/overload1.html"><img src="../../../next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="asio.reference.basic_deadline_timer.expires_from_now"></a><a class="link" href="expires_from_now.html" title="basic_deadline_timer::expires_from_now">basic_deadline_timer::expires_from_now</a>
</h4></div></div></div>
<p>
          <a class="indexterm" name="asio.indexterm.basic_deadline_timer.expires_from_now"></a> 
Get
          the timer's expiry time relative to now.
        </p>
<pre class="programlisting">duration_type <a class="link" href="expires_from_now/overload1.html" title="basic_deadline_timer::expires_from_now (1 of 3 overloads)">expires_from_now</a>() const;
  <span class="emphasis"><em>» <a class="link" href="expires_from_now/overload1.html" title="basic_deadline_timer::expires_from_now (1 of 3 overloads)">more...</a></em></span>
</pre>
<p>
          Set the timer's expiry time relative to now.
        </p>
<pre class="programlisting">std::size_t <a class="link" href="expires_from_now/overload2.html" title="basic_deadline_timer::expires_from_now (2 of 3 overloads)">expires_from_now</a>(
    const duration_type &amp; expiry_time);
  <span class="emphasis"><em>» <a class="link" href="expires_from_now/overload2.html" title="basic_deadline_timer::expires_from_now (2 of 3 overloads)">more...</a></em></span>

std::size_t <a class="link" href="expires_from_now/overload3.html" title="basic_deadline_timer::expires_from_now (3 of 3 overloads)">expires_from_now</a>(
    const duration_type &amp; expiry_time,
    asio::error_code &amp; ec);
  <span class="emphasis"><em>» <a class="link" href="expires_from_now/overload3.html" title="basic_deadline_timer::expires_from_now (3 of 3 overloads)">more...</a></em></span>
</pre>
</div>
<table xmlns:rev="http://www.cs.rpi.edu/~gregod/boost/tools/doc/revision" width="100%"><tr>
<td align="left"></td>
<td align="right"><div class="copyright-footer">Copyright © 2003-2021 Christopher
      M. Kohlhoff<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="expires_at/overload3.html"><img src="../../../prev.png" alt="Prev"></a><a accesskey="u" href="../basic_deadline_timer.html"><img src="../../../up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../home.png" alt="Home"></a><a accesskey="n" href="expires_from_now/overload1.html"><img src="../../../next.png" alt="Next"></a>
</div>
</body>
</html>
