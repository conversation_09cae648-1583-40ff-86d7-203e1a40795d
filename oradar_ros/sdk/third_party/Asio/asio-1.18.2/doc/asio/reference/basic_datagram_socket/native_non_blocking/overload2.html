<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>basic_datagram_socket::native_non_blocking (2 of 3 overloads)</title>
<link rel="stylesheet" href="../../../../boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.75.2">
<link rel="home" href="../../../../index.html" title="Asio">
<link rel="up" href="../native_non_blocking.html" title="basic_datagram_socket::native_non_blocking">
<link rel="prev" href="overload1.html" title="basic_datagram_socket::native_non_blocking (1 of 3 overloads)">
<link rel="next" href="overload3.html" title="basic_datagram_socket::native_non_blocking (3 of 3 overloads)">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr><td valign="top"><img alt="asio C++ library" width="250" height="60" src="../../../../asio.png"></td></tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="overload1.html"><img src="../../../../prev.png" alt="Prev"></a><a accesskey="u" href="../native_non_blocking.html"><img src="../../../../up.png" alt="Up"></a><a accesskey="h" href="../../../../index.html"><img src="../../../../home.png" alt="Home"></a><a accesskey="n" href="overload3.html"><img src="../../../../next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h5 class="title">
<a name="asio.reference.basic_datagram_socket.native_non_blocking.overload2"></a><a class="link" href="overload2.html" title="basic_datagram_socket::native_non_blocking (2 of 3 overloads)">basic_datagram_socket::native_non_blocking
          (2 of 3 overloads)</a>
</h5></div></div></div>
<p>
            <span class="emphasis"><em>Inherited from basic_socket.</em></span>
          </p>
<p>
            Sets the non-blocking mode of the native socket implementation.
          </p>
<pre class="programlisting">void native_non_blocking(
    bool mode);
</pre>
<p>
            This function is used to modify the non-blocking mode of the underlying
            native socket. It has no effect on the behaviour of the socket object's
            synchronous operations.
          </p>
<h6>
<a name="asio.reference.basic_datagram_socket.native_non_blocking.overload2.h0"></a>
            <span><a name="asio.reference.basic_datagram_socket.native_non_blocking.overload2.parameters"></a></span><a class="link" href="overload2.html#asio.reference.basic_datagram_socket.native_non_blocking.overload2.parameters">Parameters</a>
          </h6>
<div class="variablelist">
<p class="title"><b></b></p>
<dl>
<dt><span class="term">mode</span></dt>
<dd><p>
                  If <code class="computeroutput">true</code>, the underlying socket is put into non-blocking
                  mode and direct system calls may fail with <code class="computeroutput">asio::error::would_block</code>
                  (or the equivalent system error).
                </p></dd>
</dl>
</div>
<h6>
<a name="asio.reference.basic_datagram_socket.native_non_blocking.overload2.h1"></a>
            <span><a name="asio.reference.basic_datagram_socket.native_non_blocking.overload2.exceptions"></a></span><a class="link" href="overload2.html#asio.reference.basic_datagram_socket.native_non_blocking.overload2.exceptions">Exceptions</a>
          </h6>
<div class="variablelist">
<p class="title"><b></b></p>
<dl>
<dt><span class="term">asio::system_error</span></dt>
<dd><p>
                  Thrown on failure. If the <code class="computeroutput">mode</code> is <code class="computeroutput">false</code>,
                  but the current value of <code class="computeroutput">non_blocking()</code> is <code class="computeroutput">true</code>,
                  this function fails with <code class="computeroutput">asio::error::invalid_argument</code>,
                  as the combination does not make sense.
                </p></dd>
</dl>
</div>
<h6>
<a name="asio.reference.basic_datagram_socket.native_non_blocking.overload2.h2"></a>
            <span><a name="asio.reference.basic_datagram_socket.native_non_blocking.overload2.example"></a></span><a class="link" href="overload2.html#asio.reference.basic_datagram_socket.native_non_blocking.overload2.example">Example</a>
          </h6>
<p>
            This function is intended to allow the encapsulation of arbitrary non-blocking
            system calls as asynchronous operations, in a way that is transparent
            to the user of the socket object. The following example illustrates how
            Linux's <code class="computeroutput">sendfile</code> system call might be encapsulated:
          </p>
<pre class="programlisting">template &lt;typename Handler&gt;
struct sendfile_op
{
  tcp::socket&amp; sock_;
  int fd_;
  Handler handler_;
  off_t offset_;
  std::size_t total_bytes_transferred_;

  // Function call operator meeting WriteHandler requirements.
  // Used as the handler for the async_write_some operation.
  void operator()(asio::error_code ec, std::size_t)
  {
    // Put the underlying socket into non-blocking mode.
    if (!ec)
      if (!sock_.native_non_blocking())
        sock_.native_non_blocking(true, ec);

    if (!ec)
    {
      for (;;)
      {
        // Try the system call.
        errno = 0;
        int n = ::sendfile(sock_.native_handle(), fd_, &amp;offset_, 65536);
        ec = asio::error_code(n &lt; 0 ? errno : 0,
            asio::error::get_system_category());
        total_bytes_transferred_ += ec ? 0 : n;

        // Retry operation immediately if interrupted by signal.
        if (ec == asio::error::interrupted)
          continue;

        // Check if we need to run the operation again.
        if (ec == asio::error::would_block
            || ec == asio::error::try_again)
        {
          // We have to wait for the socket to become ready again.
          sock_.async_wait(tcp::socket::wait_write, *this);
          return;
        }

        if (ec || n == 0)
        {
          // An error occurred, or we have reached the end of the file.
          // Either way we must exit the loop so we can call the handler.
          break;
        }

        // Loop around to try calling sendfile again.
      }
    }

    // Pass result back to user's handler.
    handler_(ec, total_bytes_transferred_);
  }
};

template &lt;typename Handler&gt;
void async_sendfile(tcp::socket&amp; sock, int fd, Handler h)
{
  sendfile_op&lt;Handler&gt; op = { sock, fd, h, 0, 0 };
  sock.async_wait(tcp::socket::wait_write, op);
}
</pre>
</div>
<table xmlns:rev="http://www.cs.rpi.edu/~gregod/boost/tools/doc/revision" width="100%"><tr>
<td align="left"></td>
<td align="right"><div class="copyright-footer">Copyright © 2003-2021 Christopher
      M. Kohlhoff<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="overload1.html"><img src="../../../../prev.png" alt="Prev"></a><a accesskey="u" href="../native_non_blocking.html"><img src="../../../../up.png" alt="Up"></a><a accesskey="h" href="../../../../index.html"><img src="../../../../home.png" alt="Home"></a><a accesskey="n" href="overload3.html"><img src="../../../../next.png" alt="Next"></a>
</div>
</body>
</html>
