<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>basic_raw_socket::lowest_layer (1 of 2 overloads)</title>
<link rel="stylesheet" href="../../../../boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.75.2">
<link rel="home" href="../../../../index.html" title="Asio">
<link rel="up" href="../lowest_layer.html" title="basic_raw_socket::lowest_layer">
<link rel="prev" href="../lowest_layer.html" title="basic_raw_socket::lowest_layer">
<link rel="next" href="overload2.html" title="basic_raw_socket::lowest_layer (2 of 2 overloads)">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr><td valign="top"><img alt="asio C++ library" width="250" height="60" src="../../../../asio.png"></td></tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="../lowest_layer.html"><img src="../../../../prev.png" alt="Prev"></a><a accesskey="u" href="../lowest_layer.html"><img src="../../../../up.png" alt="Up"></a><a accesskey="h" href="../../../../index.html"><img src="../../../../home.png" alt="Home"></a><a accesskey="n" href="overload2.html"><img src="../../../../next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h5 class="title">
<a name="asio.reference.basic_raw_socket.lowest_layer.overload1"></a><a class="link" href="overload1.html" title="basic_raw_socket::lowest_layer (1 of 2 overloads)">basic_raw_socket::lowest_layer
          (1 of 2 overloads)</a>
</h5></div></div></div>
<p>
            <span class="emphasis"><em>Inherited from basic_socket.</em></span>
          </p>
<p>
            Get a reference to the lowest layer.
          </p>
<pre class="programlisting">lowest_layer_type &amp; lowest_layer();
</pre>
<p>
            This function returns a reference to the lowest layer in a stack of layers.
            Since a <a class="link" href="../../basic_socket.html" title="basic_socket"><code class="computeroutput">basic_socket</code></a>
            cannot contain any further layers, it simply returns a reference to itself.
          </p>
<h6>
<a name="asio.reference.basic_raw_socket.lowest_layer.overload1.h0"></a>
            <span><a name="asio.reference.basic_raw_socket.lowest_layer.overload1.return_value"></a></span><a class="link" href="overload1.html#asio.reference.basic_raw_socket.lowest_layer.overload1.return_value">Return
            Value</a>
          </h6>
<p>
            A reference to the lowest layer in the stack of layers. Ownership is
            not transferred to the caller.
          </p>
</div>
<table xmlns:rev="http://www.cs.rpi.edu/~gregod/boost/tools/doc/revision" width="100%"><tr>
<td align="left"></td>
<td align="right"><div class="copyright-footer">Copyright © 2003-2021 Christopher
      M. Kohlhoff<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="../lowest_layer.html"><img src="../../../../prev.png" alt="Prev"></a><a accesskey="u" href="../lowest_layer.html"><img src="../../../../up.png" alt="Up"></a><a accesskey="h" href="../../../../index.html"><img src="../../../../home.png" alt="Home"></a><a accesskey="n" href="overload2.html"><img src="../../../../next.png" alt="Next"></a>
</div>
</body>
</html>
