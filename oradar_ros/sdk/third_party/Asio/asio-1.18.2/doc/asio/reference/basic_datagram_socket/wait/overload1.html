<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>basic_datagram_socket::wait (1 of 2 overloads)</title>
<link rel="stylesheet" href="../../../../boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.75.2">
<link rel="home" href="../../../../index.html" title="Asio">
<link rel="up" href="../wait.html" title="basic_datagram_socket::wait">
<link rel="prev" href="../wait.html" title="basic_datagram_socket::wait">
<link rel="next" href="overload2.html" title="basic_datagram_socket::wait (2 of 2 overloads)">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr><td valign="top"><img alt="asio C++ library" width="250" height="60" src="../../../../asio.png"></td></tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="../wait.html"><img src="../../../../prev.png" alt="Prev"></a><a accesskey="u" href="../wait.html"><img src="../../../../up.png" alt="Up"></a><a accesskey="h" href="../../../../index.html"><img src="../../../../home.png" alt="Home"></a><a accesskey="n" href="overload2.html"><img src="../../../../next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h5 class="title">
<a name="asio.reference.basic_datagram_socket.wait.overload1"></a><a class="link" href="overload1.html" title="basic_datagram_socket::wait (1 of 2 overloads)">basic_datagram_socket::wait
          (1 of 2 overloads)</a>
</h5></div></div></div>
<p>
            <span class="emphasis"><em>Inherited from basic_socket.</em></span>
          </p>
<p>
            Wait for the socket to become ready to read, ready to write, or to have
            pending error conditions.
          </p>
<pre class="programlisting">void wait(
    wait_type w);
</pre>
<p>
            This function is used to perform a blocking wait for a socket to enter
            a ready to read, write or error condition state.
          </p>
<h6>
<a name="asio.reference.basic_datagram_socket.wait.overload1.h0"></a>
            <span><a name="asio.reference.basic_datagram_socket.wait.overload1.parameters"></a></span><a class="link" href="overload1.html#asio.reference.basic_datagram_socket.wait.overload1.parameters">Parameters</a>
          </h6>
<div class="variablelist">
<p class="title"><b></b></p>
<dl>
<dt><span class="term">w</span></dt>
<dd><p>
                  Specifies the desired socket state.
                </p></dd>
</dl>
</div>
<h6>
<a name="asio.reference.basic_datagram_socket.wait.overload1.h1"></a>
            <span><a name="asio.reference.basic_datagram_socket.wait.overload1.example"></a></span><a class="link" href="overload1.html#asio.reference.basic_datagram_socket.wait.overload1.example">Example</a>
          </h6>
<p>
            Waiting for a socket to become readable.
          </p>
<pre class="programlisting">asio::ip::tcp::socket socket(my_context);
...
socket.wait(asio::ip::tcp::socket::wait_read);
</pre>
</div>
<table xmlns:rev="http://www.cs.rpi.edu/~gregod/boost/tools/doc/revision" width="100%"><tr>
<td align="left"></td>
<td align="right"><div class="copyright-footer">Copyright © 2003-2021 Christopher
      M. Kohlhoff<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="../wait.html"><img src="../../../../prev.png" alt="Prev"></a><a accesskey="u" href="../wait.html"><img src="../../../../up.png" alt="Up"></a><a accesskey="h" href="../../../../index.html"><img src="../../../../home.png" alt="Home"></a><a accesskey="n" href="overload2.html"><img src="../../../../next.png" alt="Next"></a>
</div>
</body>
</html>
