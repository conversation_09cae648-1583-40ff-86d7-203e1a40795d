<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>any_io_executor::execute</title>
<link rel="stylesheet" href="../../../boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.75.2">
<link rel="home" href="../../../index.html" title="Asio">
<link rel="up" href="../any_io_executor.html" title="any_io_executor">
<link rel="prev" href="context.html" title="any_io_executor::context">
<link rel="next" href="operator_bool.html" title="any_io_executor::operator bool">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr><td valign="top"><img alt="asio C++ library" width="250" height="60" src="../../../asio.png"></td></tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="context.html"><img src="../../../prev.png" alt="Prev"></a><a accesskey="u" href="../any_io_executor.html"><img src="../../../up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../home.png" alt="Home"></a><a accesskey="n" href="operator_bool.html"><img src="../../../next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="asio.reference.any_io_executor.execute"></a><a class="link" href="execute.html" title="any_io_executor::execute">any_io_executor::execute</a>
</h4></div></div></div>
<p>
          <span class="emphasis"><em>Inherited from execution::any_executor.</em></span>
        </p>
<p>
          <a class="indexterm" name="asio.indexterm.any_io_executor.execute"></a> 
Execute the
          function on the target executor.
        </p>
<pre class="programlisting">template&lt;
    typename Function&gt;
void execute(
    Function &amp;&amp; f) const;
</pre>
<p>
          Do not call this function directly. It is intended for use with the <a class="link" href="../execution__execute.html" title="execution::execute"><code class="computeroutput">execution::execute</code></a>
          customisation point.
        </p>
<p>
          For example:
        </p>
<pre class="programlisting">execution::any_executor&lt;&gt; ex = ...;
execution::execute(ex, my_function_object);
</pre>
<p>
          Throws <a class="link" href="../bad_executor.html" title="bad_executor"><code class="computeroutput">bad_executor</code></a>
          if the polymorphic wrapper has no target.
        </p>
</div>
<table xmlns:rev="http://www.cs.rpi.edu/~gregod/boost/tools/doc/revision" width="100%"><tr>
<td align="left"></td>
<td align="right"><div class="copyright-footer">Copyright © 2003-2021 Christopher
      M. Kohlhoff<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="context.html"><img src="../../../prev.png" alt="Prev"></a><a accesskey="u" href="../any_io_executor.html"><img src="../../../up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../home.png" alt="Home"></a><a accesskey="n" href="operator_bool.html"><img src="../../../next.png" alt="Next"></a>
</div>
</body>
</html>
