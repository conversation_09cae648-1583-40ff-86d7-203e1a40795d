<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>async_write_at (4 of 4 overloads)</title>
<link rel="stylesheet" href="../../../boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.75.2">
<link rel="home" href="../../../index.html" title="Asio">
<link rel="up" href="../async_write_at.html" title="async_write_at">
<link rel="prev" href="overload3.html" title="async_write_at (3 of 4 overloads)">
<link rel="next" href="../awaitable.html" title="awaitable">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr><td valign="top"><img alt="asio C++ library" width="250" height="60" src="../../../asio.png"></td></tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="overload3.html"><img src="../../../prev.png" alt="Prev"></a><a accesskey="u" href="../async_write_at.html"><img src="../../../up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../home.png" alt="Home"></a><a accesskey="n" href="../awaitable.html"><img src="../../../next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="asio.reference.async_write_at.overload4"></a><a class="link" href="overload4.html" title="async_write_at (4 of 4 overloads)">async_write_at
        (4 of 4 overloads)</a>
</h4></div></div></div>
<p>
          Start an asynchronous operation to write a certain amount of data at the
          specified offset.
        </p>
<pre class="programlisting">template&lt;
    typename <a class="link" href="../AsyncRandomAccessWriteDevice.html" title="Buffer-oriented asynchronous random-access write device requirements">AsyncRandomAccessWriteDevice</a>,
    typename Allocator,
    typename <a class="link" href="../CompletionCondition.html" title="Completion condition requirements">CompletionCondition</a>,
    typename <a class="link" href="../WriteHandler.html" title="Write handler requirements">WriteHandler</a> = <a class="link" href="../asynchronous_operations.html#asio.reference.asynchronous_operations.default_completion_tokens"><span class="emphasis"><em>DEFAULT</em></span></a>&gt;
<a class="link" href="../asynchronous_operations.html#asio.reference.asynchronous_operations.automatic_deduction_of_initiating_function_return_type"><span class="emphasis"><em>DEDUCED</em></span></a> async_write_at(
    AsyncRandomAccessWriteDevice &amp; d,
    uint64_t offset,
    basic_streambuf&lt; Allocator &gt; &amp; b,
    CompletionCondition completion_condition,
    WriteHandler &amp;&amp; handler = <a class="link" href="../asynchronous_operations.html#asio.reference.asynchronous_operations.default_completion_tokens"><span class="emphasis"><em>DEFAULT</em></span></a>);
</pre>
<p>
          This function is used to asynchronously write a certain number of bytes
          of data to a random access device at a specified offset. The function call
          always returns immediately. The asynchronous operation will continue until
          one of the following conditions is true:
        </p>
<div class="itemizedlist"><ul class="itemizedlist" type="disc">
<li class="listitem">
              All of the data in the supplied <a class="link" href="../basic_streambuf.html" title="basic_streambuf"><code class="computeroutput">basic_streambuf</code></a>
              has been written.
            </li>
<li class="listitem">
              The completion_condition function object returns 0.
            </li>
</ul></div>
<p>
          This operation is implemented in terms of zero or more calls to the device's
          async_write_some_at function, and is known as a <span class="emphasis"><em>composed operation</em></span>.
          The program must ensure that the device performs no <span class="emphasis"><em>overlapping</em></span>
          write operations (such as async_write_at, the device's async_write_some_at
          function, or any other composed operations that perform writes) until this
          operation completes. Operations are overlapping if the regions defined
          by their offsets, and the numbers of bytes to write, intersect.
        </p>
<h6>
<a name="asio.reference.async_write_at.overload4.h0"></a>
          <span><a name="asio.reference.async_write_at.overload4.parameters"></a></span><a class="link" href="overload4.html#asio.reference.async_write_at.overload4.parameters">Parameters</a>
        </h6>
<div class="variablelist">
<p class="title"><b></b></p>
<dl>
<dt><span class="term">d</span></dt>
<dd><p>
                The device to which the data is to be written. The type must support
                the AsyncRandomAccessWriteDevice concept.
              </p></dd>
<dt><span class="term">offset</span></dt>
<dd><p>
                The offset at which the data will be written.
              </p></dd>
<dt><span class="term">b</span></dt>
<dd><p>
                A <a class="link" href="../basic_streambuf.html" title="basic_streambuf"><code class="computeroutput">basic_streambuf</code></a>
                object from which data will be written. Ownership of the streambuf
                is retained by the caller, which must guarantee that it remains valid
                until the handler is called.
              </p></dd>
<dt><span class="term">completion_condition</span></dt>
<dd>
<p>
                The function object to be called to determine whether the write operation
                is complete. The signature of the function object must be:
</p>
<pre class="programlisting">std::size_t completion_condition(
  // Result of latest async_write_some_at operation.
  const asio::error_code&amp; error,

  // Number of bytes transferred so far.
  std::size_t bytes_transferred
);
</pre>
<p>
                A return value of 0 indicates that the write operation is complete.
                A non-zero return value indicates the maximum number of bytes to
                be written on the next call to the device's async_write_some_at function.
              </p>
</dd>
<dt><span class="term">handler</span></dt>
<dd>
<p>
                The handler to be called when the write operation completes. Copies
                will be made of the handler as required. The function signature of
                the handler must be:
</p>
<pre class="programlisting">void handler(
  // Result of operation.
  const asio::error_code&amp; error,

  // Number of bytes written from the buffers. If an error
  // occurred, this will be less than the sum of the buffer sizes.
  std::size_t bytes_transferred
);
</pre>
<p>
                Regardless of whether the asynchronous operation completes immediately
                or not, the handler will not be invoked from within this function.
                On immediate completion, invocation of the handler will be performed
                in a manner equivalent to using <a class="link" href="../post.html" title="post"><code class="computeroutput">post</code></a>.
              </p>
</dd>
</dl>
</div>
</div>
<table xmlns:rev="http://www.cs.rpi.edu/~gregod/boost/tools/doc/revision" width="100%"><tr>
<td align="left"></td>
<td align="right"><div class="copyright-footer">Copyright © 2003-2021 Christopher
      M. Kohlhoff<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="overload3.html"><img src="../../../prev.png" alt="Prev"></a><a accesskey="u" href="../async_write_at.html"><img src="../../../up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../home.png" alt="Home"></a><a accesskey="n" href="../awaitable.html"><img src="../../../next.png" alt="Next"></a>
</div>
</body>
</html>
