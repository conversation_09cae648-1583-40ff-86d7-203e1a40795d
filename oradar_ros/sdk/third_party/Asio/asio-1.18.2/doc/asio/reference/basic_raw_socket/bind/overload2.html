<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>basic_raw_socket::bind (2 of 2 overloads)</title>
<link rel="stylesheet" href="../../../../boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.75.2">
<link rel="home" href="../../../../index.html" title="Asio">
<link rel="up" href="../bind.html" title="basic_raw_socket::bind">
<link rel="prev" href="overload1.html" title="basic_raw_socket::bind (1 of 2 overloads)">
<link rel="next" href="../broadcast.html" title="basic_raw_socket::broadcast">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr><td valign="top"><img alt="asio C++ library" width="250" height="60" src="../../../../asio.png"></td></tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="overload1.html"><img src="../../../../prev.png" alt="Prev"></a><a accesskey="u" href="../bind.html"><img src="../../../../up.png" alt="Up"></a><a accesskey="h" href="../../../../index.html"><img src="../../../../home.png" alt="Home"></a><a accesskey="n" href="../broadcast.html"><img src="../../../../next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h5 class="title">
<a name="asio.reference.basic_raw_socket.bind.overload2"></a><a class="link" href="overload2.html" title="basic_raw_socket::bind (2 of 2 overloads)">basic_raw_socket::bind
          (2 of 2 overloads)</a>
</h5></div></div></div>
<p>
            <span class="emphasis"><em>Inherited from basic_socket.</em></span>
          </p>
<p>
            Bind the socket to the given local endpoint.
          </p>
<pre class="programlisting">void bind(
    const endpoint_type &amp; endpoint,
    asio::error_code &amp; ec);
</pre>
<p>
            This function binds the socket to the specified endpoint on the local
            machine.
          </p>
<h6>
<a name="asio.reference.basic_raw_socket.bind.overload2.h0"></a>
            <span><a name="asio.reference.basic_raw_socket.bind.overload2.parameters"></a></span><a class="link" href="overload2.html#asio.reference.basic_raw_socket.bind.overload2.parameters">Parameters</a>
          </h6>
<div class="variablelist">
<p class="title"><b></b></p>
<dl>
<dt><span class="term">endpoint</span></dt>
<dd><p>
                  An endpoint on the local machine to which the socket will be bound.
                </p></dd>
<dt><span class="term">ec</span></dt>
<dd><p>
                  Set to indicate what error occurred, if any.
                </p></dd>
</dl>
</div>
<h6>
<a name="asio.reference.basic_raw_socket.bind.overload2.h1"></a>
            <span><a name="asio.reference.basic_raw_socket.bind.overload2.example"></a></span><a class="link" href="overload2.html#asio.reference.basic_raw_socket.bind.overload2.example">Example</a>
          </h6>
<pre class="programlisting">asio::ip::tcp::socket socket(my_context);
socket.open(asio::ip::tcp::v4());
asio::error_code ec;
socket.bind(asio::ip::tcp::endpoint(
      asio::ip::tcp::v4(), 12345), ec);
if (ec)
{
  // An error occurred.
}
</pre>
</div>
<table xmlns:rev="http://www.cs.rpi.edu/~gregod/boost/tools/doc/revision" width="100%"><tr>
<td align="left"></td>
<td align="right"><div class="copyright-footer">Copyright © 2003-2021 Christopher
      M. Kohlhoff<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="overload1.html"><img src="../../../../prev.png" alt="Prev"></a><a accesskey="u" href="../bind.html"><img src="../../../../up.png" alt="Up"></a><a accesskey="h" href="../../../../index.html"><img src="../../../../home.png" alt="Home"></a><a accesskey="n" href="../broadcast.html"><img src="../../../../next.png" alt="Next"></a>
</div>
</body>
</html>
