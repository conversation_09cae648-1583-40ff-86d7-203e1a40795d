<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>basic_raw_socket::receive_from (3 of 3 overloads)</title>
<link rel="stylesheet" href="../../../../boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.75.2">
<link rel="home" href="../../../../index.html" title="Asio">
<link rel="up" href="../receive_from.html" title="basic_raw_socket::receive_from">
<link rel="prev" href="overload2.html" title="basic_raw_socket::receive_from (2 of 3 overloads)">
<link rel="next" href="../receive_low_watermark.html" title="basic_raw_socket::receive_low_watermark">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr><td valign="top"><img alt="asio C++ library" width="250" height="60" src="../../../../asio.png"></td></tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="overload2.html"><img src="../../../../prev.png" alt="Prev"></a><a accesskey="u" href="../receive_from.html"><img src="../../../../up.png" alt="Up"></a><a accesskey="h" href="../../../../index.html"><img src="../../../../home.png" alt="Home"></a><a accesskey="n" href="../receive_low_watermark.html"><img src="../../../../next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h5 class="title">
<a name="asio.reference.basic_raw_socket.receive_from.overload3"></a><a class="link" href="overload3.html" title="basic_raw_socket::receive_from (3 of 3 overloads)">basic_raw_socket::receive_from
          (3 of 3 overloads)</a>
</h5></div></div></div>
<p>
            Receive raw data with the endpoint of the sender.
          </p>
<pre class="programlisting">template&lt;
    typename <a class="link" href="../../MutableBufferSequence.html" title="Mutable buffer sequence requirements">MutableBufferSequence</a>&gt;
std::size_t receive_from(
    const MutableBufferSequence &amp; buffers,
    endpoint_type &amp; sender_endpoint,
    socket_base::message_flags flags,
    asio::error_code &amp; ec);
</pre>
<p>
            This function is used to receive raw data. The function call will block
            until data has been received successfully or an error occurs.
          </p>
<h6>
<a name="asio.reference.basic_raw_socket.receive_from.overload3.h0"></a>
            <span><a name="asio.reference.basic_raw_socket.receive_from.overload3.parameters"></a></span><a class="link" href="overload3.html#asio.reference.basic_raw_socket.receive_from.overload3.parameters">Parameters</a>
          </h6>
<div class="variablelist">
<p class="title"><b></b></p>
<dl>
<dt><span class="term">buffers</span></dt>
<dd><p>
                  One or more buffers into which the data will be received.
                </p></dd>
<dt><span class="term">sender_endpoint</span></dt>
<dd><p>
                  An endpoint object that receives the endpoint of the remote sender
                  of the data.
                </p></dd>
<dt><span class="term">flags</span></dt>
<dd><p>
                  Flags specifying how the receive call is to be made.
                </p></dd>
<dt><span class="term">ec</span></dt>
<dd><p>
                  Set to indicate what error occurred, if any.
                </p></dd>
</dl>
</div>
<h6>
<a name="asio.reference.basic_raw_socket.receive_from.overload3.h1"></a>
            <span><a name="asio.reference.basic_raw_socket.receive_from.overload3.return_value"></a></span><a class="link" href="overload3.html#asio.reference.basic_raw_socket.receive_from.overload3.return_value">Return
            Value</a>
          </h6>
<p>
            The number of bytes received.
          </p>
</div>
<table xmlns:rev="http://www.cs.rpi.edu/~gregod/boost/tools/doc/revision" width="100%"><tr>
<td align="left"></td>
<td align="right"><div class="copyright-footer">Copyright © 2003-2021 Christopher
      M. Kohlhoff<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="overload2.html"><img src="../../../../prev.png" alt="Prev"></a><a accesskey="u" href="../receive_from.html"><img src="../../../../up.png" alt="Up"></a><a accesskey="h" href="../../../../index.html"><img src="../../../../home.png" alt="Home"></a><a accesskey="n" href="../receive_low_watermark.html"><img src="../../../../next.png" alt="Next"></a>
</div>
</body>
</html>
