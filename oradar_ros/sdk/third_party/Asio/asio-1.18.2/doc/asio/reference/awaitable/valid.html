<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>awaitable::valid</title>
<link rel="stylesheet" href="../../../boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.75.2">
<link rel="home" href="../../../index.html" title="Asio">
<link rel="up" href="../awaitable.html" title="awaitable">
<link rel="prev" href="executor_type.html" title="awaitable::executor_type">
<link rel="next" href="value_type.html" title="awaitable::value_type">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr><td valign="top"><img alt="asio C++ library" width="250" height="60" src="../../../asio.png"></td></tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="executor_type.html"><img src="../../../prev.png" alt="Prev"></a><a accesskey="u" href="../awaitable.html"><img src="../../../up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../home.png" alt="Home"></a><a accesskey="n" href="value_type.html"><img src="../../../next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="asio.reference.awaitable.valid"></a><a class="link" href="valid.html" title="awaitable::valid">awaitable::valid</a>
</h4></div></div></div>
<p>
          <a class="indexterm" name="asio.indexterm.awaitable.valid"></a> 
Checks if the awaitable refers
          to a future result.
        </p>
<pre class="programlisting">bool valid() const;
</pre>
</div>
<table xmlns:rev="http://www.cs.rpi.edu/~gregod/boost/tools/doc/revision" width="100%"><tr>
<td align="left"></td>
<td align="right"><div class="copyright-footer">Copyright © 2003-2021 Christopher
      M. Kohlhoff<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="executor_type.html"><img src="../../../prev.png" alt="Prev"></a><a accesskey="u" href="../awaitable.html"><img src="../../../up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../home.png" alt="Home"></a><a accesskey="n" href="value_type.html"><img src="../../../next.png" alt="Next"></a>
</div>
</body>
</html>
