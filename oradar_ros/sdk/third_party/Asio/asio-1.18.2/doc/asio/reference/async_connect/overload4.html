<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>async_connect (4 of 6 overloads)</title>
<link rel="stylesheet" href="../../../boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.75.2">
<link rel="home" href="../../../index.html" title="Asio">
<link rel="up" href="../async_connect.html" title="async_connect">
<link rel="prev" href="overload3.html" title="async_connect (3 of 6 overloads)">
<link rel="next" href="overload5.html" title="async_connect (5 of 6 overloads)">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr><td valign="top"><img alt="asio C++ library" width="250" height="60" src="../../../asio.png"></td></tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="overload3.html"><img src="../../../prev.png" alt="Prev"></a><a accesskey="u" href="../async_connect.html"><img src="../../../up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../home.png" alt="Home"></a><a accesskey="n" href="overload5.html"><img src="../../../next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="asio.reference.async_connect.overload4"></a><a class="link" href="overload4.html" title="async_connect (4 of 6 overloads)">async_connect
        (4 of 6 overloads)</a>
</h4></div></div></div>
<p>
          Asynchronously establishes a socket connection by trying each endpoint
          in a sequence.
        </p>
<pre class="programlisting">template&lt;
    typename <a class="link" href="../Protocol.html" title="Protocol requirements">Protocol</a>,
    typename <a class="link" href="../Executor1.html" title="Executor requirements">Executor</a>,
    typename <a class="link" href="../EndpointSequence.html" title="Endpoint sequence requirements">EndpointSequence</a>,
    typename <a class="link" href="../ConnectCondition.html" title="Connect condition requirements">ConnectCondition</a>,
    typename <a class="link" href="../RangeConnectHandler.html" title="Range connect handler requirements">RangeConnectHandler</a> = <a class="link" href="../asynchronous_operations.html#asio.reference.asynchronous_operations.default_completion_tokens"><span class="emphasis"><em>DEFAULT</em></span></a>&gt;
<a class="link" href="../asynchronous_operations.html#asio.reference.asynchronous_operations.automatic_deduction_of_initiating_function_return_type"><span class="emphasis"><em>DEDUCED</em></span></a> async_connect(
    basic_socket&lt; Protocol, Executor &gt; &amp; s,
    const EndpointSequence &amp; endpoints,
    ConnectCondition connect_condition,
    RangeConnectHandler &amp;&amp; handler = <a class="link" href="../asynchronous_operations.html#asio.reference.asynchronous_operations.default_completion_tokens"><span class="emphasis"><em>DEFAULT</em></span></a>,
    typename constraint&lt; is_endpoint_sequence&lt; EndpointSequence &gt;::value &gt;::type  = 0);
</pre>
<p>
          This function attempts to connect a socket to one of a sequence of endpoints.
          It does this by repeated calls to the socket's <code class="computeroutput">async_connect</code>
          member function, once for each endpoint in the sequence, until a connection
          is successfully established.
        </p>
<h6>
<a name="asio.reference.async_connect.overload4.h0"></a>
          <span><a name="asio.reference.async_connect.overload4.parameters"></a></span><a class="link" href="overload4.html#asio.reference.async_connect.overload4.parameters">Parameters</a>
        </h6>
<div class="variablelist">
<p class="title"><b></b></p>
<dl>
<dt><span class="term">s</span></dt>
<dd><p>
                The socket to be connected. If the socket is already open, it will
                be closed.
              </p></dd>
<dt><span class="term">endpoints</span></dt>
<dd><p>
                A sequence of endpoints.
              </p></dd>
<dt><span class="term">connect_condition</span></dt>
<dd>
<p>
                A function object that is called prior to each connection attempt.
                The signature of the function object must be:
</p>
<pre class="programlisting">bool connect_condition(
    const asio::error_code&amp; ec,
    const typename Protocol::endpoint&amp; next);
</pre>
<p>
                The <code class="computeroutput">ec</code> parameter contains the result from the most recent
                connect operation. Before the first connection attempt, <code class="computeroutput">ec</code>
                is always set to indicate success. The <code class="computeroutput">next</code> parameter
                is the next endpoint to be tried. The function object should return
                true if the next endpoint should be tried, and false if it should
                be skipped.
              </p>
</dd>
<dt><span class="term">handler</span></dt>
<dd>
<p>
                The handler to be called when the connect operation completes. Copies
                will be made of the handler as required. The function signature of
                the handler must be:
</p>
<pre class="programlisting">void handler(
  // Result of operation. if the sequence is empty, set to
  // asio::error::not_found. Otherwise, contains the
  // error from the last connection attempt.
  const asio::error_code&amp; error,

  // On success, an iterator denoting the successfully
  // connected endpoint. Otherwise, the end iterator.
  Iterator iterator
);
</pre>
<p>
                Regardless of whether the asynchronous operation completes immediately
                or not, the handler will not be invoked from within this function.
                On immediate completion, invocation of the handler will be performed
                in a manner equivalent to using <a class="link" href="../post.html" title="post"><code class="computeroutput">post</code></a>.
              </p>
</dd>
</dl>
</div>
<h6>
<a name="asio.reference.async_connect.overload4.h1"></a>
          <span><a name="asio.reference.async_connect.overload4.example"></a></span><a class="link" href="overload4.html#asio.reference.async_connect.overload4.example">Example</a>
        </h6>
<p>
          The following connect condition function object can be used to output information
          about the individual connection attempts:
        </p>
<pre class="programlisting">struct my_connect_condition
{
  bool operator()(
      const asio::error_code&amp; ec,
      const::tcp::endpoint&amp; next)
  {
    if (ec) std::cout &lt;&lt; "Error: " &lt;&lt; ec.message() &lt;&lt; std::endl;
    std::cout &lt;&lt; "Trying: " &lt;&lt; next &lt;&lt; std::endl;
    return true;
  }
};
</pre>
<p>
          It would be used with the <code class="computeroutput">asio::connect</code> function as follows:
        </p>
<pre class="programlisting">tcp::resolver r(my_context);
tcp::resolver::query q("host", "service");
tcp::socket s(my_context);

// ...

r.async_resolve(q, resolve_handler);

// ...

void resolve_handler(
    const asio::error_code&amp; ec,
    tcp::resolver::results_type results)
{
  if (!ec)
  {
    asio::async_connect(s, results,
        my_connect_condition(),
        connect_handler);
  }
}

// ...

void connect_handler(
    const asio::error_code&amp; ec,
    const tcp::endpoint&amp; endpoint)
{
  if (ec)
  {
    // An error occurred.
  }
  else
  {
    std::cout &lt;&lt; "Connected to: " &lt;&lt; endpoint &lt;&lt; std::endl;
  }
}
</pre>
</div>
<table xmlns:rev="http://www.cs.rpi.edu/~gregod/boost/tools/doc/revision" width="100%"><tr>
<td align="left"></td>
<td align="right"><div class="copyright-footer">Copyright © 2003-2021 Christopher
      M. Kohlhoff<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="overload3.html"><img src="../../../prev.png" alt="Prev"></a><a accesskey="u" href="../async_connect.html"><img src="../../../up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../home.png" alt="Home"></a><a accesskey="n" href="overload5.html"><img src="../../../next.png" alt="Next"></a>
</div>
</body>
</html>
