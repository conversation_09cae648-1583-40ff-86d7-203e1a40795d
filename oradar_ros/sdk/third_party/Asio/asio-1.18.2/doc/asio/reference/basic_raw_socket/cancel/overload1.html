<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>basic_raw_socket::cancel (1 of 2 overloads)</title>
<link rel="stylesheet" href="../../../../boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.75.2">
<link rel="home" href="../../../../index.html" title="Asio">
<link rel="up" href="../cancel.html" title="basic_raw_socket::cancel">
<link rel="prev" href="../cancel.html" title="basic_raw_socket::cancel">
<link rel="next" href="overload2.html" title="basic_raw_socket::cancel (2 of 2 overloads)">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr><td valign="top"><img alt="asio C++ library" width="250" height="60" src="../../../../asio.png"></td></tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="../cancel.html"><img src="../../../../prev.png" alt="Prev"></a><a accesskey="u" href="../cancel.html"><img src="../../../../up.png" alt="Up"></a><a accesskey="h" href="../../../../index.html"><img src="../../../../home.png" alt="Home"></a><a accesskey="n" href="overload2.html"><img src="../../../../next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h5 class="title">
<a name="asio.reference.basic_raw_socket.cancel.overload1"></a><a class="link" href="overload1.html" title="basic_raw_socket::cancel (1 of 2 overloads)">basic_raw_socket::cancel
          (1 of 2 overloads)</a>
</h5></div></div></div>
<p>
            <span class="emphasis"><em>Inherited from basic_socket.</em></span>
          </p>
<p>
            Cancel all asynchronous operations associated with the socket.
          </p>
<pre class="programlisting">void cancel();
</pre>
<p>
            This function causes all outstanding asynchronous connect, send and receive
            operations to finish immediately, and the handlers for cancelled operations
            will be passed the <code class="computeroutput">asio::error::operation_aborted</code> error.
          </p>
<h6>
<a name="asio.reference.basic_raw_socket.cancel.overload1.h0"></a>
            <span><a name="asio.reference.basic_raw_socket.cancel.overload1.exceptions"></a></span><a class="link" href="overload1.html#asio.reference.basic_raw_socket.cancel.overload1.exceptions">Exceptions</a>
          </h6>
<div class="variablelist">
<p class="title"><b></b></p>
<dl>
<dt><span class="term">asio::system_error</span></dt>
<dd><p>
                  Thrown on failure.
                </p></dd>
</dl>
</div>
<h6>
<a name="asio.reference.basic_raw_socket.cancel.overload1.h1"></a>
            <span><a name="asio.reference.basic_raw_socket.cancel.overload1.remarks"></a></span><a class="link" href="overload1.html#asio.reference.basic_raw_socket.cancel.overload1.remarks">Remarks</a>
          </h6>
<p>
            Calls to <code class="computeroutput">cancel()</code> will always fail with <code class="computeroutput">asio::error::operation_not_supported</code>
            when run on Windows XP, Windows Server 2003, and earlier versions of
            Windows, unless ASIO_ENABLE_CANCELIO is defined. However, the CancelIo
            function has two issues that should be considered before enabling its
            use:
          </p>
<div class="itemizedlist"><ul class="itemizedlist" type="disc">
<li class="listitem">
                It will only cancel asynchronous operations that were initiated in
                the current thread.
              </li>
<li class="listitem">
                It can appear to complete without error, but the request to cancel
                the unfinished operations may be silently ignored by the operating
                system. Whether it works or not seems to depend on the drivers that
                are installed.
              </li>
</ul></div>
<p>
            For portable cancellation, consider using one of the following alternatives:
          </p>
<div class="itemizedlist"><ul class="itemizedlist" type="disc">
<li class="listitem">
                Disable asio's I/O completion port backend by defining ASIO_DISABLE_IOCP.
              </li>
<li class="listitem">
                Use the <code class="computeroutput">close()</code> function to simultaneously cancel the
                outstanding operations and close the socket.
              </li>
</ul></div>
<p>
            When running on Windows Vista, Windows Server 2008, and later, the CancelIoEx
            function is always used. This function does not have the problems described
            above.
          </p>
</div>
<table xmlns:rev="http://www.cs.rpi.edu/~gregod/boost/tools/doc/revision" width="100%"><tr>
<td align="left"></td>
<td align="right"><div class="copyright-footer">Copyright © 2003-2021 Christopher
      M. Kohlhoff<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="../cancel.html"><img src="../../../../prev.png" alt="Prev"></a><a accesskey="u" href="../cancel.html"><img src="../../../../up.png" alt="Up"></a><a accesskey="h" href="../../../../index.html"><img src="../../../../home.png" alt="Home"></a><a accesskey="n" href="overload2.html"><img src="../../../../next.png" alt="Next"></a>
</div>
</body>
</html>
