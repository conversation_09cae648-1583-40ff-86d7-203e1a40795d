<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>basic_datagram_socket::linger</title>
<link rel="stylesheet" href="../../../boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.75.2">
<link rel="home" href="../../../index.html" title="Asio">
<link rel="up" href="../basic_datagram_socket.html" title="basic_datagram_socket">
<link rel="prev" href="keep_alive.html" title="basic_datagram_socket::keep_alive">
<link rel="next" href="local_endpoint.html" title="basic_datagram_socket::local_endpoint">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr><td valign="top"><img alt="asio C++ library" width="250" height="60" src="../../../asio.png"></td></tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="keep_alive.html"><img src="../../../prev.png" alt="Prev"></a><a accesskey="u" href="../basic_datagram_socket.html"><img src="../../../up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../home.png" alt="Home"></a><a accesskey="n" href="local_endpoint.html"><img src="../../../next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="asio.reference.basic_datagram_socket.linger"></a><a class="link" href="linger.html" title="basic_datagram_socket::linger">basic_datagram_socket::linger</a>
</h4></div></div></div>
<p>
          <span class="emphasis"><em>Inherited from socket_base.</em></span>
        </p>
<p>
          <a class="indexterm" name="asio.indexterm.basic_datagram_socket.linger"></a> 
Socket
          option to specify whether the socket lingers on close if unsent data is
          present.
        </p>
<pre class="programlisting">typedef implementation_defined linger;
</pre>
<p>
          Implements the SOL_SOCKET/SO_LINGER socket option.
        </p>
<h6>
<a name="asio.reference.basic_datagram_socket.linger.h0"></a>
          <span><a name="asio.reference.basic_datagram_socket.linger.examples"></a></span><a class="link" href="linger.html#asio.reference.basic_datagram_socket.linger.examples">Examples</a>
        </h6>
<p>
          Setting the option:
        </p>
<pre class="programlisting">asio::ip::tcp::socket socket(my_context);
...
asio::socket_base::linger option(true, 30);
socket.set_option(option);
</pre>
<p>
          Getting the current option value:
        </p>
<pre class="programlisting">asio::ip::tcp::socket socket(my_context);
...
asio::socket_base::linger option;
socket.get_option(option);
bool is_set = option.enabled();
unsigned short timeout = option.timeout();
</pre>
<h6>
<a name="asio.reference.basic_datagram_socket.linger.h1"></a>
          <span><a name="asio.reference.basic_datagram_socket.linger.requirements"></a></span><a class="link" href="linger.html#asio.reference.basic_datagram_socket.linger.requirements">Requirements</a>
        </h6>
<p>
          <span class="emphasis"><em>Header: </em></span><code class="literal">asio/basic_datagram_socket.hpp</code>
        </p>
<p>
          <span class="emphasis"><em>Convenience header: </em></span><code class="literal">asio.hpp</code>
        </p>
</div>
<table xmlns:rev="http://www.cs.rpi.edu/~gregod/boost/tools/doc/revision" width="100%"><tr>
<td align="left"></td>
<td align="right"><div class="copyright-footer">Copyright © 2003-2021 Christopher
      M. Kohlhoff<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="keep_alive.html"><img src="../../../prev.png" alt="Prev"></a><a accesskey="u" href="../basic_datagram_socket.html"><img src="../../../up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../home.png" alt="Home"></a><a accesskey="n" href="local_endpoint.html"><img src="../../../next.png" alt="Next"></a>
</div>
</body>
</html>
