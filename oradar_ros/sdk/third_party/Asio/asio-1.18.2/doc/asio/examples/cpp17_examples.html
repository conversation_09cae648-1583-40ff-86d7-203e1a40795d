<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>C++17 Examples</title>
<link rel="stylesheet" href="../../boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.75.2">
<link rel="home" href="../../index.html" title="Asio">
<link rel="up" href="../examples.html" title="Examples">
<link rel="prev" href="cpp14_examples.html" title="C++14 Examples">
<link rel="next" href="../reference.html" title="Reference">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr><td valign="top"><img alt="asio C++ library" width="250" height="60" src="../../asio.png"></td></tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="cpp14_examples.html"><img src="../../prev.png" alt="Prev"></a><a accesskey="u" href="../examples.html"><img src="../../up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../home.png" alt="Home"></a><a accesskey="n" href="../reference.html"><img src="../../next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="asio.examples.cpp17_examples"></a><a class="link" href="cpp17_examples.html" title="C++17 Examples">C++17 Examples</a>
</h3></div></div></div>
<h5>
<a name="asio.examples.cpp17_examples.h0"></a>
        <span><a name="asio.examples.cpp17_examples.coroutines_ts_support"></a></span><a class="link" href="cpp17_examples.html#asio.examples.cpp17_examples.coroutines_ts_support">Coroutines
        TS Support</a>
      </h5>
<p>
        Examples showing how to implement a chain of asynchronous operations using
        the Coroutines TS.
      </p>
<div class="itemizedlist"><ul class="itemizedlist" type="disc">
<li class="listitem">
            <a href="../../../src/examples/cpp17/coroutines_ts/echo_server.cpp" target="_top">../src/examples/cpp17/coroutines_ts/echo_server.cpp</a>
          </li>
<li class="listitem">
            <a href="../../../src/examples/cpp17/coroutines_ts/refactored_echo_server.cpp" target="_top">../src/examples/cpp17/coroutines_ts/refactored_echo_server.cpp</a>
          </li>
<li class="listitem">
            <a href="../../../src/examples/cpp17/coroutines_ts/chat_server.cpp" target="_top">../src/examples/cpp17/coroutines_ts/chat_server.cpp</a>
          </li>
<li class="listitem">
            <a href="../../../src/examples/cpp17/coroutines_ts/range_based_for.cpp" target="_top">../src/examples/cpp17/coroutines_ts/range_based_for.cpp</a>
          </li>
</ul></div>
</div>
<table xmlns:rev="http://www.cs.rpi.edu/~gregod/boost/tools/doc/revision" width="100%"><tr>
<td align="left"></td>
<td align="right"><div class="copyright-footer">Copyright © 2003-2021 Christopher
      M. Kohlhoff<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="cpp14_examples.html"><img src="../../prev.png" alt="Prev"></a><a accesskey="u" href="../examples.html"><img src="../../up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../home.png" alt="Home"></a><a accesskey="n" href="../reference.html"><img src="../../next.png" alt="Next"></a>
</div>
</body>
</html>
