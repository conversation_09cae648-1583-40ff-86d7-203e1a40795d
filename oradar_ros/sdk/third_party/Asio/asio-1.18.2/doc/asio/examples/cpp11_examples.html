<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>C++11 Examples</title>
<link rel="stylesheet" href="../../boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.75.2">
<link rel="home" href="../../index.html" title="Asio">
<link rel="up" href="../examples.html" title="Examples">
<link rel="prev" href="cpp03_examples.html" title="C++03 Examples">
<link rel="next" href="cpp14_examples.html" title="C++14 Examples">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr><td valign="top"><img alt="asio C++ library" width="250" height="60" src="../../asio.png"></td></tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="cpp03_examples.html"><img src="../../prev.png" alt="Prev"></a><a accesskey="u" href="../examples.html"><img src="../../up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../home.png" alt="Home"></a><a accesskey="n" href="cpp14_examples.html"><img src="../../next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="asio.examples.cpp11_examples"></a><a class="link" href="cpp11_examples.html" title="C++11 Examples">C++11 Examples</a>
</h3></div></div></div>
<h5>
<a name="asio.examples.cpp11_examples.h0"></a>
        <span><a name="asio.examples.cpp11_examples.allocation"></a></span><a class="link" href="cpp11_examples.html#asio.examples.cpp11_examples.allocation">Allocation</a>
      </h5>
<p>
        This example shows how to customise the allocation of memory associated with
        asynchronous operations.
      </p>
<div class="itemizedlist"><ul class="itemizedlist" type="disc"><li class="listitem">
            <a href="../../../src/examples/cpp11/allocation/server.cpp" target="_top">../src/examples/cpp11/allocation/server.cpp</a>
            (<a href="../../examples/diffs/allocation/server.cpp.html" target="_top">diff to C++03</a>)
          </li></ul></div>
<h5>
<a name="asio.examples.cpp11_examples.h1"></a>
        <span><a name="asio.examples.cpp11_examples.buffers"></a></span><a class="link" href="cpp11_examples.html#asio.examples.cpp11_examples.buffers">Buffers</a>
      </h5>
<p>
        This example demonstrates how to create reference counted buffers that can
        be used with socket read and write operations.
      </p>
<div class="itemizedlist"><ul class="itemizedlist" type="disc"><li class="listitem">
            <a href="../../../src/examples/cpp11/buffers/reference_counted.cpp" target="_top">../src/examples/cpp11/buffers/reference_counted.cpp</a>
            (<a href="../../examples/diffs/buffers/reference_counted.cpp.html" target="_top">diff
            to C++03</a>)
          </li></ul></div>
<h5>
<a name="asio.examples.cpp11_examples.h2"></a>
        <span><a name="asio.examples.cpp11_examples.chat"></a></span><a class="link" href="cpp11_examples.html#asio.examples.cpp11_examples.chat">Chat</a>
      </h5>
<p>
        This example implements a chat server and client. The programs use a custom
        protocol with a fixed length message header and variable length message body.
      </p>
<div class="itemizedlist"><ul class="itemizedlist" type="disc">
<li class="listitem">
            <a href="../../../src/examples/cpp11/chat/chat_message.hpp" target="_top">../src/examples/cpp11/chat/chat_message.hpp</a>
            (<a href="../../examples/diffs/chat/chat_message.hpp.html" target="_top">diff to C++03</a>)
          </li>
<li class="listitem">
            <a href="../../../src/examples/cpp11/chat/chat_client.cpp" target="_top">../src/examples/cpp11/chat/chat_client.cpp</a>
            (<a href="../../examples/diffs/chat/chat_client.cpp.html" target="_top">diff to C++03</a>)
          </li>
<li class="listitem">
            <a href="../../../src/examples/cpp11/chat/chat_server.cpp" target="_top">../src/examples/cpp11/chat/chat_server.cpp</a>
            (<a href="../../examples/diffs/chat/chat_server.cpp.html" target="_top">diff to C++03</a>)
          </li>
</ul></div>
<h5>
<a name="asio.examples.cpp11_examples.h3"></a>
        <span><a name="asio.examples.cpp11_examples.echo"></a></span><a class="link" href="cpp11_examples.html#asio.examples.cpp11_examples.echo">Echo</a>
      </h5>
<p>
        A collection of simple clients and servers, showing the use of both synchronous
        and asynchronous operations.
      </p>
<div class="itemizedlist"><ul class="itemizedlist" type="disc">
<li class="listitem">
            <a href="../../../src/examples/cpp11/echo/async_tcp_echo_server.cpp" target="_top">../src/examples/cpp11/echo/async_tcp_echo_server.cpp</a>
            (<a href="../../examples/diffs/echo/async_tcp_echo_server.cpp.html" target="_top">diff
            to C++03</a>)
          </li>
<li class="listitem">
            <a href="../../../src/examples/cpp11/echo/async_udp_echo_server.cpp" target="_top">../src/examples/cpp11/echo/async_udp_echo_server.cpp</a>
            (<a href="../../examples/diffs/echo/async_udp_echo_server.cpp.html" target="_top">diff
            to C++03</a>)
          </li>
<li class="listitem">
            <a href="../../../src/examples/cpp11/echo/blocking_tcp_echo_client.cpp" target="_top">../src/examples/cpp11/echo/blocking_tcp_echo_client.cpp</a>
            (<a href="../../examples/diffs/echo/blocking_tcp_echo_client.cpp.html" target="_top">diff
            to C++03</a>)
          </li>
<li class="listitem">
            <a href="../../../src/examples/cpp11/echo/blocking_tcp_echo_server.cpp" target="_top">../src/examples/cpp11/echo/blocking_tcp_echo_server.cpp</a>
            (<a href="../../examples/diffs/echo/blocking_tcp_echo_server.cpp.html" target="_top">diff
            to C++03</a>)
          </li>
<li class="listitem">
            <a href="../../../src/examples/cpp11/echo/blocking_udp_echo_client.cpp" target="_top">../src/examples/cpp11/echo/blocking_udp_echo_client.cpp</a>
            (<a href="../../examples/diffs/echo/blocking_udp_echo_client.cpp.html" target="_top">diff
            to C++03</a>)
          </li>
<li class="listitem">
            <a href="../../../src/examples/cpp11/echo/blocking_udp_echo_server.cpp" target="_top">../src/examples/cpp11/echo/blocking_udp_echo_server.cpp</a>
            (<a href="../../examples/diffs/echo/blocking_udp_echo_server.cpp.html" target="_top">diff
            to C++03</a>)
          </li>
</ul></div>
<h5>
<a name="asio.examples.cpp11_examples.h4"></a>
        <span><a name="asio.examples.cpp11_examples.fork"></a></span><a class="link" href="cpp11_examples.html#asio.examples.cpp11_examples.fork">Fork</a>
      </h5>
<p>
        These POSIX-specific examples show how to use Asio in conjunction with the
        <code class="computeroutput">fork()</code> system call. The first example illustrates the steps
        required to start a daemon process:
      </p>
<div class="itemizedlist"><ul class="itemizedlist" type="disc"><li class="listitem">
            <a href="../../../src/examples/cpp11/fork/daemon.cpp" target="_top">../src/examples/cpp11/fork/daemon.cpp</a>
            (<a href="../../examples/diffs/fork/daemon.cpp.html" target="_top">diff to C++03</a>)
          </li></ul></div>
<p>
        The second example demonstrates how it is possible to fork a process from
        within a completion handler.
      </p>
<div class="itemizedlist"><ul class="itemizedlist" type="disc"><li class="listitem">
            <a href="../../../src/examples/cpp11/fork/process_per_connection.cpp" target="_top">../src/examples/cpp11/fork/process_per_connection.cpp</a>
            (<a href="../../examples/diffs/fork/process_per_connection.cpp.html" target="_top">diff
            to C++03</a>)
          </li></ul></div>
<h5>
<a name="asio.examples.cpp11_examples.h5"></a>
        <span><a name="asio.examples.cpp11_examples.futures"></a></span><a class="link" href="cpp11_examples.html#asio.examples.cpp11_examples.futures">Futures</a>
      </h5>
<p>
        This example demonstrates how to use std::future in conjunction with Asio's
        asynchronous operations.
      </p>
<div class="itemizedlist"><ul class="itemizedlist" type="disc"><li class="listitem">
            <a href="../../../src/examples/cpp11/futures/daytime_client.cpp" target="_top">../src/examples/cpp11/futures/daytime_client.cpp</a>
          </li></ul></div>
<h5>
<a name="asio.examples.cpp11_examples.h6"></a>
        <span><a name="asio.examples.cpp11_examples.handler_tracking"></a></span><a class="link" href="cpp11_examples.html#asio.examples.cpp11_examples.handler_tracking">Handler
        Tracking</a>
      </h5>
<p>
        This example header file shows how to implement custom handler tracking.
      </p>
<div class="itemizedlist"><ul class="itemizedlist" type="disc"><li class="listitem">
            <a href="../../../src/examples/cpp11/handler_tracking/custom_tracking.hpp" target="_top">../src/examples/cpp11/handler_tracking/custom_tracking.hpp</a>
          </li></ul></div>
<p>
        This example program shows how to include source location information in
        the handler tracking output.
      </p>
<div class="itemizedlist"><ul class="itemizedlist" type="disc"><li class="listitem">
            <a href="../../../src/examples/cpp11/handler_tracking/async_tcp_echo_server.cpp" target="_top">../src/examples/cpp11/handler_tracking/async_tcp_echo_server.cpp</a>
          </li></ul></div>
<h5>
<a name="asio.examples.cpp11_examples.h7"></a>
        <span><a name="asio.examples.cpp11_examples.http_server"></a></span><a class="link" href="cpp11_examples.html#asio.examples.cpp11_examples.http_server">HTTP
        Server</a>
      </h5>
<p>
        This example illustrates the use of asio in a simple single-threaded server
        implementation of HTTP 1.0. It demonstrates how to perform a clean shutdown
        by cancelling all outstanding asynchronous operations.
      </p>
<div class="itemizedlist"><ul class="itemizedlist" type="disc">
<li class="listitem">
            <a href="../../../src/examples/cpp11/http/server/connection.cpp" target="_top">../src/examples/cpp11/http/server/connection.cpp</a>
            (<a href="../../examples/diffs/http/server/connection.cpp.html" target="_top">diff to
            C++03</a>)
          </li>
<li class="listitem">
            <a href="../../../src/examples/cpp11/http/server/connection.hpp" target="_top">../src/examples/cpp11/http/server/connection.hpp</a>
            (<a href="../../examples/diffs/http/server/connection.hpp.html" target="_top">diff to
            C++03</a>)
          </li>
<li class="listitem">
            <a href="../../../src/examples/cpp11/http/server/connection_manager.cpp" target="_top">../src/examples/cpp11/http/server/connection_manager.cpp</a>
            (<a href="../../examples/diffs/http/server/connection_manager.cpp.html" target="_top">diff
            to C++03</a>)
          </li>
<li class="listitem">
            <a href="../../../src/examples/cpp11/http/server/connection_manager.hpp" target="_top">../src/examples/cpp11/http/server/connection_manager.hpp</a>
            (<a href="../../examples/diffs/http/server/connection_manager.hpp.html" target="_top">diff
            to C++03</a>)
          </li>
<li class="listitem">
            <a href="../../../src/examples/cpp11/http/server/header.hpp" target="_top">../src/examples/cpp11/http/server/header.hpp</a>
            (<a href="../../examples/diffs/http/server/header.hpp.html" target="_top">diff to C++03</a>)
          </li>
<li class="listitem">
            <a href="../../../src/examples/cpp11/http/server/main.cpp" target="_top">../src/examples/cpp11/http/server/main.cpp</a>
            (<a href="../../examples/diffs/http/server/main.cpp.html" target="_top">diff to C++03</a>)
          </li>
<li class="listitem">
            <a href="../../../src/examples/cpp11/http/server/mime_types.cpp" target="_top">../src/examples/cpp11/http/server/mime_types.cpp</a>
            (<a href="../../examples/diffs/http/server/mime_types.cpp.html" target="_top">diff to
            C++03</a>)
          </li>
<li class="listitem">
            <a href="../../../src/examples/cpp11/http/server/mime_types.hpp" target="_top">../src/examples/cpp11/http/server/mime_types.hpp</a>
            (<a href="../../examples/diffs/http/server/mime_types.hpp.html" target="_top">diff to
            C++03</a>)
          </li>
<li class="listitem">
            <a href="../../../src/examples/cpp11/http/server/reply.cpp" target="_top">../src/examples/cpp11/http/server/reply.cpp</a>
            (<a href="../../examples/diffs/http/server/reply.cpp.html" target="_top">diff to C++03</a>)
          </li>
<li class="listitem">
            <a href="../../../src/examples/cpp11/http/server/reply.hpp" target="_top">../src/examples/cpp11/http/server/reply.hpp</a>
            (<a href="../../examples/diffs/http/server/reply.hpp.html" target="_top">diff to C++03</a>)
          </li>
<li class="listitem">
            <a href="../../../src/examples/cpp11/http/server/request.hpp" target="_top">../src/examples/cpp11/http/server/request.hpp</a>
            (<a href="../../examples/diffs/http/server/request.hpp.html" target="_top">diff to C++03</a>)
          </li>
<li class="listitem">
            <a href="../../../src/examples/cpp11/http/server/request_handler.cpp" target="_top">../src/examples/cpp11/http/server/request_handler.cpp</a>
            (<a href="../../examples/diffs/http/server/request_handler.cpp.html" target="_top">diff
            to C++03</a>)
          </li>
<li class="listitem">
            <a href="../../../src/examples/cpp11/http/server/request_handler.hpp" target="_top">../src/examples/cpp11/http/server/request_handler.hpp</a>
            (<a href="../../examples/diffs/http/server/request_handler.hpp.html" target="_top">diff
            to C++03</a>)
          </li>
<li class="listitem">
            <a href="../../../src/examples/cpp11/http/server/request_parser.cpp" target="_top">../src/examples/cpp11/http/server/request_parser.cpp</a>
            (<a href="../../examples/diffs/http/server/request_parser.cpp.html" target="_top">diff
            to C++03</a>)
          </li>
<li class="listitem">
            <a href="../../../src/examples/cpp11/http/server/request_parser.hpp" target="_top">../src/examples/cpp11/http/server/request_parser.hpp</a>
            (<a href="../../examples/diffs/http/server/request_parser.hpp.html" target="_top">diff
            to C++03</a>)
          </li>
<li class="listitem">
            <a href="../../../src/examples/cpp11/http/server/server.cpp" target="_top">../src/examples/cpp11/http/server/server.cpp</a>
            (<a href="../../examples/diffs/http/server/server.cpp.html" target="_top">diff to C++03</a>)
          </li>
<li class="listitem">
            <a href="../../../src/examples/cpp11/http/server/server.hpp" target="_top">../src/examples/cpp11/http/server/server.hpp</a>
            (<a href="../../examples/diffs/http/server/server.hpp.html" target="_top">diff to C++03</a>)
          </li>
</ul></div>
<h5>
<a name="asio.examples.cpp11_examples.h8"></a>
        <span><a name="asio.examples.cpp11_examples.multicast"></a></span><a class="link" href="cpp11_examples.html#asio.examples.cpp11_examples.multicast">Multicast</a>
      </h5>
<p>
        An example showing the use of multicast to transmit packets to a group of
        subscribers.
      </p>
<div class="itemizedlist"><ul class="itemizedlist" type="disc">
<li class="listitem">
            <a href="../../../src/examples/cpp11/multicast/receiver.cpp" target="_top">../src/examples/cpp11/multicast/receiver.cpp</a>
            (<a href="../../examples/diffs/multicast/receiver.cpp.html" target="_top">diff to C++03</a>)
          </li>
<li class="listitem">
            <a href="../../../src/examples/cpp11/multicast/sender.cpp" target="_top">../src/examples/cpp11/multicast/sender.cpp</a>
            (<a href="../../examples/diffs/multicast/sender.cpp.html" target="_top">diff to C++03</a>)
          </li>
</ul></div>
<h5>
<a name="asio.examples.cpp11_examples.h9"></a>
        <span><a name="asio.examples.cpp11_examples.nonblocking"></a></span><a class="link" href="cpp11_examples.html#asio.examples.cpp11_examples.nonblocking">Nonblocking</a>
      </h5>
<p>
        Example demonstrating reactor-style operations for integrating a third-party
        library that wants to perform the I/O operations itself.
      </p>
<div class="itemizedlist"><ul class="itemizedlist" type="disc"><li class="listitem">
            <a href="../../../src/examples/cpp11/nonblocking/third_party_lib.cpp" target="_top">../src/examples/cpp11/nonblocking/third_party_lib.cpp</a>
            (<a href="../../examples/diffs/nonblocking/third_party_lib.cpp.html" target="_top">diff
            to C++03</a>)
          </li></ul></div>
<h5>
<a name="asio.examples.cpp11_examples.h10"></a>
        <span><a name="asio.examples.cpp11_examples.operations"></a></span><a class="link" href="cpp11_examples.html#asio.examples.cpp11_examples.operations">Operations</a>
      </h5>
<p>
        Examples showing how to implement composed asynchronous operations as reusable
        library functions.
      </p>
<div class="itemizedlist"><ul class="itemizedlist" type="disc">
<li class="listitem">
            <a href="../../../src/examples/cpp11/operations/composed_1.cpp" target="_top">../src/examples/cpp11/operations/composed_1.cpp</a>
          </li>
<li class="listitem">
            <a href="../../../src/examples/cpp11/operations/composed_2.cpp" target="_top">../src/examples/cpp11/operations/composed_2.cpp</a>
          </li>
<li class="listitem">
            <a href="../../../src/examples/cpp11/operations/composed_3.cpp" target="_top">../src/examples/cpp11/operations/composed_3.cpp</a>
          </li>
<li class="listitem">
            <a href="../../../src/examples/cpp11/operations/composed_4.cpp" target="_top">../src/examples/cpp11/operations/composed_4.cpp</a>
          </li>
<li class="listitem">
            <a href="../../../src/examples/cpp11/operations/composed_5.cpp" target="_top">../src/examples/cpp11/operations/composed_5.cpp</a>
          </li>
<li class="listitem">
            <a href="../../../src/examples/cpp11/operations/composed_6.cpp" target="_top">../src/examples/cpp11/operations/composed_6.cpp</a>
          </li>
<li class="listitem">
            <a href="../../../src/examples/cpp11/operations/composed_7.cpp" target="_top">../src/examples/cpp11/operations/composed_7.cpp</a>
          </li>
<li class="listitem">
            <a href="../../../src/examples/cpp11/operations/composed_8.cpp" target="_top">../src/examples/cpp11/operations/composed_8.cpp</a>
          </li>
</ul></div>
<h5>
<a name="asio.examples.cpp11_examples.h11"></a>
        <span><a name="asio.examples.cpp11_examples.socks_4"></a></span><a class="link" href="cpp11_examples.html#asio.examples.cpp11_examples.socks_4">SOCKS
        4</a>
      </h5>
<p>
        Example client program implementing the SOCKS 4 protocol for communication
        via a proxy.
      </p>
<div class="itemizedlist"><ul class="itemizedlist" type="disc">
<li class="listitem">
            <a href="../../../src/examples/cpp11/socks4/sync_client.cpp" target="_top">../src/examples/cpp11/socks4/sync_client.cpp</a>
            (<a href="../../examples/diffs/socks4/sync_client.cpp.html" target="_top">diff to C++03</a>)
          </li>
<li class="listitem">
            <a href="../../../src/examples/cpp11/socks4/socks4.hpp" target="_top">../src/examples/cpp11/socks4/socks4.hpp</a>
            (<a href="../../examples/diffs/socks4/socks4.hpp.html" target="_top">diff to C++03</a>)
          </li>
</ul></div>
<h5>
<a name="asio.examples.cpp11_examples.h12"></a>
        <span><a name="asio.examples.cpp11_examples.spawn"></a></span><a class="link" href="cpp11_examples.html#asio.examples.cpp11_examples.spawn">Spawn</a>
      </h5>
<p>
        Example of using the asio::spawn() function, a wrapper around the <a href="http://www.boost.org/doc/libs/release/libs/coroutine/index.html" target="_top">Boost.Coroutine</a>
        library, to implement a chain of asynchronous operations using stackful coroutines.
      </p>
<div class="itemizedlist"><ul class="itemizedlist" type="disc"><li class="listitem">
            <a href="../../../src/examples/cpp11/spawn/echo_server.cpp" target="_top">../src/examples/cpp11/spawn/echo_server.cpp</a>
            (<a href="../../examples/diffs/spawn/echo_server.cpp.html" target="_top">diff to C++03</a>)
          </li></ul></div>
<h5>
<a name="asio.examples.cpp11_examples.h13"></a>
        <span><a name="asio.examples.cpp11_examples.ssl"></a></span><a class="link" href="cpp11_examples.html#asio.examples.cpp11_examples.ssl">SSL</a>
      </h5>
<p>
        Example client and server programs showing the use of the <a class="link" href="../reference/ssl__stream.html" title="ssl::stream">ssl::stream&lt;&gt;</a>
        template with asynchronous operations.
      </p>
<div class="itemizedlist"><ul class="itemizedlist" type="disc">
<li class="listitem">
            <a href="../../../src/examples/cpp11/ssl/client.cpp" target="_top">../src/examples/cpp11/ssl/client.cpp</a>
            (<a href="../../examples/diffs/ssl/client.cpp.html" target="_top">diff to C++03</a>)
          </li>
<li class="listitem">
            <a href="../../../src/examples/cpp11/ssl/server.cpp" target="_top">../src/examples/cpp11/ssl/server.cpp</a>
            (<a href="../../examples/diffs/ssl/server.cpp.html" target="_top">diff to C++03</a>)
          </li>
</ul></div>
<h5>
<a name="asio.examples.cpp11_examples.h14"></a>
        <span><a name="asio.examples.cpp11_examples.timeouts"></a></span><a class="link" href="cpp11_examples.html#asio.examples.cpp11_examples.timeouts">Timeouts</a>
      </h5>
<p>
        A collection of examples showing how to cancel long running asynchronous
        operations after a period of time.
      </p>
<div class="itemizedlist"><ul class="itemizedlist" type="disc">
<li class="listitem">
            <a href="../../../src/examples/cpp11/timeouts/async_tcp_client.cpp" target="_top">../src/examples/cpp11/timeouts/async_tcp_client.cpp</a>
            (<a href="../../examples/diffs/timeouts/async_tcp_client.cpp.html" target="_top">diff
            to C++03</a>)
          </li>
<li class="listitem">
            <a href="../../../src/examples/cpp11/timeouts/blocking_tcp_client.cpp" target="_top">../src/examples/cpp11/timeouts/blocking_tcp_client.cpp</a>
            (<a href="../../examples/diffs/timeouts/blocking_tcp_client.cpp.html" target="_top">diff
            to C++03</a>)
          </li>
<li class="listitem">
            <a href="../../../src/examples/cpp11/timeouts/blocking_token_tcp_client.cpp" target="_top">../src/examples/cpp11/timeouts/blocking_token_tcp_client.cpp</a>
            (<a href="../../examples/diffs/timeouts/blocking_token_tcp_client.cpp.html" target="_top">diff
            to C++03</a>)
          </li>
<li class="listitem">
            <a href="../../../src/examples/cpp11/timeouts/blocking_udp_client.cpp" target="_top">../src/examples/cpp11/timeouts/blocking_udp_client.cpp</a>
            (<a href="../../examples/diffs/timeouts/blocking_udp_client.cpp.html" target="_top">diff
            to C++03</a>)
          </li>
<li class="listitem">
            <a href="../../../src/examples/cpp11/timeouts/server.cpp" target="_top">../src/examples/cpp11/timeouts/server.cpp</a>
            (<a href="../../examples/diffs/timeouts/server.cpp.html" target="_top">diff to C++03</a>)
          </li>
</ul></div>
<h5>
<a name="asio.examples.cpp11_examples.h15"></a>
        <span><a name="asio.examples.cpp11_examples.timers"></a></span><a class="link" href="cpp11_examples.html#asio.examples.cpp11_examples.timers">Timers</a>
      </h5>
<p>
        Example showing how to customise basic_waitable_timer using a different clock
        type.
      </p>
<div class="itemizedlist"><ul class="itemizedlist" type="disc"><li class="listitem">
            <a href="../../../src/examples/cpp11/timers/time_t_timer.cpp" target="_top">../src/examples/cpp11/timers/time_t_timer.cpp</a>
            (<a href="../../examples/diffs/timers/time_t_timer.cpp.html" target="_top">diff to C++03</a>)
          </li></ul></div>
<h5>
<a name="asio.examples.cpp11_examples.h16"></a>
        <span><a name="asio.examples.cpp11_examples.unix_domain_sockets"></a></span><a class="link" href="cpp11_examples.html#asio.examples.cpp11_examples.unix_domain_sockets">UNIX
        Domain Sockets</a>
      </h5>
<p>
        Examples showing how to use UNIX domain (local) sockets.
      </p>
<div class="itemizedlist"><ul class="itemizedlist" type="disc">
<li class="listitem">
            <a href="../../../src/examples/cpp11/local/connect_pair.cpp" target="_top">../src/examples/cpp11/local/connect_pair.cpp</a>
            (<a href="../../examples/diffs/local/connect_pair.cpp.html" target="_top">diff to C++03</a>)
          </li>
<li class="listitem">
            <a href="../../../src/examples/cpp11/local/iostream_client.cpp" target="_top">../src/examples/cpp11/local/iostream_client.cpp</a>
            (<a href="../../examples/diffs/local/iostream_client.cpp.html" target="_top">diff to C++03</a>)
          </li>
<li class="listitem">
            <a href="../../../src/examples/cpp11/local/stream_server.cpp" target="_top">../src/examples/cpp11/local/stream_server.cpp</a>
            (<a href="../../examples/diffs/local/stream_server.cpp.html" target="_top">diff to C++03</a>)
          </li>
<li class="listitem">
            <a href="../../../src/examples/cpp11/local/stream_client.cpp" target="_top">../src/examples/cpp11/local/stream_client.cpp</a>
            (<a href="../../examples/diffs/local/stream_client.cpp.html" target="_top">diff to C++03</a>)
          </li>
</ul></div>
</div>
<table xmlns:rev="http://www.cs.rpi.edu/~gregod/boost/tools/doc/revision" width="100%"><tr>
<td align="left"></td>
<td align="right"><div class="copyright-footer">Copyright © 2003-2021 Christopher
      M. Kohlhoff<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="cpp03_examples.html"><img src="../../prev.png" alt="Prev"></a><a accesskey="u" href="../examples.html"><img src="../../up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../home.png" alt="Home"></a><a accesskey="n" href="cpp14_examples.html"><img src="../../next.png" alt="Next"></a>
</div>
</body>
</html>
