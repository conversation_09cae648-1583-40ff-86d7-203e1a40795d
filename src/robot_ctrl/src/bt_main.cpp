#include <memory>
#include <rclcpp/rclcpp.hpp>

#include "behaviortree_cpp_v3/bt_factory.h"
#include "behaviortree_cpp_v3/loggers/bt_cout_logger.h"
#include "behaviortree_cpp_v3/loggers/bt_zmq_publisher.h"

#include "robot_ctrl/bt_plugins/get_goal_from_room_action.hpp"
#include "robot_ctrl/bt_plugins/clear_task_command_action.hpp"
#include "robot_ctrl/bt_plugins/is_task_command_condition.hpp"
#include "robot_ctrl/bt_plugins/wait_for_new_command_condition.hpp"
#include "robot_ctrl/bt_plugins/nav_to_pose_action.hpp"
#include "robot_ctrl/bt_plugins/get_arm_goal_from_tf_action.hpp"
#include "robot_ctrl/bt_plugins/send_arm_goal_action.hpp"
#include "robot_ctrl/bt_plugins/repeat_with_blackboard.hpp"
#include "robot_ctrl/bt_plugins/interact_with_plc_action.hpp"
#include "robot_ctrl/bt_plugins/grip_control_action.hpp"
#include "robot_ctrl/bt_plugins/log_error_condition.hpp"
#include "robot_ctrl/bt_plugins/retry_decorator.hpp"
#include "robot_ctrl/bt_plugins/task_locked_action.hpp"

int main(int argc, char **argv)
{
    // 初始化 ROS2
    rclcpp::init(argc, argv);
    auto node = rclcpp::Node::make_shared("bt_runner");

    // 创建行为树工厂
    BT::BehaviorTreeFactory factory;

    // 注册所有自定义节点
    factory.registerNodeType<robot_ctrl::GetNavGoalFromRoom>("GetGoalFromRoom");
    factory.registerNodeType<robot_ctrl::ClearTaskCommandNode>("ClearTaskCommand");
    factory.registerNodeType<robot_ctrl::IsCommand>("IsTaskCommand");
    factory.registerNodeType<robot_ctrl::WaitForNewCommandCondition>("WaitForNewCommand");
    factory.registerNodeType<robot_ctrl::Nav2Pose>("NavToPose");
    factory.registerNodeType<robot_ctrl::GetArmGoalFromTfAction>("GetArmGoalFromTf");
    factory.registerNodeType<robot_ctrl::SendArmGoalAction>("SendArmGoal");
    factory.registerNodeType<robot_ctrl::RepeatWithBlackboard>("RepeatWithBlackboard");
    factory.registerNodeType<robot_ctrl::InteractWithPLCAction>("InteractWithPLC");
    factory.registerNodeType<robot_ctrl::GripControlAction>("GripControl");
    factory.registerNodeType<robot_ctrl::LogErrorCondition>("LogError");
    factory.registerNodeType<robot_ctrl::RetryUntilSuccessful>("Retry");
    factory.registerNodeType<robot_ctrl::SetTaskLockedAction>("SetTaskLocked");
    // 加载 XML 行为树文件
    const std::string xml_file = "/home/<USER>/test_ws/src/robot_ctrl/configs/main_tree.xml";
    auto blackboard = BT::Blackboard::create();
    blackboard->set("node", node);
    blackboard->set("task_locked", false);
    blackboard->set("target_pot_num", 0);
    blackboard->set("error_message", "IDEL");
    

    auto tree = factory.createTreeFromFile(xml_file, blackboard);

    // 控制台日志器（可选）
    BT::StdCoutLogger logger(tree);

    // ZeroMQ 发布器用于连接 Groot2
    // BT::PublisherZMQ zmq_publisher(tree);

    // 行为树主循环
    rclcpp::Rate loop_rate(10.0);  // 1Hz
    while (rclcpp::ok())
    {
        tree.tickRoot();
        rclcpp::spin_some(node);
        loop_rate.sleep();
    }

    rclcpp::shutdown();
    return 0;
}
