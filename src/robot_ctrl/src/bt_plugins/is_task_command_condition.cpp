#include "robot_ctrl/bt_plugins/is_task_command_condition.hpp"
#include "behaviortree_cpp_v3/bt_factory.h"

namespace robot_ctrl
{

IsCommand::IsCommand(
  const std::string & name,
  const BT::NodeConfiguration & config)
: BT::ConditionNode(name, config)
{
  node_ = config.blackboard->get<rclcpp::Node::SharedPtr>("node");
}

BT::PortsList IsCommand::providedPorts()
{
  return {
    BT::InputPort<std::string>("voice_task_command"),
    BT::InputPort<std::string>("task_type")
  };
}

BT::NodeStatus IsCommand::tick()
{
  std::string cmd, type;

  if (!getInput("voice_task_command", cmd)) {
    // RCLCPP_WARN_THROTTLE(node_->get_logger(), *node_->get_clock(), 2000,
    //   "[IsCommand] Missing input: voice_task_command");
    return BT::NodeStatus::FAILURE;
  }

  if (!getInput("task_type", type)) {
    RCLCPP_WARN_THROTTLE(node_->get_logger(), *node_->get_clock(), 2000,
      "[IsCommand] Missing input: task_type");
    return BT::NodeStatus::FAILURE;
  }

  RCLCPP_INFO(node_->get_logger(), "[IsCommand] Comparing '%s' == '%s'",
              cmd.c_str(), type.c_str());

  return (cmd == type) ? BT::NodeStatus::SUCCESS : BT::NodeStatus::FAILURE;
}

}  // namespace robot_ctrl


