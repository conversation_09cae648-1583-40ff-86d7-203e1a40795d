#include "robot_ctrl/bt_plugins/send_arm_goal_action.hpp"
#include "robot_ctrl/bt_plugins/error_log_queue.hpp"

namespace robot_ctrl {

SendArmGoalAction::SendArmGoalAction(const std::string& name, const BT::NodeConfiguration& config)
    : BT::StatefulActionNode(name, config),
      current_status_(BT::NodeStatus::IDLE)
{
    node_ = config.blackboard->get<rclcpp::Node::SharedPtr>("node");
    client_ = rclcpp_action::create_client<ArmCommand>(node_, "arm_command");
}

BT::PortsList SendArmGoalAction::providedPorts() {
    return { 
        BT::InputPort<geometry_msgs::msg::Pose>("arm_goal")
    };
}

BT::NodeStatus SendArmGoalAction::onStart() {
    // 1. 获取输入
    geometry_msgs::msg::Pose pose;
    if (!getInput("arm_goal", pose)) {
        last_error_ = "Missing arm_goal input";
        RCLCPP_ERROR(node_->get_logger(), "%s", last_error_.c_str());
        ErrorLogQueue::instance().pushError("SendArmGoalAction: Missing arm_goal input");
        return BT::NodeStatus::FAILURE;
    }

    // 2. 等待服务
    if (!client_->wait_for_action_server(std::chrono::seconds(2))) {
        last_error_ = "Action server not available";
        RCLCPP_ERROR(node_->get_logger(), "%s", last_error_.c_str());
        ErrorLogQueue::instance().pushError("SendArmGoalAction: Action server not available");
        return BT::NodeStatus::FAILURE;
    }

    // 3. 单位转换和验证
    ArmCommand::Goal goal;
    try {
        goal.target_x = pose.position.x ; 
        goal.target_y = pose.position.y ;
        goal.target_z = pose.position.z ;

        tf2::Quaternion q;
        tf2::fromMsg(pose.orientation, q);
        double roll, pitch, yaw;
        tf2::Matrix3x3(q).getRPY(roll, pitch, yaw);
        goal.target_a = yaw * 180.0 / M_PI; // rad -> deg,转为角度
    } catch (const std::exception& e) {
        last_error_ = std::string("Conversion error: ") + e.what();
        RCLCPP_ERROR(node_->get_logger(), "%s", last_error_.c_str());
        ErrorLogQueue::instance().pushError("SendArmGoalAction: tf transform error");
        return BT::NodeStatus::FAILURE;
    }

    // 4. 配置回调
    auto send_goal_options = rclcpp_action::Client<ArmCommand>::SendGoalOptions();
    send_goal_options.result_callback = 
        [this](const GoalHandle::WrappedResult& result) {
            this->processResult(result);
        };

    // 5. 发送目标
    auto future = client_->async_send_goal(goal, send_goal_options);
    if (rclcpp::spin_until_future_complete(node_, future, std::chrono::seconds(1)) 
        != rclcpp::FutureReturnCode::SUCCESS) 
    {
        last_error_ = "Failed to send goal";
        RCLCPP_ERROR(node_->get_logger(), "%s", last_error_.c_str());
        ErrorLogQueue::instance().pushError("SendArmGoalAction: Failed to send goal");
        return BT::NodeStatus::FAILURE;
    }

    goal_handle_ = future.get();
    start_time_ = node_->now();
    current_status_ = BT::NodeStatus::RUNNING;
    return BT::NodeStatus::RUNNING;
}

BT::NodeStatus SendArmGoalAction::onRunning() {
    // 1. 检查超时（30秒）
    if ((node_->now() - start_time_) > rclcpp::Duration(30, 0)) {
        last_error_ = "Action timed out";
        RCLCPP_ERROR(node_->get_logger(), "%s", last_error_.c_str());
        ErrorLogQueue::instance().pushError("SendArmGoalAction: Action timed out");
        return BT::NodeStatus::FAILURE;
    }

    // 2. 返回当前状态
    auto status = current_status_.load();
    if (status == BT::NodeStatus::FAILURE) {
    }
    return status;
}

void SendArmGoalAction::onHalted() {
    if (goal_handle_ && rclcpp::ok()) {
        RCLCPP_INFO(node_->get_logger(), "Canceling goal...");
        client_->async_cancel_goal(goal_handle_);
    }
    current_status_ = BT::NodeStatus::IDLE;
}

// 专用结果处理函数
void SendArmGoalAction::processResult(const GoalHandle::WrappedResult& result) {
    std::lock_guard<std::mutex> lock(result_mutex_);

    if (current_status_ != BT::NodeStatus::RUNNING) {
    RCLCPP_WARN(node_->get_logger(), "Received result after node was halted");
    return;
    }   

    switch (result.code) {
        case rclcpp_action::ResultCode::SUCCEEDED:
            RCLCPP_INFO(node_->get_logger(), 
                       "Goal succeeded: %s", 
                       result.result->message.c_str());
            current_status_ = BT::NodeStatus::SUCCESS;
            break;
            
        case rclcpp_action::ResultCode::ABORTED:
            last_error_ = "Goal aborted: " + result.result->message;
            RCLCPP_ERROR(node_->get_logger(), "%s", last_error_.c_str());
            ErrorLogQueue::instance().pushError("SendArmGoalAction: Goal aborted");
            current_status_ = BT::NodeStatus::FAILURE;
            break;
            
        case rclcpp_action::ResultCode::CANCELED:
            last_error_ = "Goal canceled by user";
            RCLCPP_WARN(node_->get_logger(), "%s", last_error_.c_str());
            ErrorLogQueue::instance().pushError("SendArmGoalAction: Goal canceled by user");
            current_status_ = BT::NodeStatus::FAILURE;
            break;
            
        default:
            last_error_ = "Unknown result code";
            RCLCPP_ERROR(node_->get_logger(), "%s", last_error_.c_str());
            ErrorLogQueue::instance().pushError("SendArmGoalAction: Unknown result code");
            current_status_ = BT::NodeStatus::FAILURE;
    }
}

} // namespace robot_ctrl