#include "robot_ctrl/bt_plugins/log_error_condition.hpp"
#include "robot_ctrl/bt_plugins/error_log_queue.hpp"
#include <sstream>
#include <iomanip>
#include <chrono>

namespace robot_ctrl
{
LogErrorCondition::LogErrorCondition(const std::string& name, const BT::NodeConfiguration& config)
    : BT::ConditionNode(name, config)
{
    log_file_.open("src/robot_ctrl/logs/error_log.txt", std::ios::app);
}

LogErrorCondition::~LogErrorCondition()
{
    if (log_file_.is_open())
        log_file_.close();
}

BT::NodeStatus LogErrorCondition::tick()
{
    std::string error_message;

    // 遍历队列，把所有错误写入日志
    while (ErrorLogQueue::instance().popError(error_message))
    {
        logError(error_message);
    }

    // 始终返回 FAILURE
    return BT::NodeStatus::FAILURE;
}

void LogErrorCondition::logError(const std::string& error_message)
{
    auto now = std::chrono::system_clock::now();
    auto now_c = std::chrono::system_clock::to_time_t(now);
    std::ostringstream timestamp_stream;
    timestamp_stream << std::put_time(std::localtime(&now_c), "%Y-%m-%d %H:%M:%S");
    log_file_ << timestamp_stream.str() << " - Error: " << error_message << std::endl;
}
}
