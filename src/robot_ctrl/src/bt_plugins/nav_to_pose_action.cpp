#include "robot_ctrl/bt_plugins/nav_to_pose_action.hpp"
#include "robot_ctrl/bt_plugins/error_log_queue.hpp"

using namespace BT;
namespace robot_ctrl
{
  Nav2Pose::Nav2Pose(const std::string &name, const NodeConfiguration &config)
  : StatefulActionNode(name, config)
  {
    node_ = config.blackboard->get<rclcpp::Node::SharedPtr>("node");
    action_client_ = rclcpp_action::create_client<NavigateToPose>(node_, "navigate_to_pose");
    node_->get_parameter_or("send_goal_timeout_ms", send_goal_timeout_, 1000);
  }

  NodeStatus Nav2Pose::onStart()
  {
    auto goal = getInput<geometry_msgs::msg::PoseStamped>("goal");
    if (!goal)
    {
      RCLCPP_ERROR(node_->get_logger(), "Goal is not set.");
      ErrorLogQueue::instance().pushError("Nav2Pose: Goal is not set");
      return NodeStatus::FAILURE;
    }

    navigation_goal_.pose = goal.value();
    navigation_goal_.pose.header.frame_id = "map";
    navigation_goal_.pose.header.stamp = node_->now();

    auto future_goal_handle = action_client_->async_send_goal(navigation_goal_);
    RCLCPP_DEBUG(node_->get_logger(), "Sending goal with timeout %d ms", send_goal_timeout_);

    if (rclcpp::spin_until_future_complete(node_, future_goal_handle, std::chrono::milliseconds(send_goal_timeout_)) !=
        rclcpp::FutureReturnCode::SUCCESS)
    {
      RCLCPP_ERROR(node_->get_logger(), "Send goal failed.");
      ErrorLogQueue::instance().pushError("Nav2Pose: Send goal failed");
      return NodeStatus::FAILURE;
    }

    goal_handle_ = future_goal_handle.get();
    if (!goal_handle_)
    {
      RCLCPP_ERROR(node_->get_logger(), "Goal handle is null.");
      ErrorLogQueue::instance().pushError("Nav2Pose: Goal handle is null");
      return NodeStatus::FAILURE;
    }

    result_future_ = action_client_->async_get_result(goal_handle_);

    RCLCPP_INFO(node_->get_logger(), "Navigating to pose [%.2f, %.2f, %.2f]",
                goal->pose.position.x, goal->pose.position.y, goal->pose.position.z);

    return NodeStatus::RUNNING;
  }

  NodeStatus Nav2Pose::onRunning()
  {
    if (!goal_handle_)
    {
      RCLCPP_ERROR(node_->get_logger(), "Goal handle not initialized.");
      ErrorLogQueue::instance().pushError("Nav2Pose: Goal handle not initialized");
      return NodeStatus::FAILURE;
    }

    if (!result_future_.valid())
    {
      result_future_ = action_client_->async_get_result(goal_handle_);
    }

    auto status = result_future_.wait_for(std::chrono::milliseconds(10));

    if (status == std::future_status::ready)
    {
      auto wrapped_result = result_future_.get();

      switch (wrapped_result.code)
      {
        case rclcpp_action::ResultCode::SUCCEEDED:
          RCLCPP_INFO(node_->get_logger(), "Navigation succeeded.");
          return NodeStatus::SUCCESS;

        case rclcpp_action::ResultCode::ABORTED:
          RCLCPP_ERROR(node_->get_logger(), "Navigation aborted.");
          ErrorLogQueue::instance().pushError("Nav2Pose: Navigation aborted");
          return NodeStatus::FAILURE;

        case rclcpp_action::ResultCode::CANCELED:
          RCLCPP_WARN(node_->get_logger(), "Navigation canceled.");
          ErrorLogQueue::instance().pushError("Nav2Pose: Navigation canceled");
          return NodeStatus::FAILURE;

        default:
          RCLCPP_ERROR(node_->get_logger(), "Unknown result code.");
          ErrorLogQueue::instance().pushError("Nav2Pose: Unknown result code");
          return NodeStatus::FAILURE;
      }
    }

    return NodeStatus::RUNNING;
  }

  void Nav2Pose::onHalted()
  {
    RCLCPP_INFO(node_->get_logger(), "Goal halted.");
    if (goal_handle_)
    {
      auto cancel_future = action_client_->async_cancel_goal(goal_handle_);
      if (rclcpp::spin_until_future_complete(node_, cancel_future) != rclcpp::FutureReturnCode::SUCCESS)
      {
        RCLCPP_ERROR(node_->get_logger(), "Cancel goal failed.");
      }
      RCLCPP_INFO(node_->get_logger(), "Goal canceled.");
    }
  }

  PortsList Nav2Pose::providedPorts()
  {
    const char *description = "Goal pose to send to Nav2";
    return {
      BT::InputPort<geometry_msgs::msg::PoseStamped>("goal", description)
    };
    
  }

} // namespace robot_ctrl
