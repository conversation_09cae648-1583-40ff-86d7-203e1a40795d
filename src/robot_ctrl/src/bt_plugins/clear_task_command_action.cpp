#include "robot_ctrl/bt_plugins/clear_task_command_action.hpp"

#include <rclcpp/rclcpp.hpp>

namespace robot_ctrl
{

ClearTaskCommandNode::ClearTaskCommandNode(const std::string& name, const BT::NodeConfiguration& config)
: BT::SyncActionNode(name, config) {}

BT::PortsList ClearTaskCommandNode::providedPorts()
{
    return {
        BT::OutputPort<std::string>("voice_task_command"),
        BT::OutputPort<std::string>("nav_target_room"),
        BT::OutputPort<int>("target_pot_num")
    };
}

BT::NodeStatus ClearTaskCommandNode::tick()
{
    RCLCPP_INFO(rclcpp::get_logger("ClearTaskCommandNode"), "Clearing voice_task_command and nav_target_room");

    setOutput("voice_task_command", "IDLE");
    setOutput("nav_target_room", "");
    setOutput("target_pot_num", 0);
    return BT::NodeStatus::SUCCESS;
}

}  // namespace robot_ctrl

