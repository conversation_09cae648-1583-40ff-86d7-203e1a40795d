#include "robot_ctrl/bt_plugins/repeat_with_blackboard.hpp"

namespace robot_ctrl
{
RepeatWithBlackboard::RepeatWithBlackboard(const std::string& name, const BT::NodeConfiguration& config)
  : BT::DecoratorNode(name, config), repeat_count_(0), current_count_(0)
{
  node_ = config.blackboard->get<rclcpp::Node::SharedPtr>("node");
}

BT::PortsList RepeatWithBlackboard::providedPorts()
{
  return {
    BT::InputPort<int>("target_pot_num")
  };
}

BT::NodeStatus RepeatWithBlackboard::tick()
{
  if (status() == BT::NodeStatus::IDLE)
  {
    int target_pot_num = 0;
    if (!getInput("target_pot_num", target_pot_num))
    {
      throw BT::RuntimeError("Missing required input [target_pot_num]");
    }
    if (target_pot_num <= 0) {
      throw BT::RuntimeError("Invalid input: target_pot_num must be positive");
    }

    // 计算重复次数，最少为 1
    repeat_count_ = std::max(1, (target_pot_num + 2) / 3);  // 向上取整
    current_count_ = 0;
    RCLCPP_INFO(node_->get_logger(), "All repeats start (%d/%d)", 
               current_count_, repeat_count_);
  }
  // 检查子节点是否存在
  if (!child_node_)
  {
    throw BT::RuntimeError("Child node is missing");
  }

  if (current_count_ >= repeat_count_)
  {
    return BT::NodeStatus::SUCCESS;
  }

  const BT::NodeStatus child_status = child_node_->executeTick();

  if (child_status == BT::NodeStatus::SUCCESS)
  {
    current_count_++;

    if (current_count_ < repeat_count_)
    {
      child_node_->halt();
      RCLCPP_INFO(node_->get_logger(), "repeats completed (%d/%d)", 
               current_count_, repeat_count_);
      return BT::NodeStatus::RUNNING;
    }
    else
    {
      RCLCPP_INFO(node_->get_logger(), "All repeats completed ");
      return BT::NodeStatus::SUCCESS;
    }
  }
  else if (child_status == BT::NodeStatus::FAILURE)
  {
    // 子节点失败，直接返回失败，不再继续重复
    return BT::NodeStatus::FAILURE;
  }


  // 子节点仍在运行或异常状态
  return child_status;
}

void RepeatWithBlackboard::halt()
{
  DecoratorNode::halt();
  current_count_ = 0;
}
}