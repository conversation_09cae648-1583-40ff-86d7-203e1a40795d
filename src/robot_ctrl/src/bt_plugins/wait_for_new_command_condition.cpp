#include "robot_ctrl/bt_plugins/wait_for_new_command_condition.hpp"
#include "robot_ctrl/bt_plugins/error_log_queue.hpp"
namespace robot_ctrl
{

WaitForNewCommandCondition::WaitForNewCommandCondition(
  const std::string & name,
  const BT::NodeConfiguration & config)
: BT::ConditionNode(name, config)
{
  node_ = config.blackboard->get<rclcpp::Node::SharedPtr>("node");

  voice_sub_ = node_->create_subscription<VoiceCommandMsg>(
    "/voice_command", 10,
    std::bind(&WaitForNewCommandCondition::voiceCommandCallback, this, std::placeholders::_1));

  app_sub_ = node_->create_subscription<VoiceCommandMsg>(
    "/app_command", 10,
    std::bind(&WaitForNewCommandCondition::appCommandCallback, this, std::placeholders::_1));

  tans_sub_ = node_->create_subscription<std_msgs::msg::Int32>(
    "/translate_command", 10,
    std::bind(&WaitForNewCommandCondition::tansCommandCallback, this, std::placeholders::_1));
}

BT::NodeStatus WaitForNewCommandCondition::tick()
{
  bool task_locked = false;
  getInput("task_locked", task_locked);

  rclcpp::spin_some(node_);

  std::lock_guard<std::mutex> lock(mutex_);

  if (new_command_available_)
  {
    // if (last_room_name_ != current_room_name_ || last_task_command_ != current_task_command_)
    // {
      if (task_locked)//判断正在执行的任务是否被锁住
      {
        RCLCPP_WARN_THROTTLE(node_->get_logger(), *node_->get_clock(), 5000,
          "Task is locked, ignoring new command.");
        ErrorLogQueue::instance().pushError("WaitForNewCommandCondition: Task is locked, ignoring new command");
        last_room_name_ = current_room_name_;
        last_task_command_ = current_task_command_;
        last_pot_num_ = current_pot_num_;
        new_command_available_ = false;
        return BT::NodeStatus::FAILURE;
      }

      setOutput("nav_target_room", current_room_name_);
      setOutput("voice_task_command", current_task_command_);
      setOutput("target_pot_num", current_pot_num_);
      RCLCPP_INFO(node_->get_logger(), "New command received: room = '%s', task = '%s'",
        current_room_name_.c_str(), current_task_command_.c_str());

      last_room_name_ = current_room_name_;
      last_task_command_ = current_task_command_;
      last_pot_num_ = current_pot_num_;
      new_command_available_ = false;
      return BT::NodeStatus::SUCCESS;
    // }
    // else
    // {
    //   last_room_name_ = current_room_name_;
    //   last_task_command_ = current_task_command_;
    //   last_pot_num_ = current_pot_num_;
    //   new_command_available_ = false;
    //   RCLCPP_WARN_THROTTLE(node_->get_logger(), *node_->get_clock(), 5000,
    //     "Received same command as before.");
    // }
  }

  if(translate_available_)
  {
      if (task_locked)//判断正在执行的任务是否被锁住
      {
          RCLCPP_WARN_THROTTLE(node_->get_logger(), *node_->get_clock(), 5000,
          "Task is locked, ignoring new command.");
          ErrorLogQueue::instance().pushError("WaitForNewCommandCondition: Task is still running, please wait for it to finish");
          last_room_name_ = current_room_name_;
          last_task_command_ = current_task_command_;
          last_pot_num_ = current_pot_num_;
          translate_available_ = false;
          return BT::NodeStatus::FAILURE;
      }
      else
      {
        setOutput("target_pot_num", current_pot_num_);
        setOutput("voice_task_command", "MovePot");    
        RCLCPP_INFO(node_->get_logger(), "New command received: pot_num = '%d'", current_pot_num_);
        last_room_name_ = current_room_name_;
        last_task_command_ = current_task_command_;
        last_pot_num_ = current_pot_num_;
        translate_available_ = false;
        return BT::NodeStatus::SUCCESS;
      }



  }

  return BT::NodeStatus::FAILURE;
}

void WaitForNewCommandCondition::voiceCommandCallback(const VoiceCommandMsg::SharedPtr msg)
{
  std::lock_guard<std::mutex> lock(mutex_);
  current_room_name_ = msg->target_room;
  current_task_command_ = msg->task_command;
  current_pot_num_ = msg->pot_num;
  new_command_available_ = true;
}

void WaitForNewCommandCondition::appCommandCallback(const VoiceCommandMsg::SharedPtr msg)
{
  std::lock_guard<std::mutex> lock(mutex_);
  current_room_name_ = msg->target_room;
  current_task_command_ = msg->task_command;
  current_pot_num_ = msg->pot_num;
  new_command_available_ = true;
}

void WaitForNewCommandCondition::tansCommandCallback(const std_msgs::msg::Int32::SharedPtr msg)
{
  std::lock_guard<std::mutex> lock(mutex_);
  current_pot_num_ = msg->data;
  translate_available_ = true;
}

BT::PortsList WaitForNewCommandCondition::providedPorts()
{
  return {
    BT::OutputPort<std::string>("nav_target_room"),
    BT::OutputPort<std::string>("voice_task_command"),
    BT::OutputPort<int>("target_pot_num"),
    BT::InputPort<bool>("task_locked")
  };
}

}  // namespace robot_ctrl

// #include "behaviortree_cpp_v3/bt_factory.h"

// BT_REGISTER_NODES(factory)
// {
//   factory.registerNodeType<robot_ctrl::WaitForNewCommandCondition>("WaitForNewCommand");
// }

