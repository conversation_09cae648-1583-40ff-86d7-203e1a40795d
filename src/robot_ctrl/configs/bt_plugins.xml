<library path="lib/librobot_ctrl_bt_plugins">
  <class name="WaitForNewCommand" 
         type="robot_ctrl::WaitForNewCommandCondition" 
         base_class_type="BT::ConditionNode"/>
  <class name="IsCommand"
         type="robot_ctrl::IsCommand"
         base_class_type="BT::ConditionNode"/>
  <class name="GetNavGoalFromRoom"
         type="robot_ctrl::GetNavGoalFromRoom"
         base_class_type="BT::SyncActionNode"/>
  <class name="ClearTaskCommand"
         type="robot_ctrl::ClearTaskCommand"
         base_class_type="BT::SyncActionNode"/>
  <class name="GetArmGoalFromTf"
         type="robot_ctrl::GetArmGoalFromTfAction"
         base_class_type="BT::SyncActionNode"/>
  <class name="SendArmGoalAction"
         type="robot_ctrl::SendArmGoalAction"
         base_class_type="BT::StatefulActionNode"/>
  <class name="RepeatWithBlackboard"
         type="robot_ctrl::RepeatWithBlackboard"
         base_class_type="BT::DecoratorNode"/>
</library>
