#pragma once

#include <string>
#include <mutex>
#include "behaviortree_cpp_v3/condition_node.h"
#include "rclcpp/rclcpp.hpp"
#include "robot_ctrl/msg/voice_command.hpp"
#include "std_msgs/msg/int32.hpp"

namespace robot_ctrl
{

class WaitForNewCommandCondition : public BT::ConditionNode
{
public:
  using VoiceCommandMsg = robot_ctrl::msg::VoiceCommand;

  WaitForNewCommandCondition(
    const std::string & name,
    const BT::NodeConfiguration & config);

  WaitForNewCommandCondition() = delete;

  BT::NodeStatus tick() override;

  static BT::PortsList providedPorts();

private:
  void voiceCommandCallback(const VoiceCommandMsg::SharedPtr msg);
  void appCommandCallback(const VoiceCommandMsg::SharedPtr msg);
  void tansCommandCallback(const std_msgs::msg::Int32::SharedPtr msg);

  rclcpp::Node::SharedPtr node_;
  rclcpp::Subscription<VoiceCommandMsg>::SharedPtr voice_sub_;
  rclcpp::Subscription<VoiceCommandMsg>::SharedPtr app_sub_;
  rclcpp::Subscription<std_msgs::msg::Int32>::SharedPtr tans_sub_;

  std::mutex mutex_;
  bool new_command_available_ = false;
  bool translate_available_ = false;
  bool stop_translate_task_ = false;

  std::string current_room_name_;
  std::string current_task_command_;
  std::string last_room_name_;
  std::string last_task_command_;
  int last_pot_num_;
  int current_pot_num_;
};

}  // namespace robot_ctrl
