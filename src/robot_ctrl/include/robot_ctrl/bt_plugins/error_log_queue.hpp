#pragma once
#include <queue>
#include <mutex>
#include <string>

class ErrorLogQueue
{
public:
    static ErrorLogQueue& instance()
    {
        static ErrorLogQueue queue;
        return queue;
    }

    void pushError(const std::string& msg)
    {
        std::lock_guard<std::mutex> lock(mutex_);
        errors_.push(msg);
    }

    bool popError(std::string& msg)
    {
        std::lock_guard<std::mutex> lock(mutex_);
        if(errors_.empty()) return false;
        msg = errors_.front();
        errors_.pop();
        return true;
    }

    bool empty()
    {
        std::lock_guard<std::mutex> lock(mutex_);
        return errors_.empty();
    }

private:
    ErrorLogQueue() {}
    std::queue<std::string> errors_;
    std::mutex mutex_;
};
