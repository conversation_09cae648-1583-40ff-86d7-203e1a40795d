#ifndef LOG_ERROR_CONDITION_HPP
#define LOG_ERROR_CONDITION_HPP

#include <behaviortree_cpp_v3/behavior_tree.h>
#include <fstream>
#include <iomanip>
#include <chrono>
#include <ctime>
#include "robot_ctrl/bt_plugins/error_log_queue.hpp"  // 引入错误队列头文件

namespace robot_ctrl
{
class LogErrorCondition : public BT::ConditionNode
{
public:
    LogErrorCondition(const std::string& name, const BT::NodeConfiguration& config);
    ~LogErrorCondition();

    BT::NodeStatus tick() override;

    // 不再通过黑板端口传递错误，所以可以不提供 error_message 端口
    static BT::PortsList providedPorts()
    {
        return {};
    }

private:
    std::ofstream log_file_;
    void logError(const std::string& error_message);
};

} // namespace robot_ctrl

#endif // LOG_ERROR_CONDITION_HPP
