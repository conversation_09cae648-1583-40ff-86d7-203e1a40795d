#pragma once

#include <rclcpp/rclcpp.hpp>
#include <serial/serial.h>
#include <robot_ctrl/msg/voice_command.hpp>

class VoiceCtrlNode : public rclcpp::Node
{
public:
    VoiceCtrlNode();

private:
    void processSerialData();
    void sendSerialCommand(uint8_t mode);
    std::string parseRoomNumber(uint8_t result);

    serial::Serial serial_port_;
    rclcpp::Publisher<robot_ctrl::msg::VoiceCommand>::SharedPtr voice_pub_;
    rclcpp::TimerBase::SharedPtr timer_;

    uint8_t current_state_ = 0;
    uint8_t last_state_ = 0;
    bool is_navigating_ = false;
    std::string last_room_;
    std::string last_task_command_;
};
