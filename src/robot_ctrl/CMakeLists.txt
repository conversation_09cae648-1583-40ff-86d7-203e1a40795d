cmake_minimum_required(VERSION 3.8)
project(robot_ctrl)

if(CMAKE_COMPILER_IS_GNUCXX OR CMAKE_CXX_COMPILER_ID MATCHES "Clang")
  add_compile_options(-Wall -Wextra -Wpedantic)
endif()

# find dependencies
find_package(ament_cmake REQUIRED)
find_package(rclcpp REQUIRED)
find_package(rclcpp_action REQUIRED)
find_package(rclcpp_lifecycle REQUIRED)
find_package(std_msgs REQUIRED)
find_package(behaviortree_cpp_v3 REQUIRED)
find_package(rosidl_default_generators REQUIRED)
find_package(pluginlib REQUIRED)
find_package(tf2 REQUIRED)
find_package(tf2_ros REQUIRED)
find_package(tf2_geometry_msgs REQUIRED)
find_package(geometry_msgs REQUIRED)
find_package(nav_msgs REQUIRED)
find_package(nav2_msgs REQUIRED)
find_package(robotcar_base REQUIRED) 
find_package(robot_arm REQUIRED)
find_package(apriltag_msgs REQUIRED)


# find_path(PAHO_MQTT_CPP_INCLUDE_DIRS
#   NAMES mqtt/async_client.h
#   PATHS /usr/local/include
# )

# find_library(PAHO_MQTT_CPP_LIBRARIES
#   NAMES paho-mqttpp3
#   PATHS /usr/local/lib
# )

# find_library(PAHO_MQTT_C_LIBRARIES
#   NAMES paho-mqtt3a
#   PATHS /usr/local/lib
# )

include_directories(include
  ${behaviortree_cpp_v3_INCLUDE_DIRS}
  ${rclcpp_INCLUDE_DIRS} 
  ${std_msgs_INCLUDE_DIRS}
  # ${PAHO_MQTT_CPP_INCLUDE_DIRS}
  ${robot_arm_INCLUDE_DIRS}
)
include_directories(${CMAKE_CURRENT_SOURCE_DIR}/include
  ${CMAKE_CURRENT_BINARY_DIR}/rosidl_generator_cpp
)
rosidl_generate_interfaces(${PROJECT_NAME}
  "msg/VoiceCommand.msg"
  "srv/GetRoompose.srv"
  "srv/RecorderPose.srv"
)

link_directories(/usr/local/lib)

install(DIRECTORY include/
  DESTINATION include
)
#=============== 自定义服务节点 ==================================
# add_executable(voice_ctrl_node src/robot_service/voice_ctrl_node.cpp)
# ament_target_dependencies(voice_ctrl_node rclcpp std_msgs)

add_executable(load_roompose src/robot_service/load_roompose.cpp)
ament_target_dependencies(load_roompose rclcpp std_msgs)

add_executable(pose_recorder src/robot_service/pose_recorder.cpp)
ament_target_dependencies(pose_recorder 
  rclcpp 
  std_msgs 
  tf2
  tf2_ros
  tf2_geometry_msgs
  geometry_msgs
  )

# add_executable(Tcp_Qt src/robot_service/Tcp_Qt.cpp)
# ament_target_dependencies(Tcp_Qt 
#   rclcpp 
#   std_msgs 
#   nav_msgs
#   tf2
#   tf2_ros
#   tf2_geometry_msgs
#   geometry_msgs
#   robotcar_base
# )

rosidl_get_typesupport_target(cpp_typesupport_target ${PROJECT_NAME} "rosidl_typesupport_cpp")
# target_link_libraries(voice_ctrl_node ${cpp_typesupport_target} serial)
target_link_libraries(load_roompose ${cpp_typesupport_target})
target_link_libraries(pose_recorder ${cpp_typesupport_target})
# target_link_libraries(Tcp_Qt 
#   ${cpp_typesupport_target}
#   ${PAHO_MQTT_CPP_LIBRARIES}
#   ${PAHO_MQTT_C_LIBRARIES}
# )

# install(TARGETS voice_ctrl_node
 # DESTINATION lib/${PROJECT_NAME})
install(TARGETS load_roompose
  DESTINATION lib/${PROJECT_NAME})
install(TARGETS pose_recorder
  DESTINATION lib/${PROJECT_NAME})
# install(TARGETS Tcp_Qt
#   DESTINATION lib/${PROJECT_NAME})

# ================ 行为树插件 =============================
add_library(robot_ctrl_bt_plugins SHARED
  src/bt_plugins/wait_for_new_command_condition.cpp
  src/bt_plugins/is_task_command_condition.cpp
  src/bt_plugins/get_goal_from_room_action.cpp
  src/bt_plugins/clear_task_command_action.cpp
  src/bt_plugins/nav_to_pose_action.cpp
  src/bt_plugins/get_arm_goal_from_tf_action.cpp
  src/bt_plugins/send_arm_goal_action.cpp
  src/bt_plugins/repeat_with_blackboard.cpp
  src/bt_plugins/interact_with_plc_action.cpp
  src/bt_plugins/grip_control_action.cpp
  src/bt_plugins/log_error_condition.cpp
  src/bt_plugins/retry_decorator.cpp
)

target_include_directories(robot_ctrl_bt_plugins PUBLIC
  $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/include>
)
ament_target_dependencies(robot_ctrl_bt_plugins
  rclcpp
  behaviortree_cpp_v3
  geometry_msgs
  tf2
  tf2_geometry_msgs
  tf2_ros
  nav2_msgs
  rclcpp_action
  robot_arm
  apriltag_msgs
  robotcar_base
)

target_link_libraries(robot_ctrl_bt_plugins 
  ${cpp_typesupport_target}
  ${rclcpp_action_LIBRARIES}
  )

add_executable(bt_main src/bt_main.cpp)
ament_target_dependencies(bt_main rclcpp behaviortree_cpp_v3)
target_link_libraries(bt_main
  robot_ctrl_bt_plugins  
)

install(TARGETS bt_main DESTINATION lib/${PROJECT_NAME})


pluginlib_export_plugin_description_file(behaviortree_cpp_v3 configs/bt_plugins.xml)
install(TARGETS robot_ctrl_bt_plugins
  LIBRARY DESTINATION lib
)

ament_export_dependencies(rosidl_default_runtime)

install(
  DIRECTORY launch
  DESTINATION share/${PROJECT_NAME}
)

install(
  DIRECTORY launch
  DESTINATION share/${PROJECT_NAME}
)

install(
  DIRECTORY configs
  DESTINATION share/${PROJECT_NAME}
)

ament_package()
