from launch import LaunchDescription
from launch_ros.actions import Node
from launch.actions import DeclareLaunchArgument, IncludeLaunchDescription
from launch.launch_description_sources import PythonLaunchDescriptionSource
from launch.substitutions import LaunchConfiguration, ThisLaunchFileDir
import os
from ament_index_python.packages import get_package_share_directory

def generate_launch_description():

    robot_arm_launch_dir = os.path.join(
        get_package_share_directory('robot_arm'), 'launch')
    apriltag_launch_dir = os.path.join(
        get_package_share_directory('apriltag_ros'), 'launch')
    plc_tcp_launch_dir = os.path.join(
        get_package_share_directory('robot_arm'), 'launch')
        

    return LaunchDescription([
        DeclareLaunchArgument(
            'csv_file_path',
            default_value='/home/<USER>/test_ws/src/robot_ctrl/configs/roompose.csv',  # 房间坐标CSV文件
            description='Full path to room pose CSV file'
        ),

        Node(  #房间坐标标记节点
            package='robot_ctrl',
            executable='pose_recorder',
            name='pose_recorder',
            output='screen',
            parameters=[{
                'csv_file_path': LaunchConfiguration('csv_file_path')
            }]
        ),

        Node(  #房间坐标加载节点
            package='robot_ctrl',
            executable='load_roompose',
            name='load_roompose',
            output='screen',
            parameters=[{
                'csv_file_path': LaunchConfiguration('csv_file_path')
            }]
        ),

        # Node( #语音控制节点
        #     package='robot_ctrl',
        #     executable='voice_ctrl_node',
        #     name='voice_ctrl_node',
        #     output='screen',
        # ),

        # Node(  #TCP节点
        #     package='robot_ctrl',
        #     executable='Tcp_Qt',
        #     name='Tcp_Qt',
        #     output='screen',
        #     parameters=[{
        #         'csv_file_path': LaunchConfiguration('csv_file_path')
        #     }]
        # ),

        Node(
            package='tf2_ros',
            executable='static_transform_publisher',
            name='static_tf_pub_arm_base',
            arguments=[
                '--x', '0.076',
                '--y', '0.0901',
                '--z', '0.44525',
                '--qx', '0',
                '--qy', '0',
                '--qz', '0',
                '--qw', '1',
                '--frame-id', 'base_link',
                '--child-frame-id', 'arm_base_link'
            ],
            output='screen'
        ),
        # 机械臂tcp_action通信启动文件
        IncludeLaunchDescription(
            PythonLaunchDescriptionSource(
                os.path.join(robot_arm_launch_dir, 'arm_action_server.launch.py')
            )
        ), 
        # apriltag识别启动文件
        IncludeLaunchDescription(
            PythonLaunchDescriptionSource(
                os.path.join(apriltag_launch_dir, 'apriltag_camera_composable.launch.py')
            )
        ),
        # plc通信启动文件
        IncludeLaunchDescription(
            PythonLaunchDescriptionSource(
                os.path.join(plc_tcp_launch_dir, 'plc_tcp_node.launch.py')
            )
        )

    ])
