# nav_07 传感器同步使用指南

## 🎯 功能概述

nav_07_sensor_sync.launch.py 提供传感器时间同步功能，使用 message_filters 库同步 IMU、激光雷达和里程计数据。

## 📊 适用场景

### **需要传感器同步的情况**
- ✅ 高精度定位应用
- ✅ 科研级数据采集
- ✅ 传感器标定
- ✅ 自定义融合算法开发

### **不需要传感器同步的情况**
- ❌ 日常导航和建图 (nav_05/nav_06已足够)
- ❌ 一般精度要求的应用
- ❌ 资源受限的环境

## 🚀 使用方法

### **基本启动**
```bash
# 启动传感器同步节点
ros2 launch robotcar_nav nav_07_sensor_sync.launch.py

# 自定义参数启动
ros2 launch robotcar_nav nav_07_sensor_sync.launch.py \
    sync_mode:=exact \
    sync_slop:=0.05 \
    use_filtered_odom:=false
```

### **参数配置**

| 参数 | 默认值 | 说明 |
|------|--------|------|
| `sync_mode` | `approximate` | 同步模式: `exact`(精确) 或 `approximate`(近似) |
| `sync_slop` | `0.1` | 近似同步时间容差(秒) |
| `use_filtered_scan` | `false` | 是否使用过滤后的激光数据 |
| `use_filtered_odom` | `true` | 是否使用EKF融合后的里程计 |
| `imu_topic` | `/imu` | IMU数据话题 |
| `scan_topic` | `/scan` | 激光雷达数据话题 |
| `odom_topic` | `/odometry/filtered` | 里程计数据话题 |

## 📡 输入/输出话题

### **输入话题**
- `/imu` - IMU数据 (sensor_msgs/Imu)
- `/scan` - 激光雷达数据 (sensor_msgs/LaserScan)
- `/odometry/filtered` - 里程计数据 (nav_msgs/Odometry)

### **输出话题**
- `/synced/imu` - 同步后的IMU数据
- `/synced/scan` - 同步后的激光雷达数据
- `/synced/odom` - 同步后的里程计数据

## 🔧 与现有系统集成

### **修改现有launch文件使用同步数据**

例如，在 nav_03_mapping.launch.py 中使用同步数据：

```python
# 1. 先启动传感器同步
IncludeLaunchDescription(
    PythonLaunchDescriptionSource(
        os.path.join(pkg_robotcar_nav, 'launch', 'nav_07_sensor_sync.launch.py')),
    launch_arguments={
        'use_sim_time': use_sim_time,
        'sync_mode': 'approximate',
        'sync_slop': '0.1'
    }.items()
),

# 2. 修改Cartographer使用同步数据
Node(
    package='cartographer_ros',
    executable='cartographer_node',
    name='cartographer_node',
    remappings=[
        ('scan', '/synced/scan'),    # 使用同步激光数据
        ('imu', '/synced/imu'),      # 使用同步IMU数据
        ('odom', '/synced/odom'),    # 使用同步里程计数据
    ]
)
```

## ⚡ 性能考虑

### **资源占用**
- CPU: 额外 5-10% 占用
- 内存: 额外 50-100MB
- 网络: 传感器数据量翻倍

### **延迟影响**
- 同步延迟: 0.1-0.2秒 (取决于sync_slop设置)
- 适合离线处理或对实时性要求不高的应用

## 🛠️ 故障排除

### **常见问题**

1. **节点启动失败**
   ```bash
   # 检查依赖
   sudo apt install ros-humble-message-filters
   
   # 重新编译
   colcon build --packages-select robotcar_nav
   ```

2. **没有同步数据输出**
   ```bash
   # 检查输入话题是否有数据
   ros2 topic echo /imu
   ros2 topic echo /scan
   ros2 topic echo /odometry/filtered
   
   # 检查时间戳差异
   ros2 topic echo /imu --field header.stamp
   ```

3. **同步频率太低**
   - 减小 `sync_slop` 参数
   - 检查传感器发布频率是否匹配
   - 考虑使用 `exact` 同步模式

## 📈 监控和调试

### **查看同步状态**
```bash
# 监控同步输出
ros2 topic hz /synced/imu
ros2 topic hz /synced/scan
ros2 topic hz /synced/odom

# 查看节点日志
ros2 node info /sensor_sync_node
```

### **性能分析**
```bash
# 查看话题延迟
ros2 topic delay /synced/imu
ros2 topic delay /synced/scan
ros2 topic delay /synced/odom
```

## 💡 最佳实践

1. **选择合适的同步模式**
   - 一般应用: `approximate` 模式
   - 高精度应用: `exact` 模式

2. **调整时间容差**
   - 传感器频率相近: 0.05-0.1秒
   - 传感器频率差异大: 0.1-0.2秒

3. **监控系统性能**
   - 定期检查CPU和内存使用
   - 监控话题发布频率

4. **测试验证**
   - 对比同步前后的数据质量
   - 验证下游算法的改进效果

## 🔄 与其他nav系统的关系

- **nav_01-nav_06**: 不使用传感器同步，适合日常使用
- **nav_07**: 可选的高级功能，按需启用
- **未来扩展**: 可作为其他高精度功能的基础

---

**总结**: nav_07 是一个可选的高级功能，为需要严格时间同步的应用提供支持。对于一般的导航和建图任务，现有的 nav_05/nav_06 系统已经足够使用。
