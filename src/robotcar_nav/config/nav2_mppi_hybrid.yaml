# Nav2 MPPI控制器 + Smac Hybrid A*规划器配置文件
# 适配750mm×600mm差分驱动机器人，基于现有速度和加速度限制
# {{ AURA-X: Create - MPPI + Hybrid A*配置文件适配大型差分驱动机器人. Approval: 寸止(ID:mppi_hybrid_config). }}

# ========== 行为树导航器配置 ==========
bt_navigator:
  ros__parameters:
    use_sim_time: false
    global_frame: map
    robot_base_frame: base_footprint
    odom_topic: /odom
    bt_loop_duration: 10
    default_server_timeout: 20
    
    # 使用标准的行为树
    default_nav_to_pose_bt_xml: "/opt/ros/humble/share/nav2_bt_navigator/behavior_trees/navigate_to_pose_w_replanning_and_recovery.xml"
    default_nav_through_poses_bt_xml: "/opt/ros/humble/share/nav2_bt_navigator/behavior_trees/navigate_through_poses_w_replanning_and_recovery.xml"
    
    # 插件配置
    plugin_lib_names:
    - nav2_compute_path_to_pose_action_bt_node
    - nav2_compute_path_through_poses_action_bt_node
    - nav2_smooth_path_action_bt_node
    - nav2_follow_path_action_bt_node
    - nav2_spin_action_bt_node
    - nav2_wait_action_bt_node
    - nav2_assisted_teleop_action_bt_node
    - nav2_back_up_action_bt_node
    - nav2_drive_on_heading_bt_node
    - nav2_clear_costmap_service_bt_node
    - nav2_is_stuck_condition_bt_node
    - nav2_goal_reached_condition_bt_node
    - nav2_goal_updated_condition_bt_node
    - nav2_globally_updated_goal_condition_bt_node
    - nav2_is_path_valid_condition_bt_node
    - nav2_initial_pose_received_condition_bt_node
    - nav2_reinitialize_global_localization_service_bt_node
    - nav2_rate_controller_bt_node
    - nav2_distance_controller_bt_node
    - nav2_speed_controller_bt_node
    - nav2_truncate_path_action_bt_node
    - nav2_truncate_path_local_action_bt_node
    - nav2_goal_updater_node_bt_node
    - nav2_recovery_node_bt_node
    - nav2_pipeline_sequence_bt_node
    - nav2_round_robin_node_bt_node
    - nav2_transform_available_condition_bt_node
    - nav2_time_expired_condition_bt_node
    - nav2_path_expiring_timer_condition
    - nav2_distance_traveled_condition_bt_node
    - nav2_single_trigger_bt_node
    - nav2_goal_updated_controller_bt_node
    - nav2_is_battery_low_condition_bt_node
    - nav2_navigate_through_poses_action_bt_node
    - nav2_navigate_to_pose_action_bt_node
    - nav2_remove_passed_goals_action_bt_node
    - nav2_planner_selector_bt_node
    - nav2_controller_selector_bt_node
    - nav2_goal_checker_selector_bt_node
    - nav2_controller_cancel_bt_node
    - nav2_path_longer_on_approach_bt_node
    - nav2_wait_cancel_bt_node
    - nav2_spin_cancel_bt_node
    - nav2_back_up_cancel_bt_node
    - nav2_assisted_teleop_cancel_bt_node
    - nav2_drive_on_heading_cancel_bt_node
    - nav2_is_battery_charging_condition_bt_node

# ========== 控制器服务器配置 ==========
# 使用MPPI控制器替代PolarBear控制器
controller_server:
  ros__parameters:
    use_sim_time: false
    odom_topic: odom
    controller_frequency: 25.0              # 匹配model_dt，确保频率兼容
    min_x_velocity_threshold: 0.001
    min_y_velocity_threshold: 0.0
    min_theta_velocity_threshold: 0.001
    failure_tolerance: 0.3
    progress_checker_plugins: ["progress_checker"]
    goal_checker_plugins: ["goal_checker"]
    controller_plugins: ["FollowPath"]

    progress_checker:
      plugin: "nav2_controller::SimpleProgressChecker"
      required_movement_radius: 0.5
      movement_time_allowance: 10.0

    goal_checker:
      plugin: "nav2_controller::SimpleGoalChecker"
      # {{ AURA-X: Modify - 调整目标容差适配大车体. Approval: 寸止(ID:large_robot_footprint). }}
      xy_goal_tolerance: 0.15              # 增加位置容差适配大车体
      yaw_goal_tolerance: 0.1             # 增加角度容差
      stateful: True
      
    # === MPPI控制器配置 ===
    # {{ AURA-X: Create - MPPI控制器配置适配750mm×600mm差分驱动机器人. Approval: 寸止(ID:mppi_differential_config). }}
    FollowPath:
      plugin: "nav2_mppi_controller::MPPIController"
      
      # --- 基本MPPI参数（大幅优化实时性） ---
      # {{ AURA-X: Fix - 大幅减少计算量解决50Hz频率警告. Approval: 寸止(ID:fix_frequency_warning). }}
      time_steps: 32                       # 大幅减少预测步数(0.8秒预测)
      model_dt: 0.04                       # 增大时间步长匹配25Hz控制频率
      batch_size: 800                      # 大幅减少采样数量提高实时性
      iteration_count: 1                   # 保持单次迭代确保实时性
      
      # --- 运动模型配置 ---
      motion_model: "DiffDrive"            # 差分驱动模型
      
      # --- 速度限制（允许倒退解决复杂规划） ---
      # {{ AURA-X: Fix - 增加倒退能力解决目标点无法到达问题. Approval: 寸止(ID:enable_reverse_motion). }}
      vx_max: 0.3                          # 保持前进速度
      vx_min: -0.2                         # 增加倒退速度，允许复杂机动
      vy_max: 0.0                          # 差分驱动无侧向运动
      vy_min: 0.0                          # 差分驱动无侧向运动
      wz_max: 0.3                          # 提高角速度，万向轮辅助原地转向
      wz_min: -0.3                         # 对称角速度，充分利用转向能力
      
      # --- 加速度限制（基于现有配置） ---
      ax_max: 0.2                          # 最大前进加速度，参考controllers.yaml
      ax_min: -0.2                         # 最大减速度
      ay_max: 0.0                          # 差分驱动无侧向加速度
      ay_min: 0.0
      az_max: 0.3                          # 最大角加速度，参考controllers.yaml
      
      # --- 采样标准差（优化轨迹跟踪精度） ---
      # {{ AURA-X: Optimize - 优化采样参数提高轨迹跟踪精度. Approval: 寸止(ID:precise_trajectory_tracking). }}
      vx_std: 0.08                         # 大幅降低前进速度方差，更精确跟踪
      vy_std: 0.0                          # 差分驱动无侧向运动
      wz_std: 0.15                         # 降低角速度方差，更精确转向控制
      
      # --- MPPI算法参数（优化轨迹跟踪精度） ---
      # {{ AURA-X: Optimize - 优化MPPI算法参数提高轨迹跟踪精度. Approval: 寸止(ID:mppi_tracking_precision). }}
      temperature: 0.1                     # 进一步降低温度，更确定性的轨迹选择
      gamma: 0.005                         # 进一步降低gamma，减少平滑性约束，提高响应性
      visualize: false                     # 关闭可视化提高性能
      regenerate_noises: false             # 使用固定噪声分布提高性能
      reset_period: 0.5                    # 缩短重置周期，更快适应新情况
      retry_attempt_limit: 2               # 增加重试次数，提高成功率
      
      # --- 路径处理配置（优化路径跟踪） ---
      # {{ AURA-X: Optimize - 优化路径处理提高跟踪精度. Approval: 寸止(ID:path_tracking_optimization). }}
      transform_tolerance: 0.1
      prune_distance: 1.2                  # 路径修剪距离，适配预测范围
      max_robot_pose_search_dist: 10.0
      enforce_path_inversion: false        # 不强制路径反转
      inversion_xy_tolerance: 0.2          # 路径反转XY容差
      inversion_yaw_tolerance: 0.4         # 路径反转角度容差
      
      # --- 轨迹验证器 ---
      TrajectoryValidator:
        plugin: "mppi::DefaultOptimalTrajectoryValidator"
        collision_lookahead_time: 1.8      # 碰撞前瞻时间
        consider_footprint: true           # 考虑机器人轮廓
      
      # --- 评价函数配置 ---
      # {{ AURA-X: Optimize - 优化评价函数组合实现直线优先+路点旋转. Approval: 寸止(ID:straight_line_waypoint_rotation). }}
      critics: ["ConstraintCritic", "CostCritic", "GoalCritic", "GoalAngleCritic", "PathAlignCritic", "PathFollowCritic", "PathAngleCritic", "PreferForwardCritic", "VelocityDeadbandCritic"]
      
      # 约束评价函数 - 确保轨迹在动力学约束内（支持原地旋转）
      # {{ AURA-X: Fix - 优化约束评价支持原地旋转. Approval: 寸止(ID:constraint_in_place_rotation). }}
      ConstraintCritic:
        enabled: true
        cost_power: 1
        cost_weight: 2.0                   # 降低权重，允许更灵活的运动
      
      # 代价评价函数 - 平衡避障和流畅性
      # {{ AURA-X: Fix - 平衡避障和流畅性，减少卡顿. Approval: 寸止(ID:balance_collision_smoothness). }}
      CostCritic:
        enabled: true
        cost_power: 2                      # 平方惩罚，避免过度反应
        cost_weight: 15.0                  # 适度权重，保证避障但不过度
        critical_cost: 150.0               # 适度临界代价，避免过早触发
        consider_footprint: true           # 使用完整轮廓进行碰撞检测
        collision_cost: 10000.0            # 适度碰撞代价，避免过度惩罚
        near_goal_distance: 0.8            # 更大范围内开始接近目标行为
        trajectory_point_step: 2           # 适度精细的碰撞检测
      
      # 目标评价函数 - 角度优先策略，旋转完成后再朝目标移动
      # {{ AURA-X: Fix - 大幅降低目标权重，强制角度优先完成. Approval: 寸止(ID:angle_first_goal_weight). }}
      GoalCritic:
        enabled: true
        cost_power: 1                      # 降低到线性惩罚，减少对前进的强制
        cost_weight: 8.0                   # 大幅降低权重，避免过早朝目标移动
        threshold_to_consider: 0.5         # 进一步降低阈值，更近距离才开始目标导向
      
      # 目标角度评价函数 - 流畅角度控制
      # {{ AURA-X: Fix - 进一步优化角度控制流畅性. Approval: 寸止(ID:smooth_angle_control). }}
      GoalAngleCritic:
        enabled: true
        cost_power: 1                      # 线性惩罚，最大化流畅性
        cost_weight: 12.0                  # 进一步降低权重，减少卡顿
        threshold_to_consider: 0.5         # 更大阈值，减少过度敏感
      
      # 路径对齐评价函数 - 流畅路径跟踪
      # {{ AURA-X: Fix - 优化路径对齐，确保直线前进流畅. Approval: 寸止(ID:smooth_path_align). }}
      PathAlignCritic:
        enabled: true
        cost_power: 1
        cost_weight: 8.0                   # 适度权重，保证路径跟踪但不过度
        max_path_occupancy_ratio: 0.1      # 适度路径占用比例
        trajectory_point_step: 2           # 适度步长，平衡精度和性能
        threshold_to_consider: 0.8         # 适度阈值，及时启动路径跟踪
        offset_from_furthest: 6            # 适度偏移，平衡响应和稳定
        use_path_orientations: true        # 启用路径朝向
      
      # 路径跟踪评价函数 - 流畅路径跟踪
      # {{ AURA-X: Fix - 优化路径跟踪，确保直线前进流畅. Approval: 寸止(ID:smooth_path_follow). }}
      PathFollowCritic:
        enabled: true
        cost_power: 1
        cost_weight: 6.0                   # 适度权重，保证路径跟踪流畅
        offset_from_furthest: 4            # 适度偏移，平衡响应和稳定
        threshold_to_consider: 0.6         # 适度阈值，及时启动路径跟踪
      
      # 路径角度评价函数 - 平衡路径跟踪和控制稳定性
      # {{ AURA-X: Fix - 优化路径角度控制，减少超调问题. Approval: 寸止(ID:reduce_path_overshoot). }}
      PathAngleCritic:
        enabled: true
        cost_power: 1                      # 降低到线性惩罚，减少超调
        cost_weight: 8.0                   # 大幅降低权重，提高控制稳定性
        offset_from_furthest: 6            # 增加偏移，减少过度约束
        threshold_to_consider: 0.8         # 提高阈值，减少过早控制
        max_angle_to_furthest: 1.0         # 增加最大角度，允许更灵活控制
        mode: 0                            # 前进偏好模式
      
      # 前进偏好评价函数 - 角度优先策略，旋转完成后再前进
      # {{ AURA-X: Fix - 大幅降低前进偏好，强制角度优先完成. Approval: 寸止(ID:angle_first_forward). }}
      PreferForwardCritic:
        enabled: true
        cost_power: 1
        cost_weight: 0.5                   # 大幅降低权重，允许充分旋转
        threshold_to_consider: 1.0         # 增大影响范围，但权重极低

      # 速度死区评价函数 - 确保运动连续性
      # {{ AURA-X: Fix - 最小化速度死区影响，确保运动流畅. Approval: 寸止(ID:minimize_deadband_impact). }}
      VelocityDeadbandCritic:
        enabled: true
        cost_power: 1
        cost_weight: 2.0                   # 极小权重，最小化对运动的干扰
        deadband_velocities: [0.002, 0.0, 0.002]  # 极小死区阈值，确保运动连续

# ========== 局部代价地图配置 ==========
local_costmap:
  local_costmap:
    ros__parameters:
      # --- 基本参数配置 ---
      update_frequency: 20.0              # 地图更新频率(Hz)
      publish_frequency: 20.0             # 地图发布频率(Hz)
      global_frame: odom                   # 全局参考坐标系
      robot_base_frame: base_footprint     # 机器人本体坐标系
      transform_timeout: 1.0               # TF变换超时时间(秒)
      tf_buffer_duration: 10.0             # TF缓存持续时间(秒)
      use_sim_time: false                  # 使用真实时间
      rolling_window: true                 # 启用滚动窗口

      # --- 地图尺寸配置 ---
      # {{ AURA-X: Modify - 增加地图尺寸适配MPPI预测范围. Approval: 寸止(ID:mppi_costmap_size). }}
      width: 3                             # 增加地图宽度适配MPPI预测范围(1.8s*0.3m/s*2=1.08m，留余量)
      height: 3                            # 增加地图高度
      resolution: 0.03                     # 地图分辨率(m/pixel)

      # --- 机器人形状配置 ---
      # {{ AURA-X: Modify - 机器人轮廓适配750mm×600mm车体. Approval: 寸止(ID:large_robot_footprint). }}
      footprint: "[[0.4, 0.325], [0.4, -0.325], [-0.4, -0.325], [-0.4, 0.325]]"

      # --- 插件配置 ---
      plugins: ["static_layer", "obstacle_layer", "inflation_layer"]

      # === 静态层配置 ===
      static_layer:
        plugin: "nav2_costmap_2d::StaticLayer"
        map_subscribe_transient_local: True

      # === 障碍物层配置 ===
      obstacle_layer:
        plugin: "nav2_costmap_2d::ObstacleLayer"
        enabled: True
        observation_sources: scan
        footprint_clearing_enabled: true
        combination_method: 1
        scan:
          topic: merged
          max_obstacle_height: 2.0
          clearing: True
          marking: True
          data_type: "LaserScan"
          raytrace_max_range: 3.0
          raytrace_min_range: 0.0
          obstacle_max_range: 2.5
          obstacle_min_range: 0.0

      # === 膨胀层配置 ===
      # {{ AURA-X: Modify - 调整膨胀参数适配MPPI控制器. Approval: 寸止(ID:mppi_inflation_config). }}
      inflation_layer:
        plugin: "nav2_costmap_2d::InflationLayer"
        enabled: true
        cost_scaling_factor: 5.0           # 适中的代价缩放因子
        inflation_radius: 0.35             # 适配大车体的膨胀半径

      always_send_full_costmap: True

# ========== 全局代价地图配置 ==========
global_costmap:
  global_costmap:
    ros__parameters:
      # --- 基本参数配置 ---
      update_frequency: 20.0              # 地图更新频率(Hz)
      publish_frequency: 20.0             # 地图发布频率(Hz)
      global_frame: map                    # 全局参考坐标系
      robot_base_frame: base_footprint     # 机器人本体坐标系
      transform_timeout: 1.0               # TF变换超时时间(秒)
      tf_buffer_duration: 10.0             # TF缓存持续时间(秒)
      use_sim_time: false                  # 使用真实时间

      # --- 机器人形状配置 ---
      footprint: "[[0.4, 0.325], [0.4, -0.325], [-0.4, -0.325], [-0.4, 0.325]]"

      # --- 地图参数配置 ---
      resolution: 0.03                     # 地图分辨率(m/pixel)
      track_unknown_space: false           # 不跟踪未知空间

      # --- 插件配置 ---
      plugins: ["static_layer", "obstacle_layer", "inflation_layer"]

      # === 静态层配置 ===
      static_layer:
        plugin: "nav2_costmap_2d::StaticLayer"
        map_subscribe_transient_local: True

      # === 障碍物层配置 ===
      obstacle_layer:
        plugin: "nav2_costmap_2d::ObstacleLayer"
        enabled: True
        observation_sources: scan
        scan:
          topic: merged
          max_obstacle_height: 2.0
          clearing: True
          marking: True
          data_type: "LaserScan"
          raytrace_max_range: 3.0
          raytrace_min_range: 0.0
          obstacle_max_range: 2.5
          obstacle_min_range: 0.0

      # === 膨胀层配置 ===
      # {{ AURA-X: Modify - 全局膨胀参数适配Hybrid A*规划器. Approval: 寸止(ID:hybrid_astar_inflation). }}
      inflation_layer:
        plugin: "nav2_costmap_2d::InflationLayer"
        enabled: true
        cost_scaling_factor: 3.0           # 较低的代价缩放因子，允许更灵活的路径
        inflation_radius: 0.6              # 适度膨胀半径，平衡避障和流畅性

      always_send_full_costmap: True

# ========== 地图服务器配置 ==========
map_server:
  ros__parameters:
    use_sim_time: false
    yaml_filename: ""                     # 将由launch文件动态设置

# ========== 规划器服务器配置 ==========
# {{ AURA-X: Create - Smac Hybrid A*规划器配置适配大型差分驱动机器人. Approval: 寸止(ID:hybrid_astar_config). }}
planner_server:
  ros__parameters:
    expected_planner_frequency: 20.0      # 期望的规划频率(Hz)
    use_sim_time: false
    planner_plugins: ["GridBased"]

    # === Smac 2D A*规划器配置 ===
    # {{ AURA-X: Change - 改用SMAC 2D A*规划器提高轨迹跟踪性能. Approval: 寸止(ID:smac2d_for_tracking). }}
    GridBased:
      plugin: "nav2_smac_planner/SmacPlanner2D"

      # --- 基本配置 ---
      tolerance: 0.15                     # 目标容差，与goal_checker一致
      downsample_costmap: false           # 不降采样代价地图，保持精度
      downsampling_factor: 1
      allow_unknown: false                # 不允许通过未知区域，提高安全性
      max_iterations: 1000000             # 最大迭代次数
      max_on_approach_iterations: 1000    # 接近目标时的最大迭代次数
      max_planning_time: 3.0              # 最大规划时间(秒)

      # --- 2D A*搜索配置 ---
      # {{ AURA-X: Configure - 配置SMAC 2D A*搜索参数. Approval: 寸止(ID:smac2d_search_config). }}
      search_info:
        # 搜索方向：8方向搜索(包括对角线)
        # MOORE: 8方向, VON_NEUMANN: 4方向
        search_type: "MOORE"

      # --- 代价惩罚配置 ---
      cost_penalty: 1.3                   # 降低代价惩罚，允许更接近障碍物
      expansion_type: "A_STAR"             # 使用A*搜索算法

      # --- 性能优化配置 ---
      lookup_table_size: 20.0             # 查找表大小
      cache_obstacle_heuristic: false     # 不缓存障碍物启发式(动态环境)
      debug_visualizations: false         # 关闭调试可视化提高性能
      use_quadratic_cost_penalty: false   # 不使用二次代价惩罚
      downsample_obstacle_heuristic: true # 降采样障碍物启发式
      allow_primitive_interpolation: false # 不允许原语插值

      # --- 目标朝向模式 ---
      goal_heading_mode: "DEFAULT"        # 使用默认目标朝向模式

      # --- 路径平滑配置 ---
      smooth_path: true                   # 启用路径平滑
      smoother:
        max_iterations: 1000
        w_smooth: 0.3                     # 平滑权重
        w_data: 0.2                       # 数据权重
        tolerance: 1.0e-10                # 收敛容差
        do_refinement: true               # 启用细化
        refinement_num: 2                 # 细化次数

# ========== 路径平滑器服务器配置 ==========
smoother_server:
  ros__parameters:
    use_sim_time: false
    smoother_plugins: ["simple_smoother"]

    # === 简单平滑器配置 ===
    simple_smoother:
      plugin: "nav2_smoother::SimpleSmoother"
      tolerance: 1.0e-10
      max_its: 1000
      do_refinement: true
      w_data: 0.2
      w_smooth: 0.3

# ========== 行为服务器配置 ==========
behavior_server:
  ros__parameters:
    use_sim_time: false
    local_costmap_topic: local_costmap/costmap_raw
    global_costmap_topic: global_costmap/costmap_raw
    local_footprint_topic: local_costmap/published_footprint
    global_footprint_topic: global_costmap/published_footprint
    cycle_frequency: 10.0
    behavior_plugins: ["spin", "backup", "drive_on_heading", "wait"]

    # === 行为插件配置 ===
    spin:
      plugin: "nav2_behaviors/Spin"
    backup:
      plugin: "nav2_behaviors/BackUp"
    drive_on_heading:
      plugin: "nav2_behaviors/DriveOnHeading"
    wait:
      plugin: "nav2_behaviors/Wait"

    # --- 行为参数配置 ---
    local_frame: odom
    global_frame: map
    robot_base_frame: base_footprint
    transform_tolerance: 0.4
    simulate_ahead_time: 2.0
    # {{ AURA-X: Modify - 调整旋转速度适配大型机器人. Approval: 寸止(ID:large_robot_behavior). }}
    max_rotational_vel: 0.4               # 降低最大旋转速度
    min_rotational_vel: 0.2               # 降低最小旋转速度
    rotational_acc_lim: 0.6               # 降低旋转加速度限制

# ========== 路径点跟随器配置 ==========
waypoint_follower:
  ros__parameters:
    use_sim_time: false
    loop_rate: 20
    stop_on_failure: false
    waypoint_task_executor_plugin: "wait_at_waypoint"

    wait_at_waypoint:
      plugin: "nav2_waypoint_follower::WaitAtWaypoint"
      enabled: True
      waypoint_pause_duration: 2000

# ========== 速度平滑器配置 ==========
# {{ AURA-X: Modify - 速度平滑器参数适配MPPI控制器. Approval: 寸止(ID:mppi_velocity_smoother). }}
velocity_smoother:
  ros__parameters:
    use_sim_time: false
    smoothing_frequency: 40.0             # 提高平滑频率匹配MPPI
    scale_velocities: false
    feedback: "OPEN_LOOP"

    # --- 速度限制配置 ---
    # {{ AURA-X: Fix - 优化速度控制，提高整体流畅性. Approval: 寸止(ID:optimize_velocity_smoothness). }}
    # 平衡线速度和角速度，优化整体流畅性
    max_velocity: [0.25, 0.0, 0.3]       # 提高线速度，确保直线前进流畅
    min_velocity: [0.0, 0.0, -0.3]       # 禁用倒退，提高角速度
    max_accel: [0.3, 0.0, 0.3]           # 提高加速度，增强响应性
    max_decel: [-0.3, 0.0, -0.3]         # 对应的减速度

    # --- 其他配置 ---
    odom_topic: "odom"
    odom_duration: 0.1
    deadband_velocity: [0.0, 0.0, 0.0]
    velocity_timeout: 1.0

# ========== Cartographer节点配置 ==========
cartographer_node:
  ros__parameters:
    use_sim_time: false

# Cartographer占用栅格节点配置
cartographer_occupancy_grid_node:
  ros__parameters:
    use_sim_time: false
