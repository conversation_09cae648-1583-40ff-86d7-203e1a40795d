
-- 继承建图配置作为基础配置
include "cartographer_mapping.lua"

-- ========== 纯定位模式配置 ==========
-- === 启用纯定位模式 ===
-- 冻结地图构建，只进行定位，不创建新的子地图
TRAJECTORY_BUILDER.pure_localization_trimmer = {
    max_submaps_to_keep = 3,              -- 保留的子地图数量，减少内存占用
}

-- === 禁用新子地图创建 ===
-- 强制使用预加载的地图，防止在定位过程中创建新子地图
TRAJECTORY_BUILDER_2D.submaps.num_range_data = 999999    -- 设置极大值，确保不会创建新子地图

-- ========== 定位模式专用参数优化 ==========
-- 针对纯定位模式的特殊需求进行参数调整，重点是减少抖动和提高稳定性

-- === 运动滤波器优化 ===
-- 平衡Motion Filter，既减少抖动又保持响应性
TRAJECTORY_BUILDER_2D.motion_filter.max_time_seconds = 0.8           -- 适中时间阈值，平衡过滤和响应性
TRAJECTORY_BUILDER_2D.motion_filter.max_distance_meters = 0.03       -- 进一步减小距离阈值，提高定位精度
TRAJECTORY_BUILDER_2D.motion_filter.max_angle_radians = math.rad(1.5) -- 适中角度阈值，减少角度抖动

-- === Ceres扫描匹配器稳定性增强 ===
-- 提高扫描匹配的稳定性，减少定位抖动
TRAJECTORY_BUILDER_2D.ceres_scan_matcher.translation_weight = 50.0   -- 增加平移权重，提高位置稳定性
TRAJECTORY_BUILDER_2D.ceres_scan_matcher.rotation_weight = 100.0     -- 增加旋转权重，减少角度抖动
TRAJECTORY_BUILDER_2D.ceres_scan_matcher.ceres_solver_options.max_num_iterations = 10  -- 减少迭代次数，提高实时性

-- === 发布频率优化 ===
-- 修改继承的options表中的发布频率参数，适配定位模式需求
options.pose_publish_period_sec = 0.05       -- 位姿发布频率20Hz，提高定位精度
options.trajectory_publish_period_sec = 0.1  -- 轨迹发布频率10Hz，适中频率

return options  -- 返回完整配置选项

