### Nav2完整配置文件（已修复RotationShimController旋转逻辑）
# 适用场景: Cartographer定位 + Nav2导航
# 机器人类型: 差分驱动，矩形footprint 0.7 m×0.56 m
# 传感器: 激光雷达 + IMU + 里程计
# 关键修复: RotationShimController阈值收紧，确保旋转到路径方向后才由DWB接管

# =======================================================================
# 1. 行为树导航器
# =======================================================================
bt_navigator:
  ros__parameters:
    use_sim_time: false
    global_frame: map
    robot_base_frame: base_footprint
    odom_topic: odom
    bt_loop_duration: 10
    default_server_timeout: 20

    # Groot2
    enable_groot_monitoring: true
    groot_zmq_publisher_port: 1666
    groot_zmq_server_port: 1667

    default_nav_to_pose_bt_xml: "/opt/ros/humble/share/nav2_bt_navigator/behavior_trees/navigate_to_pose_w_replanning_and_recovery.xml"
    default_nav_through_poses_bt_xml: "/opt/ros/humble/share/nav2_bt_navigator/behavior_trees/navigate_through_poses_w_replanning_and_recovery.xml"

    plugin_lib_names:
      - nav2_compute_path_to_pose_action_bt_node
      - nav2_compute_path_through_poses_action_bt_node
      - nav2_smooth_path_action_bt_node
      - nav2_follow_path_action_bt_node
      - nav2_back_up_action_bt_node
      - nav2_spin_action_bt_node
      - nav2_wait_action_bt_node
      - nav2_clear_costmap_service_bt_node
      - nav2_is_stuck_condition_bt_node
      - nav2_goal_reached_condition_bt_node
      - nav2_goal_updated_condition_bt_node
      - nav2_globally_updated_goal_condition_bt_node
      - nav2_is_path_valid_condition_bt_node
      - nav2_initial_pose_received_condition_bt_node
      - nav2_transform_available_condition_bt_node
      - nav2_time_expired_condition_bt_node
      - nav2_path_expiring_timer_condition
      - nav2_distance_traveled_condition_bt_node
      - nav2_is_battery_low_condition_bt_node
      - nav2_is_battery_charging_condition_bt_node
      - nav2_reinitialize_global_localization_service_bt_node
      - nav2_rate_controller_bt_node
      - nav2_distance_controller_bt_node
      - nav2_speed_controller_bt_node
      - nav2_truncate_path_action_bt_node
      - nav2_truncate_path_local_action_bt_node
      - nav2_goal_updater_node_bt_node
      - nav2_recovery_node_bt_node
      - nav2_pipeline_sequence_bt_node
      - nav2_round_robin_node_bt_node
      - nav2_single_trigger_bt_node
      - nav2_goal_updated_controller_bt_node
      - nav2_navigate_through_poses_action_bt_node
      - nav2_navigate_to_pose_action_bt_node
      - nav2_remove_passed_goals_action_bt_node
      - nav2_planner_selector_bt_node
      - nav2_controller_selector_bt_node
      - nav2_goal_checker_selector_bt_node
      - nav2_controller_cancel_bt_node
      - nav2_path_longer_on_approach_bt_node
      - nav2_wait_cancel_bt_node
      - nav2_spin_cancel_bt_node
      - nav2_back_up_cancel_bt_node
      - nav2_assisted_teleop_cancel_bt_node
      - nav2_drive_on_heading_cancel_bt_node

# =======================================================================
# 2. 控制器服务器（RotationShimController + DWB）
# =======================================================================
controller_server:
  ros__parameters:
    use_sim_time: false
    odom_topic: odom
    controller_frequency: 20.0
    min_x_velocity_threshold: 0.001
    min_y_velocity_threshold: 0.0
    min_theta_velocity_threshold: 0.001
    failure_tolerance: 0.3
    progress_checker_plugins: ["progress_checker"]
    goal_checker_plugins: ["goal_checker"]
    controller_plugins: ["FollowPath"]

    progress_checker:
      plugin: "nav2_controller::SimpleProgressChecker"
      required_movement_radius: 0.8
      movement_time_allowance: 10.0

    goal_checker:
      plugin: "nav2_controller::SimpleGoalChecker"
      xy_goal_tolerance: 0.13
      yaw_goal_tolerance: 0.08
      stateful: true

    FollowPath:
      plugin: "nav2_rotation_shim_controller::RotationShimController"
      primary_controller: "dwb_core::DWBLocalPlanner"

      # ---------- RotationShim 关键修复 ----------
      angular_dist_threshold: 0.785          # 严格触发旋转
      angular_disengage_threshold: 0.3925     # 确保完全对准后再退出
      forward_sampling_distance: 0.5
      rotate_to_heading_angular_vel: 3.2    # 提高旋转速度
      max_angular_accel: 3.2               # 提高角加速度
      simulate_ahead_time: 1.0
      rotate_to_goal_heading: false
      # ------------------------------------------

      # ---------- DWB 参数 ----------
      debug_trajectory_details: false
      critics:
        - RotateToGoal
        - Oscillation
        - BaseObstacle
        - GoalAlign
        - PathAlign
        - PathDist
        - GoalDist
        - PreferForward

      min_vel_x: -0.25
      min_vel_y: 0.0
      max_vel_x: 0.25
      max_vel_y: 0.0
      max_vel_theta: 0.5
      min_vel_theta: -0.5
      min_speed_xy: 0.0
      max_speed_xy: 0.4

      acc_lim_x: 0.05
      acc_lim_y: 0.0
      acc_lim_theta: 0.3
      decel_lim_x: -0.3
      decel_lim_y: 0.0
      decel_lim_theta: -0.4

      vx_samples: 20
      vy_samples: 0
      vtheta_samples: 40
      sim_time: 1.7
      linear_granularity: 0.2
      angular_granularity: 0.025
      transform_tolerance: 0.1
      xy_goal_tolerance: 0.13
      trans_stopped_velocity: 0.04
      short_circuit_trajectory_evaluation: true
      stateful: true

      BaseObstacle.scale: 0.8
      PathAlign.scale: 40.0
      PathAlign.forward_point_distance: 0.5
      PathDist.scale: 40.0
      GoalAlign.scale: 32.0
      GoalAlign.forward_point_distance: 0.24
      GoalDist.scale: 28.0
      RotateToGoal.scale: 30.0
      RotateToGoal.slowing_factor: 6.0
      RotateToGoal.lookahead_time: 1.0
      Oscillation.scale: 0.5
      Oscillation.oscillation_reset_dist: 0.2
      Oscillation.oscillation_reset_angle: 0.2
      Oscillation.oscillation_reset_time: 2.0
      PreferForward.scale: 0.5
      PreferForward.penalty: 0.5

      include_last_point: true
      prune_plan: true
      prune_distance: 1.0
      discretize_by_time: false
      time_granularity: 0.2
      use_dwa: false
      restore_defaults: false

# =======================================================================
# 3. 局部代价地图
# =======================================================================
local_costmap:
  local_costmap:
    ros__parameters:
      update_frequency: 20.0
      publish_frequency: 20.0
      global_frame: map
      robot_base_frame: base_footprint
      transform_timeout: 1.0
      tf_buffer_duration: 10.0
      use_sim_time: false
      rolling_window: true
      width: 3
      height: 3
      resolution: 0.03
      footprint: "[[0.42,0.35],[0.42,-0.35],[-0.42,-0.35],[-0.42,0.35]]"
      plugins: ["static_layer","obstacle_layer","inflation_layer"]
      static_layer:
        plugin: "nav2_costmap_2d::StaticLayer"
        map_subscribe_transient_local: true
      obstacle_layer:
        plugin: "nav2_costmap_2d::ObstacleLayer"
        enabled: true
        observation_sources: scan
        footprint_clearing_enabled: true
        combination_method: 1
        scan:
          topic: merged
          max_obstacle_height: 0.5
          clearing: true
          marking: true
          data_type: "LaserScan"
          raytrace_max_range: 3.0
          raytrace_min_range: 0.0
          obstacle_max_range: 2.5
          obstacle_min_range: 0.0
      inflation_layer:
        plugin: "nav2_costmap_2d::InflationLayer"
        enabled: true
        cost_scaling_factor: 1.0
        inflation_radius: 0.26
      always_send_full_costmap: true

# =======================================================================
# 4. 全局代价地图
# =======================================================================
global_costmap:
  global_costmap:
    ros__parameters:
      update_frequency: 20.0
      publish_frequency: 20.0
      global_frame: map
      robot_base_frame: base_footprint
      transform_timeout: 1.0
      tf_buffer_duration: 10.0
      use_sim_time: false
      footprint: "[[0.38,0.32],[0.38,-0.32],[-0.38,-0.32],[-0.38,0.32]]"
      resolution: 0.03
      track_unknown_space: false
      plugins: ["static_layer","obstacle_layer","inflation_layer"]
      static_layer:
        plugin: "nav2_costmap_2d::StaticLayer"
        map_subscribe_transient_local: true
      obstacle_layer:
        plugin: "nav2_costmap_2d::ObstacleLayer"
        enabled: true
        observation_sources: scan
        scan:
          topic: merged
          max_obstacle_height: 0.5
          clearing: true
          marking: true
          data_type: "LaserScan"
          raytrace_max_range: 3.0
          raytrace_min_range: 0.0
          obstacle_max_range: 2.5
          obstacle_min_range: 0.0
      inflation_layer:
        plugin: "nav2_costmap_2d::InflationLayer"
        enabled: true
        cost_scaling_factor: 3.0
        inflation_radius: 0.7
      always_send_full_costmap: true

# =======================================================================
# 5. 地图服务器
# =======================================================================
map_server:
  ros__parameters:
    use_sim_time: false
    yaml_filename: ""

# =======================================================================
# 6. 规划器服务器
# =======================================================================
planner_server:
  ros__parameters:
    expected_planner_frequency: 20.0
    use_sim_time: false
    planner_plugins: ["GridBased"]
    GridBased:
      plugin: "nav2_smac_planner/SmacPlanner2D"
      tolerance: 0.1
      downsample_costmap: false
      downsampling_factor: 1
      allow_unknown: false
      max_iterations: 1000000
      max_on_approach_iterations: 1000
      max_planning_time: 2.0
      motion_model_for_search: "MOORE"
      angle_quantization_bins: 72
      cost_penalty: 1.5
      use_final_approach_orientation: false
      smooth_path: true

# =======================================================================
# 7. 路径平滑器服务器
# =======================================================================
smoother_server:
  ros__parameters:
    use_sim_time: false
    smoother_plugins: ["simple_smoother"]
    simple_smoother:
      plugin: "nav2_smoother::SimpleSmoother"
      tolerance: 1.0e-10
      max_its: 1000
      do_refinement: true

# =======================================================================
# 8. 行为服务器
# =======================================================================
behavior_server:
  ros__parameters:
    use_sim_time: false
    local_costmap_topic: local_costmap/costmap_raw
    global_costmap_topic: global_costmap/costmap_raw
    local_footprint_topic: local_costmap/published_footprint
    global_footprint_topic: global_costmap/published_footprint
    cycle_frequency: 10.0
    behavior_plugins: ["spin","backup","drive_on_heading","wait"]
    spin:
      plugin: "nav2_behaviors/Spin"
    backup:
      plugin: "nav2_behaviors/BackUp"
    drive_on_heading:
      plugin: "nav2_behaviors/DriveOnHeading"
    wait:
      plugin: "nav2_behaviors/Wait"
    local_frame: odom
    global_frame: map
    robot_base_frame: base_footprint
    transform_tolerance: 0.4
    simulate_ahead_time: 2.0
    max_rotational_vel: 0.3
    min_rotational_vel: 0.2
    rotational_acc_lim: 0.3

# =======================================================================
# 9. 路径点跟随器
# =======================================================================
waypoint_follower:
  ros__parameters:
    use_sim_time: false
    loop_rate: 20
    stop_on_failure: false
    waypoint_task_executor_plugin: "wait_at_waypoint"
    wait_at_waypoint:
      plugin: "nav2_waypoint_follower::WaitAtWaypoint"
      enabled: true
      waypoint_pause_duration: 2000

# =======================================================================
# 10. 速度平滑器
# =======================================================================
velocity_smoother:
  ros__parameters:
    use_sim_time: false
    smoothing_frequency: 40.0
    scale_velocities: false
    feedback: "OPEN_LOOP"
    max_velocity: [0.2, 0.0, 0.2]
    min_velocity: [0.0, 0.0, -0.2]
    max_accel: [0.2, 0.0, 0.2]
    max_decel: [-0.2, 0.0, -0.2]
    odom_topic: "odom"
    odom_duration: 0.1
    deadband_velocity: [0.0, 0.0, 0.0]
    velocity_timeout: 1.0

# =======================================================================
# 11. Cartographer节点
# =======================================================================
cartographer_node:
  ros__parameters:
    use_sim_time: false

cartographer_occupancy_grid_node:
  ros__parameters:
    use_sim_time: false