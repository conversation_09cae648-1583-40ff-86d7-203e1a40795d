# ========== 地标检测参数配置文件 ==========
# 专为反光板检测和地标定位优化
# 适用场景: 室内环境反光板定位、多地标约束SLAM
# 传感器配置: 激光雷达强度数据
# {{ AURA-X: Add - 地标检测参数配置，用于反光板检测和约束定位. }}

landmark_detector:
  ros__parameters:
    # ========== 反光板检测参数 ==========
    # 强度阈值 - 用于识别反光板的激光强度阈值
    # 值越高越严格，减少误检但可能漏检弱反光板
    # 建议范围: 1500-3000，根据反光板材质和激光雷达特性调整
    intensity_threshold: 2000.0
    
    # 聚类参数 - 控制反光板点云的聚类行为
    min_cluster_size: 3              # 最小聚类大小，过小可能是噪声
    max_cluster_size: 10             # 最大聚类大小，过大可能是墙面反射
    cluster_tolerance: 0.1           # 聚类容差(米)，控制点云聚合距离
    
    # ========== 地标约束参数 ==========
    # 地标间距约束 - 用于多地标约束优化
    min_landmark_distance: 1.0       # 最小地标间距(米)，过近的地标权重不增加
    max_landmark_distance: 10.0      # 最大地标间距(米)，过远的地标权重不增加
    
    # 约束权重 - 控制地标在SLAM中的影响力
    base_translation_weight: 1000.0  # 基础平移权重，影响位置约束强度
    base_rotation_weight: 100.0      # 基础旋转权重，影响朝向约束强度
    
    # ========== 发布和可视化参数 ==========
    # 发布频率 - 控制地标信息的更新频率
    publish_rate: 20.0               # 发布频率(Hz)，建议与激光雷达频率匹配
    
    # 可视化设置
    enable_visualization: true       # 启用RViz可视化标记
    laser_frame: "laser_frame"       # 激光雷达坐标系名称
    
    # ========== 高级参数 ==========
    # 距离权重衰减 - 距离越远权重越低
    distance_weight_decay: 1.0       # 距离权重衰减因子
    
    # 多地标权重增益 - 多个地标同时可见时的权重增益
    multi_landmark_gain: 1.5         # 平移权重增益
    multi_landmark_rotation_gain: 1.2 # 旋转权重增益
    
    # 稳定性参数
    min_detection_confidence: 0.7    # 最小检测置信度
    landmark_timeout: 2.0            # 地标超时时间(秒)

# ========== 使用说明 ==========
# 1. intensity_threshold: 根据您的反光板材质调整
#    - 3M反光贴: 2000-2500
#    - 金属反光板: 2500-3000
#    - 反光漆: 1500-2000
#
# 2. 聚类参数调整:
#    - 如果检测到的地标过多且不稳定，增加min_cluster_size
#    - 如果漏检地标，减少intensity_threshold或min_cluster_size
#    - 如果地标位置跳动，减少cluster_tolerance
#
# 3. 权重参数调整:
#    - 如果地标约束过强导致建图扭曲，减少base_translation_weight
#    - 如果地标约束太弱，增加base_translation_weight
#    - 旋转约束通常比平移约束弱，保持rotation_weight < translation_weight
#
# 4. 多地标约束:
#    - 当同时检测到2个以上地标时，会自动增加权重
#    - 这有助于提高定位精度和稳定性
#    - 可通过multi_landmark_gain调整增益强度
