# Nav2融合配置文件 - 使用ADRC控制器
# 基于nav2_fusion_pb.yaml，将PB控制器替换为ADRC控制器
# {{ AURA-X: Create - ADRC控制器专用配置文件，实现准确顺滑到位、优先旋转、只向前运动. }}

# ========== 行为树导航器配置 ==========
bt_navigator:
  ros__parameters:
    use_sim_time: false
    global_frame: map
    robot_base_frame: base_footprint
    odom_topic: /odom
    bt_loop_duration: 10
    default_server_timeout: 20
    
    # 使用标准的行为树
    default_nav_to_pose_bt_xml: "/opt/ros/humble/share/nav2_bt_navigator/behavior_trees/navigate_to_pose_w_replanning_and_recovery.xml"
    default_nav_through_poses_bt_xml: "/opt/ros/humble/share/nav2_bt_navigator/behavior_trees/navigate_through_poses_w_replanning_and_recovery.xml"
    
    # 插件配置
    plugin_lib_names:
    - nav2_compute_path_to_pose_action_bt_node
    - nav2_compute_path_through_poses_action_bt_node
    - nav2_smooth_path_action_bt_node
    - nav2_follow_path_action_bt_node
    - nav2_spin_action_bt_node
    - nav2_wait_action_bt_node
    - nav2_assisted_teleop_action_bt_node
    - nav2_back_up_action_bt_node
    - nav2_drive_on_heading_bt_node
    - nav2_clear_costmap_service_bt_node
    - nav2_is_stuck_condition_bt_node
    - nav2_goal_reached_condition_bt_node
    - nav2_goal_updated_condition_bt_node
    - nav2_globally_updated_goal_condition_bt_node
    - nav2_is_path_valid_condition_bt_node
    - nav2_initial_pose_received_condition_bt_node
    - nav2_reinitialize_global_localization_service_bt_node
    - nav2_rate_controller_bt_node
    - nav2_distance_controller_bt_node
    - nav2_speed_controller_bt_node
    - nav2_truncate_path_action_bt_node
    - nav2_truncate_path_local_action_bt_node
    - nav2_goal_updater_node_bt_node
    - nav2_recovery_node_bt_node
    - nav2_pipeline_sequence_bt_node
    - nav2_round_robin_node_bt_node
    - nav2_transform_available_condition_bt_node
    - nav2_time_expired_condition_bt_node
    - nav2_path_expiring_timer_condition
    - nav2_distance_traveled_condition_bt_node
    - nav2_single_trigger_bt_node
    - nav2_goal_updated_controller_bt_node
    - nav2_is_battery_low_condition_bt_node
    - nav2_navigate_through_poses_action_bt_node
    - nav2_navigate_to_pose_action_bt_node
    - nav2_remove_passed_goals_action_bt_node
    - nav2_planner_selector_bt_node
    - nav2_controller_selector_bt_node
    - nav2_goal_checker_selector_bt_node
    - nav2_controller_cancel_bt_node
    - nav2_path_longer_on_approach_bt_node
    - nav2_wait_cancel_bt_node
    - nav2_spin_cancel_bt_node
    - nav2_back_up_cancel_bt_node
    - nav2_assisted_teleop_cancel_bt_node
    - nav2_drive_on_heading_cancel_bt_node
    - nav2_is_battery_charging_condition_bt_node

# ========== 控制器服务器配置 ==========
# 使用ADRC控制器替代PB控制器
controller_server:
  ros__parameters:
    use_sim_time: false
    odom_topic: odom
    controller_frequency: 20.0
    min_x_velocity_threshold: 0.001
    min_y_velocity_threshold: 0.0      # 差分驱动不使用侧向运动
    min_theta_velocity_threshold: 0.001
    failure_tolerance: 0.3
    progress_checker_plugins: ["progress_checker"]
    goal_checker_plugins: ["goal_checker"]
    controller_plugins: ["FollowPath"]

    progress_checker:
      plugin: "nav2_controller::SimpleProgressChecker"
      required_movement_radius: 0.5
      movement_time_allowance: 10.0

    goal_checker:
      plugin: "nav2_controller::SimpleGoalChecker"
      xy_goal_tolerance: 0.15
      yaw_goal_tolerance: 0.2
      stateful: True
      
    FollowPath:
      plugin: "nav2_regulated_pure_pursuit_controller::RegulatedPurePursuitController"

      # === 基础控制参数（优化为ADRC风格） ===
      # {{ AURA-X: Optimize - 优化控制参数实现准确顺滑到位. }}
      desired_linear_vel: 0.4                    # 期望线速度 (m/s)，降低以提高精度
      max_linear_accel: 0.3                      # 最大线加速度 (m/s²)，平滑加速
      max_linear_decel: -0.3                     # 最大线减速度 (m/s²)，平滑减速

      # === 前瞻距离配置（精确跟踪） ===
      lookahead_dist: 0.5                        # 基础前瞻距离 (m)，减小提高精度
      min_lookahead_dist: 0.25                   # 最小前瞻距离 (m)
      max_lookahead_dist: 0.8                    # 最大前瞻距离 (m)，减小提高响应
      lookahead_time: 1.0                        # 前瞻时间 (s)，减小提高响应
      use_velocity_scaled_lookahead_dist: true   # 启用速度缩放的前瞻距离

      # === 速度限制参数（适配大车体） ===
      max_linear_vel: 0.4                        # 最大线速度 (m/s)，降低提高稳定性
      min_linear_vel: 0.0                        # 最小线速度 (m/s)
      max_angular_vel: 0.8                       # 最大角速度 (rad/s)，适配大车体
      min_angular_vel: 0.0                       # 最小角速度 (rad/s)

      # === 调节参数（ADRC风格控制） ===
      use_regulated_linear_velocity_scaling: true
      use_cost_regulated_linear_velocity_scaling: true  # 启用代价调节
      regulated_linear_scaling_min_radius: 0.6          # 减小半径阈值
      regulated_linear_scaling_min_speed: 0.15          # 降低最小速度
      cost_scaling_dist: 0.4                            # 代价缩放距离
      cost_scaling_gain: 1.5                            # 增加代价缩放增益
      inflation_cost_scaling_factor: 2.0                # 膨胀代价缩放

      # === 碰撞检测（安全保障） ===
      use_collision_detection: true
      max_allowed_time_to_collision_up_to_carrot: 0.8   # 减小碰撞时间阈值
      use_approach_vel_scaling: true
      approach_velocity_scaling_dist: 0.4               # 接近减速距离

      # === 旋转到目标（优先旋转策略） ===
      # {{ AURA-X: Optimize - 实现优先旋转然后追踪路径策略. }}
      use_rotate_to_heading: true
      rotate_to_heading_angular_vel: 0.6                # 降低旋转速度，提高精度
      rotate_to_heading_min_angle: 0.524                # 30度阈值，优先旋转
      max_angular_accel: 1.5                            # 角加速度限制

      # === 路径处理（提高跟踪精度） ===
      transform_tolerance: 0.1
      use_interpolation: true                           # 启用插值提高平滑性

      # === 目标容差（准确到位） ===
      goal_dist_tol: 0.15                              # 减小距离容差，提高精度
      goal_heading_tol: 0.15                           # 减小角度容差，提高精度

      # === 只向前运动配置 ===
      # {{ AURA-X: Configure - 配置只向前运动模式. }}
      allow_reversing: false                            # 禁止倒退，只向前运动

# ========== 全局规划器配置 ==========
planner_server:
  ros__parameters:
    expected_planner_frequency: 20.0
    use_sim_time: false
    planner_plugins: ["GridBased"]
    GridBased:
      plugin: "nav2_navfn_planner/NavfnPlanner"
      tolerance: 0.5
      use_astar: false
      allow_unknown: true

# ========== 平滑器配置 ==========
smoother_server:
  ros__parameters:
    use_sim_time: false
    smoother_plugins: ["simple_smoother"]
    simple_smoother:
      plugin: "nav2_smoother::SimpleSmoother"
      tolerance: 1.0e-10
      max_its: 1000
      do_refinement: True

# ========== 行为服务器配置 ==========
behavior_server:
  ros__parameters:
    costmap_topic: local_costmap/costmap_raw
    footprint_topic: local_costmap/published_footprint
    cycle_frequency: 10.0
    behavior_plugins: ["spin", "backup", "drive_on_heading", "wait"]
    spin:
      plugin: "nav2_behaviors/Spin"
    backup:
      plugin: "nav2_behaviors/BackUp"
    drive_on_heading:
      plugin: "nav2_behaviors/DriveOnHeading"
    wait:
      plugin: "nav2_behaviors/Wait"
    local_frame: odom
    global_frame: map
    robot_base_frame: base_footprint
    transform_tolerance: 0.4
    simulate_ahead_time: 2.0
    max_rotational_vel: 0.8                     # 适配大车体旋转速度
    min_rotational_vel: 0.1                     # 最小旋转速度
    rotational_acc_lim: 1.0                     # 旋转加速度限制

# ========== 路径跟随服务器配置 ==========
waypoint_follower:
  ros__parameters:
    use_sim_time: false
    loop_rate: 20
    stop_on_failure: false
    waypoint_task_executor_plugin: "wait_at_waypoint"
    wait_at_waypoint:
      plugin: "nav2_waypoint_follower::WaitAtWaypoint"
      enabled: True
      waypoint_pause_duration: 200

# ========== 速度限制器配置 ==========
velocity_smoother:
  ros__parameters:
    use_sim_time: false
    smoothing_frequency: 20.0
    scale_velocities: false
    feedback: "OPEN_LOOP"
    max_velocity: [0.5, 0.0, 1.0]
    min_velocity: [-0.5, 0.0, -1.0]
    max_accel: [0.5, 0.0, 1.0]
    max_decel: [-0.5, 0.0, -1.0]
    odom_topic: "odom"
    odom_duration: 0.1
    deadband_velocity: [0.0, 0.0, 0.0]
    velocity_timeout: 1.0

# ========== 碰撞监测器配置 ==========
collision_monitor:
  ros__parameters:
    use_sim_time: false
    base_frame_id: "base_footprint"
    odom_frame_id: "odom"
    cmd_vel_in_topic: "cmd_vel_smoothed"
    cmd_vel_out_topic: "cmd_vel"
    state_topic: "collision_monitor_state"
    transform_tolerance: 0.2
    source_timeout: 1.0
    base_shift_correction: True
    stop_pub_timeout: 2.0
    polygons: ["PolygonStop"]
    PolygonStop:
      type: "circle"
      radius: 0.5                                # 增加安全半径适配大车体
      action_type: "stop"
      max_points: 3
      visualize: True
      polygon_pub_topic: "polygon_stop"

# ========== 全局代价地图配置 ==========
global_costmap:
  global_costmap:
    ros__parameters:
      update_frequency: 1.0
      publish_frequency: 1.0
      global_frame: map
      robot_base_frame: base_footprint
      use_sim_time: false
      robot_radius: 0.45                         # 适配750mm×600mm车体 (对角线半径)
      resolution: 0.05
      track_unknown_space: true
      plugins: ["static_layer", "obstacle_layer", "inflation_layer"]
      obstacle_layer:
        plugin: "nav2_costmap_2d::ObstacleLayer"
        enabled: True
        observation_sources: scan
        scan:
          topic: /scan
          max_obstacle_height: 2.0
          clearing: True
          marking: True
          data_type: "LaserScan"
          raytrace_max_range: 3.0
          raytrace_min_range: 0.0
          obstacle_max_range: 2.5
          obstacle_min_range: 0.0
      static_layer:
        plugin: "nav2_costmap_2d::StaticLayer"
        map_subscribe_transient_local: True
      inflation_layer:
        plugin: "nav2_costmap_2d::InflationLayer"
        cost_scaling_factor: 3.0
        inflation_radius: 0.55
      always_send_full_costmap: True

# ========== 局部代价地图配置 ==========
local_costmap:
  local_costmap:
    ros__parameters:
      update_frequency: 5.0
      publish_frequency: 2.0
      global_frame: odom
      robot_base_frame: base_footprint
      use_sim_time: false
      rolling_window: true
      width: 3
      height: 3
      resolution: 0.05
      robot_radius: 0.45                         # 适配750mm×600mm车体
      plugins: ["voxel_layer", "inflation_layer"]
      inflation_layer:
        plugin: "nav2_costmap_2d::InflationLayer"
        cost_scaling_factor: 3.0
        inflation_radius: 0.55
      voxel_layer:
        plugin: "nav2_costmap_2d::VoxelLayer"
        enabled: True
        publish_voxel_map: True
        origin_z: 0.0
        z_resolution: 0.05
        z_voxels: 16
        max_obstacle_height: 2.0
        mark_threshold: 0
        observation_sources: scan
        scan:
          topic: /scan
          max_obstacle_height: 2.0
          clearing: True
          marking: True
          data_type: "LaserScan"
          raytrace_max_range: 3.0
          raytrace_min_range: 0.0
          obstacle_max_range: 2.5
          obstacle_min_range: 0.0
      always_send_full_costmap: True

# ========== 地图服务器配置 ==========
map_server:
  ros__parameters:
    use_sim_time: false
    yaml_filename: "turtlebot3_world.yaml"

# ========== 地图保存器配置 ==========
map_saver:
  ros__parameters:
    use_sim_time: false
    save_map_timeout: 5.0
    free_thresh_default: 0.25
    occupied_thresh_default: 0.65
    map_subscribe_transient_local: True
