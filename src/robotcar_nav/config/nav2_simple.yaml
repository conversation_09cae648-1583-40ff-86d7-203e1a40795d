### Nav2详细配置文件 - 基于成功架构的完整版本 ###
# 适用场景: Cartographer定位 + Nav2导航
# 机器人类型: 差分驱动，矩形footprint (0.7m×0.56m)
# 传感器配置: 激光雷达 + IMU + 里程计
# 关键成功要素: 独立的代价地图节点管理 + 直接订阅Cartographer地图

# ========== 行为树导航器配置 ==========
# BT Navigator是Nav2的核心决策模块，负责执行导航任务的高级逻辑
bt_navigator:
  ros__parameters:
    use_sim_time: false                    # 使用真实时间，非仿真模式
    global_frame: map                      # 全局参考坐标系，来自Cartographer
    robot_base_frame: base_footprint       # 机器人本体坐标系
    odom_topic: /odom                      # 里程计话题，来自EKF融合后的数据
    bt_loop_duration: 10                   # 行为树执行周期(ms)，控制决策频率
    default_server_timeout: 20             # 默认服务超时时间(秒)

    # Groot2监控配置
    enable_groot_monitoring: true          # 启用Groot2实时监控
    groot_zmq_publisher_port: 1666         # ZMQ发布端口，Groot2监听此端口
    groot_zmq_server_port: 1667            # ZMQ服务端口，用于交互式调试

    # 行为树XML文件配置 - 定义导航的决策逻辑
    default_nav_to_pose_bt_xml: "/opt/ros/humble/share/nav2_bt_navigator/behavior_trees/navigate_to_pose_w_replanning_and_recovery.xml"
    default_nav_through_poses_bt_xml: "/opt/ros/humble/share/nav2_bt_navigator/behavior_trees/navigate_through_poses_w_replanning_and_recovery.xml"
    
    # 行为树插件库 - 定义可用的行为树节点类型
    plugin_lib_names:
      # === 核心导航动作节点 ===
      - nav2_compute_path_to_pose_action_bt_node        # 计算到目标点的路径
      - nav2_compute_path_through_poses_action_bt_node  # 计算通过多个路径点的路径
      - nav2_smooth_path_action_bt_node                 # 路径平滑处理
      - nav2_follow_path_action_bt_node                 # 跟随路径执行
      # === 恢复行为动作节点 ===
      - nav2_back_up_action_bt_node                     # 后退恢复行为
      - nav2_spin_action_bt_node                        # 原地旋转恢复行为
      - nav2_wait_action_bt_node                        # 等待行为
      - nav2_clear_costmap_service_bt_node              # 清除代价地图服务
      # === 条件判断节点 ===
      - nav2_is_stuck_condition_bt_node                 # 判断是否卡住
      - nav2_goal_reached_condition_bt_node             # 判断是否到达目标
      - nav2_goal_updated_condition_bt_node             # 判断目标是否更新
      - nav2_globally_updated_goal_condition_bt_node    # 判断全局目标是否更新
      - nav2_is_path_valid_condition_bt_node            # 判断路径是否有效
      - nav2_initial_pose_received_condition_bt_node    # 判断是否接收到初始位姿
      - nav2_transform_available_condition_bt_node      # 判断TF变换是否可用
      - nav2_time_expired_condition_bt_node             # 判断时间是否超时
      - nav2_path_expiring_timer_condition              # 路径过期计时器条件
      - nav2_distance_traveled_condition_bt_node        # 判断行驶距离条件
      - nav2_is_battery_low_condition_bt_node           # 判断电池电量低
      - nav2_is_battery_charging_condition_bt_node      # 判断电池是否充电中
      # === 控制和选择节点 ===
      - nav2_reinitialize_global_localization_service_bt_node  # 重新初始化全局定位
      - nav2_rate_controller_bt_node                    # 速率控制器
      - nav2_distance_controller_bt_node                # 距离控制器
      - nav2_speed_controller_bt_node                   # 速度控制器
      - nav2_truncate_path_action_bt_node               # 路径截断动作
      - nav2_truncate_path_local_action_bt_node         # 局部路径截断动作
      - nav2_goal_updater_node_bt_node                  # 目标更新节点
      - nav2_recovery_node_bt_node                      # 恢复节点
      - nav2_pipeline_sequence_bt_node                  # 管道序列节点
      - nav2_round_robin_node_bt_node                   # 轮询节点
      - nav2_single_trigger_bt_node                     # 单次触发节点
      - nav2_goal_updated_controller_bt_node            # 目标更新控制器
      - nav2_navigate_through_poses_action_bt_node      # 多点导航动作
      - nav2_navigate_to_pose_action_bt_node            # 单点导航动作
      - nav2_remove_passed_goals_action_bt_node         # 移除已通过目标动作
      - nav2_planner_selector_bt_node                   # 规划器选择器
      - nav2_controller_selector_bt_node                # 控制器选择器
      - nav2_goal_checker_selector_bt_node              # 目标检查器选择器
      - nav2_controller_cancel_bt_node                  # 控制器取消节点
      - nav2_path_longer_on_approach_bt_node            # 接近时路径延长节点
      - nav2_wait_cancel_bt_node                        # 等待取消节点
      - nav2_spin_cancel_bt_node                        # 旋转取消节点
      - nav2_back_up_cancel_bt_node                     # 后退取消节点
      - nav2_assisted_teleop_cancel_bt_node             # 辅助遥控取消节点
      - nav2_drive_on_heading_cancel_bt_node            # 按航向行驶取消节点

# ========== 控制器服务器配置 ==========
# Controller Server负责局部路径跟踪和避障
controller_server:
  ros__parameters:
    # --- 基本配置 ---
    use_sim_time: false                   # 使用真实时间
    controller_frequency: 20.0            # 控制器执行频率(Hz)
    min_x_velocity_threshold: 0.001       # X方向最小速度阈值
    min_y_velocity_threshold: 0.5         # Y方向最小速度阈值(差分驱动不使用)
    min_theta_velocity_threshold: 0.001   # 角速度最小阈值
    failure_tolerance: 5.0                # 失败容忍度，连续失败次数
    
    # --- 插件配置 ---
    progress_checker_plugin: "progress_checker"      # 进度检查器插件
    goal_checker_plugins: ["goal_checker"]           # 目标检查器插件列表
    controller_plugins: ["FollowPath"]               # 控制器插件列表
    
    # === 进度检查器配置 ===
    progress_checker:
      plugin: "nav2_controller::SimpleProgressChecker"
      required_movement_radius: 0.5       # 要求的移动半径(m)，减小要求
      movement_time_allowance: 15.0       # 移动时间容限(秒)，增加容限
    
    # === 目标检查器配置 ===
    goal_checker:
      plugin: "nav2_controller::SimpleGoalChecker"
      xy_goal_tolerance: 0.15              # XY位置目标容差(m)，适当放宽
      yaw_goal_tolerance: 0.15             # 偏航角目标容差(rad)，约11度，放宽以减少卡顿
      stateful: True                       # 有状态检查
      check_xy: True                       # 检查XY位置
      check_theta: True                    # 检查偏航角

    # === 旋转垫片控制器配置 ===
    FollowPath:
      plugin: "nav2_rotation_shim_controller::RotationShimController"
      primary_controller: "dwb_core::DWBLocalPlanner"  # 主控制器仍使用DWB

      # --- 旋转垫片控制器专用参数 ---
      angular_dist_threshold: 0.13        # 角度差阈值(rad)，约30度，超过此角度才触发旋转
      forward_sampling_distance: 0.5      # 前向采样距离(m)，较近的采样点
      rotate_to_heading_angular_vel: 0.8   # 旋转到目标方向的角速度(rad/s)，提高旋转速度
      max_angular_accel: 0.7               # 最大角加速度(rad/s²)，提高旋转响应
      simulate_ahead_time: 0.8             # 前瞻仿真时间(秒)，减少预测时间

      # --- DWB局部规划器配置 ---
      debug_trajectory_details: False      # 启用轨迹调试详情

      # --- 调试配置 ---
      # 可以通过日志查看RotationShimController的切换行为

      # --- 速度限制配置 ---
      min_vel_x: 0.0                      # 最小X速度(m/s)，禁止倒车
      min_vel_y: 0.0                      # 最小Y速度(差分驱动为0)
      max_vel_x: 0.6                      # 最大X速度(m/s)，适当降低以提高安全性
      max_vel_y: 0.0                      # 最大Y速度(差分驱动为0)
      max_vel_theta: 0.4                  # 最大角速度(rad/s)，与RotationShim兼容
      min_speed_xy: 0.0                   # 最小线速度，确保不倒车
      max_speed_xy: 0.6                   # 最大线速度，适当降低以配合旋转行为
      min_speed_theta: 0.0                # 最小角速度

      # --- 加速度限制配置 ---
      acc_lim_x: 0.3                      # X方向加速度限制(m/s²)，降低以提高稳定性
      acc_lim_y: 0.0                      # Y方向加速度限制(差分驱动为0)
      acc_lim_theta: 0.8                  # 角加速度限制(rad/s²)，提高以支持快速旋转
      decel_lim_x: -0.5                   # X方向减速度限制，降低以提高稳定性
      decel_lim_y: 0.0                    # Y方向减速度限制
      decel_lim_theta: -1.5               # 角减速度限制，降低以提高稳定性

      # --- 采样配置 ---
      vx_samples: 15                      # X速度采样数，减少以提高性能
      vy_samples: 0                       # Y速度采样数(差分驱动为0)
      vtheta_samples: 20                  # 角速度采样数，保持较高精度

      # --- 轨迹评估配置 ---
      sim_time: 2.0                      # 轨迹仿真时间(秒)，适当减少
      linear_granularity: 0.05            # 线性粒度(m)
      angular_granularity: 0.025          # 角度粒度(rad)
      transform_tolerance: 0.5            # TF变换容差(秒)，增加容错性
      xy_goal_tolerance: 0.10              # XY目标容差(m)
      trans_stopped_velocity: 0.08        # 认为停止的平移速度阈值
      short_circuit_trajectory_evaluation: True  # 短路轨迹评估，提高性能
      stateful: True                      # 有状态评估

      # --- 性能优化配置 ---
      include_last_point: false           # 不包含最后一个点，减少计算
      trajectory_generator_name: "dwb_plugins::StandardTrajectoryGenerator"  # 使用标准轨迹生成器

      # --- 旋转优化配置 ---
      prefer_forward: true                 # 优先前进，减少旋转时的复杂决策
      split_path: true                     # 分割路径，让RotationShim更好地处理旋转段
      prune_plan: true                     # 修剪路径，减少不必要的计算

      # --- 评价函数配置 ---
      # 移除RotateToGoal critic，因为RotationShimController已处理旋转逻辑
      critics: ["Oscillation", "BaseObstacle", "GoalAlign", "PathAlign", "PathDist", "GoalDist"]
      BaseObstacle.scale: 1.0             # 基础障碍物评价权重，大幅提高避障优先级
      PathAlign.scale: 16.0               # 路径对齐评价权重，降低以平衡避障
      PathDist.scale: 16.0                # 路径距离评价权重，降低以平衡避障
      GoalAlign.scale: 16.0               # 目标对齐评价权重，降低以平衡避障
      GoalDist.scale: 16.0                # 目标距离评价权重，降低以平衡避障
      Oscillation.scale: 0.2              # 振荡抑制评价权重，提高以减少振荡

# ========== 局部代价地图配置 ==========
# 关键成功要素：作为独立节点在lifecycle_manager中管理
local_costmap:
  local_costmap:
    ros__parameters:
      # --- 基本参数配置 ---
      update_frequency: 20.0              # 地图更新频率(Hz)，高频率保证实时性
      publish_frequency: 20.0             # 地图发布频率(Hz)
      global_frame: odom                   # 全局参考坐标系(里程计坐标系)
      robot_base_frame: base_footprint     # 机器人本体坐标系
      transform_timeout: 1.0               # TF变换超时时间(秒)，增加容错性
      tf_buffer_duration: 10.0             # TF缓存持续时间(秒)
      use_sim_time: false                  # 使用真实时间
      rolling_window: true                 # 启用滚动窗口，跟随机器人移动
      
      # --- 地图尺寸配置 ---
      width: 3                             # 地图宽度(米)
      height: 3                            # 地图高度(米)
      resolution: 0.03                     # 地图分辨率(m/pixel)，与建图分辨率一致
      
      # --- 机器人形状配置 ---
      # 使用矩形footprint，基于URDF尺寸0.65×0.5m + 安全边距
      footprint: "[[0.35, 0.28], [0.35, -0.28], [-0.35, -0.28], [-0.35, 0.28]]"
      
      # --- 插件配置 ---
      plugins: ["static_layer", "obstacle_layer", "denoise_layer", "inflation_layer"]
      
      # === 静态层配置 ===
      # 成功关键：直接订阅Cartographer发布的/map话题
      static_layer:
        plugin: "nav2_costmap_2d::StaticLayer"
        map_subscribe_transient_local: True  # 订阅持久化地图话题
      
      # === 障碍物层配置 ===
      obstacle_layer:
        plugin: "nav2_costmap_2d::ObstacleLayer"
        enabled: True                      # 启用障碍物层
        observation_sources: scan          # 观测数据源
        footprint_clearing_enabled: true   # 启用机器人轮廓清除 
        combination_method: 1              # 组合方法：1取最大值 减少噪声影响
        scan:
          topic: /scan                     # 激光雷达话题
          max_obstacle_height: 2.0         # 最大障碍物高度(m)
          clearing: True                   # 启用障碍物清除
          marking: True                    # 启用障碍物标记
          data_type: "LaserScan"           # 数据类型
          raytrace_max_range: 3.0          # 光线追踪最大范围(m)
          raytrace_min_range: 0.0          # 光线追踪最小范围(m)
          obstacle_max_range: 2.5          # 障碍物检测最大范围(m)
          obstacle_min_range: 0.0          # 障碍物检测最小范围(m)
      
      # === 噪声过滤层配置 ===
      # 过滤激光雷达的噪声点和鬼点，减少对导航的干扰
      denoise_layer:
        plugin: "nav2_costmap_2d::DenoiseLayer"
        enabled: true                      # 启用噪声过滤层
        minimal_group_size: 2              # 最小群组大小，2表示移除孤立点
        group_connectivity_type: 8         # 连接类型：8邻域（包括对角线），更严格过滤
        # 局部代价地图：快速过滤孤立噪声点，提高导航稳定性

      # === 膨胀层配置 ===
      inflation_layer:
        plugin: "nav2_costmap_2d::InflationLayer"
        enabled: true                      # 启用膨胀层
        cost_scaling_factor: 3.0           # 代价缩放因子，影响膨胀梯度
        inflation_radius: 0.55             # 膨胀半径(m)，增加安全边距防止碰撞
      
      always_send_full_costmap: True       # 总是发送完整代价地图

# ========== 全局代价地图配置 ==========
# 关键成功要素：作为独立节点在lifecycle_manager中管理
global_costmap:
  global_costmap:
    ros__parameters:
      # --- 基本参数配置 ---
      update_frequency: 10.0              # 地图更新频率(Hz)，比局部地图低以节省计算
      publish_frequency: 10.0             # 地图发布频率(Hz)
      global_frame: map                    # 全局参考坐标系(地图坐标系)
      robot_base_frame: base_footprint     # 机器人本体坐标系
      transform_timeout: 1.0               # TF变换超时时间(秒)，增加容错性
      tf_buffer_duration: 10.0             # TF缓存持续时间(秒)
      use_sim_time: false                  # 使用真实时间
      
      # --- 机器人形状配置 ---
      # 与局部代价地图保持一致的矩形footprint
      footprint: "[[0.35, 0.28], [0.35, -0.28], [-0.35, -0.28], [-0.35, 0.28]]"
      
      # --- 地图参数配置 ---
      resolution: 0.03                     # 地图分辨率(m/pixel)，与Cartographer建图一致
      track_unknown_space: false           # 不跟踪未知空间，提高性能
      
      # --- 插件配置 ---
      plugins: ["static_layer", "obstacle_layer", "inflation_layer", "denoise_layer"]  # 静态层+障碍物层+膨胀层（暂时不在全局地图使用噪声过滤）
      
      denoise_layer:
        plugin: "nav2_costmap_2d::DenoiseLayer"
        enabled: true                      # 启用噪声过滤层
        minimal_group_size: 4              # 最小群组大小，2表示移除孤立点
        group_connectivity_type: 8         # 连接类型：8邻域（包括对角线），更严格过滤
      # 局部代价地图：快速过滤孤立噪声点，提高导航稳定性
      # 全局代价地图：更严格的噪声过滤，提高全局路径规划的准确性
      # === 静态层配置 ===
      # 成功关键：直接订阅Cartographer发布的/map话题
      static_layer:
        plugin: "nav2_costmap_2d::StaticLayer"
        map_subscribe_transient_local: True  # 订阅持久化地图话题
      
      # === 障碍物层配置 ===
      # 与局部代价地图类似，但用于全局路径规划
      obstacle_layer:
        plugin: "nav2_costmap_2d::ObstacleLayer"
        enabled: True                      # 启用障碍物层
        observation_sources: scan          # 观测数据源
        scan:
          topic: /scan                     # 激光雷达话题
          max_obstacle_height: 2.0         # 最大障碍物高度(m)
          clearing: True                   # 启用障碍物清除
          marking: True                    # 启用障碍物标记
          data_type: "LaserScan"           # 数据类型
          raytrace_max_range: 3.0          # 光线追踪最大范围(m)
          raytrace_min_range: 0.0          # 光线追踪最小范围(m)
          obstacle_max_range: 2.5          # 障碍物检测最大范围(m)
          obstacle_min_range: 0.0          # 障碍物检测最小范围(m)

      # === 膨胀层配置 ===
      # 全局膨胀半径比局部稍大，为长距离规划提供更多安全边距
      inflation_layer:
        plugin: "nav2_costmap_2d::InflationLayer"
        enabled: true                      # 启用膨胀层
        cost_scaling_factor: 3.0           # 代价缩放因子
        inflation_radius: 0.65             # 膨胀半径(m)，比局部地图大0.1m

      always_send_full_costmap: True       # 总是发送完整代价地图

# ========== 地图服务器配置 ==========
# Map Server负责加载和发布静态地图，由launch文件动态配置
map_server:
  ros__parameters:
    use_sim_time: false                   # 使用真实时间
    yaml_filename: ""                     # 将由launch文件动态设置

# ========== 规划器服务器配置 ==========
# Planner Server负责全局路径规划，从起点到终点生成最优路径
planner_server:
  ros__parameters:
    expected_planner_frequency: 10.0      # 期望的规划频率(Hz)
    use_sim_time: false                   # 使用真实时间
    planner_plugins: ["GridBased"]        # 规划器插件列表

    # === SMAC 2D规划器配置 ===
    # 适用于配合RotationShimController使用的2D规划算法
    # 不考虑运动学约束，生成更直接的路径，让RotationShimController处理旋转
    GridBased:
      plugin: "nav2_smac_planner/SmacPlanner2D"

      # --- 基本配置 ---
      tolerance: 0.1                      # 目标容差(m)，增加容差提高成功率
      downsample_costmap: false           # 不降采样代价地图，保持精度
      downsampling_factor: 1              # 降采样因子(未使用)
      allow_unknown: false                 # 允许通过未知区域，提高规划成功率
      max_iterations: 1000000             # 最大迭代次数
      max_on_approach_iterations: 1000    # 接近目标时的最大迭代次数
      max_planning_time: 3.0              # 最大规划时间(秒)

      # --- 运动模型配置 ---
      motion_model_for_search: "MOORE"    # 使用Moore邻域(8方向)，适合2D规划
      angle_quantization_bins: 72         # 角度量化分箱数(5度精度)

      # --- 代价惩罚配置 ---
      cost_penalty: 2.0                   # 代价惩罚，避免接近障碍物

      # --- 性能优化配置 ---
      use_final_approach_orientation: false  # 暂时关闭最终朝向要求，避免与RotationShim冲突
      smooth_path: true                   # 启用路径平滑

# ========== 路径平滑器服务器配置 ==========
# Smoother Server负责对规划的路径进行平滑处理，提高执行质量
smoother_server:
  ros__parameters:
    use_sim_time: false                   # 使用真实时间
    smoother_plugins: ["simple_smoother"]  # 使用简单平滑器

    # === 简单平滑器配置 ===
    # 基于简单算法的路径平滑，计算量小，适合实时应用
    simple_smoother:
      plugin: "nav2_smoother::SimpleSmoother"

      # --- 基本配置 ---
      tolerance: 1.0e-10                  # 收敛容差
      max_its: 1000                       # 最大迭代次数
      do_refinement: true                 # 启用路径细化

# ========== 行为服务器配置 ==========
# Behavior Server负责执行恢复行为，当导航失败时进行恢复操作
behavior_server:
  ros__parameters:
    # --- 基本配置 ---
    costmap_topic: local_costmap/costmap        # 代价地图话题
    footprint_topic: local_costmap/published_footprint  # 机器人轮廓话题
    cycle_frequency: 10.0                # 行为执行频率(Hz)
    behavior_plugins: ["spin", "backup", "drive_on_heading", "wait"]  # 行为插件列表

    # === 行为插件配置 ===
    spin:
      plugin: "nav2_behaviors/Spin"       # 原地旋转行为
    backup:
      plugin: "nav2_behaviors/BackUp"     # 后退行为
    drive_on_heading:
      plugin: "nav2_behaviors/DriveOnHeading"  # 按航向行驶行为
    wait:
      plugin: "nav2_behaviors/Wait"       # 等待行为

    # --- 行为参数配置 ---
    global_frame: odom                    # behavior使用odom坐标系
    robot_base_frame: base_footprint      # 机器人本体坐标系
    transform_tolerance: 0.5              # TF变换容差(秒)，增加容差
    use_sim_time: false                   # 使用真实时间
    simulate_ahead_time: 1.0              # 前瞻仿真时间(秒)，减少以提高稳定性
    max_rotational_vel: 0.4               # 最大旋转速度(rad/s)，与RotationShim保持一致
    min_rotational_vel: 0.2               # 最小旋转速度(rad/s)
    rotational_acc_lim: 0.6               # 旋转加速度限制(rad/s²)，与RotationShim保持一致

# ========== 路径点跟随器配置 ==========
# Waypoint Follower负责执行多点导航任务
waypoint_follower:
  ros__parameters:
    use_sim_time: false                   # 使用真实时间
    loop_rate: 20                         # 循环频率(Hz)
    stop_on_failure: false                # 失败时不停止，继续下一个路径点
    waypoint_task_executor_plugin: "wait_at_waypoint"  # 路径点任务执行器插件

    # === 路径点等待任务配置 ===
    wait_at_waypoint:
      plugin: "nav2_waypoint_follower::WaitAtWaypoint"
      enabled: True                       # 启用等待任务
      waypoint_pause_duration: 2000      # 在每个路径点停留2秒(毫秒)

# ========== 速度平滑器配置 ==========
# Velocity Smoother负责对控制命令进行平滑处理，提高机器人运动质量
velocity_smoother:
  ros__parameters:
    use_sim_time: false                   # 使用真实时间
    smoothing_frequency: 20.0             # 平滑频率(Hz)
    scale_velocities: false               # 不缩放速度
    feedback: "OPEN_LOOP"                 # 开环反馈模式

    # --- 速度限制配置 ---
    max_velocity: [0.6, 0.0, 1.0]        # 最大速度[x, y, theta]，提高直线速度
    min_velocity: [0.0, 0.0, -1.0]       # 最小速度，禁止倒退
    max_accel: [0.4, 0.0, 0.8]           # 最大加速度[x, y, theta]，保持原值
    max_decel: [-0.4, 0.0, -0.8]         # 最大减速度[x, y, theta]，保持原值

    # --- 其他配置 ---
    odom_topic: "odom"                    # 里程计话题
    odom_duration: 0.1                    # 里程计持续时间(秒)
    deadband_velocity: [0.0, 0.0, 0.0]   # 死区速度
    velocity_timeout: 1.0                 # 速度超时时间(秒)

# ========== 生命周期管理器配置 ==========
# 关键成功要素：正确管理所有Nav2节点，包括独立的代价地图节点
lifecycle_manager:
  ros__parameters:
    use_sim_time: false                   # 使用真实时间
    autostart: true                       # 自动启动所有管理的节点
    # 管理的节点列表 - 按启动顺序排列
    # 关键：包含local_costmap和global_costmap作为独立节点
    node_names: ['controller_server', 'smoother_server', 'planner_server',
                 'behavior_server', 'bt_navigator', 'waypoint_follower',
                 'velocity_smoother', 'local_costmap', 'global_costmap']
