<!--
  This Behavior Tree replans the global path periodically at 1 Hz and it also has
  recovery actions specific to planning / control as well as general system issues.
  This will be continuous if a kinematically valid planner is selected.
-->
<root main_tree_to_execute="MainTree">
  <BehaviorTree ID="MainTree">
    <RecoveryNode number_of_retries="6" name="NavigateRecovery">
      <PipelineSequence name="NavigateWithReplanning">
        <RateController hz="1.0">
          <RecoveryNode number_of_retries="1" name="ComputePathToPose">
            <ComputePathToPose goal="{goal}" path="{path}" planner_id="SmacHybrid"/>
            <ClearEntireCostmap name="ClearGlobalCostmap-Context" service_name="global_costmap/clear_entirely_costmap"/>
          </RecoveryNode>
        </RateController>
        <RecoveryNode number_of_retries="1" name="FollowPath">
          <FollowPath path="{path}" controller_id="FollowPath"/>
          <ClearEntireCostmap name="ClearLocalCostmap-Context" service_name="local_costmap/clear_entirely_costmap"/>
        </RecoveryNode>
      </PipelineSequence>
      <ReactiveFallback name="RecoveryFallback">
        <GoalUpdated/>
        <RoundRobin name="RecoveryActions">
          <Sequence name="SpinRecovery">
            <Spin spin_dist="1.57"/>
            <ClearEntireCostmap name="ClearGlobalCostmap-Spin" service_name="global_costmap/clear_entirely_costmap"/>
            <ClearEntireCostmap name="ClearLocalCostmap-Spin" service_name="local_costmap/clear_entirely_costmap"/>
          </Sequence>
          <Sequence name="BackUpRecovery">
            <BackUp backup_dist="0.3" backup_speed="0.05"/>
            <ClearEntireCostmap name="ClearGlobalCostmap-BackUp" service_name="global_costmap/clear_entirely_costmap"/>
            <ClearEntireCostmap name="ClearLocalCostmap-BackUp" service_name="local_costmap/clear_entirely_costmap"/>
          </Sequence>
          <Wait wait_duration="5.0"/>
        </RoundRobin>
      </ReactiveFallback>
    </RecoveryNode>
  </BehaviorTree>
</root>
