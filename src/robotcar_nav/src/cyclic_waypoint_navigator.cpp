#include <rclcpp/rclcpp.hpp>
#include <rclcpp_action/rclcpp_action.hpp>
#include <nav2_msgs/action/navigate_to_pose.hpp>
#include <geometry_msgs/msg/pose_stamped.hpp>
#include <geometry_msgs/msg/pose_with_covariance_stamped.hpp>
#include <std_msgs/msg/string.hpp>
#include <yaml-cpp/yaml.h>
#include <fstream>
#include <vector>
#include <string>
#include <chrono>
#include <cmath>

using namespace std::chrono_literals;
using NavigateToPose = nav2_msgs::action::NavigateToPose;
using GoalHandleNavigateToPose = rclcpp_action::ClientGoalHandle<NavigateToPose>;

enum class NavigationState {
    IDLE,
    NAVIGATING,
    WAITING,
    COMPLETED,
    FAILED
};

class CyclicWaypointNavigator : public rclcpp::Node
{
public:
    CyclicWaypointNavigator() : Node("cyclic_waypoint_navigator")
    {
        // 声明参数
        this->declare_parameter("waypoints_file", "");
        this->declare_parameter("loop_count", 1);
        this->declare_parameter("auto_start", true);
        this->declare_parameter("return_to_start", true);
        this->declare_parameter("wait_time_at_waypoint", 2.0);
        this->declare_parameter("goal_tolerance", 0.3);
        this->declare_parameter("timeout_per_waypoint", 120.0);

        // 获取参数
        waypoints_file_ = this->get_parameter("waypoints_file").as_string();
        loop_count_ = this->get_parameter("loop_count").as_int();
        auto_start_ = this->get_parameter("auto_start").as_bool();
        return_to_start_ = this->get_parameter("return_to_start").as_bool();
        wait_time_ = this->get_parameter("wait_time_at_waypoint").as_double();
        goal_tolerance_ = this->get_parameter("goal_tolerance").as_double();
        timeout_ = this->get_parameter("timeout_per_waypoint").as_double();

        // 初始化变量
        current_waypoint_index_ = 0;
        current_loop_ = 0;
        state_ = NavigationState::IDLE;

        // 创建Action客户端
        nav_client_ = rclcpp_action::create_client<NavigateToPose>(
            this, "navigate_to_pose");

        // 创建发布器和订阅器
        status_pub_ = this->create_publisher<std_msgs::msg::String>(
            "/waypoint_navigation_status", 10);
        
        initial_pose_sub_ = this->create_subscription<geometry_msgs::msg::PoseWithCovarianceStamped>(
            "/initialpose", 10,
            std::bind(&CyclicWaypointNavigator::initialPoseCallback, this, std::placeholders::_1));

        // 创建定时器
        status_timer_ = this->create_wall_timer(
            1s, std::bind(&CyclicWaypointNavigator::publishStatus, this));

        RCLCPP_INFO(this->get_logger(), "循环多点导航控制器已启动");
        RCLCPP_INFO(this->get_logger(), "路点文件: %s", waypoints_file_.c_str());
        RCLCPP_INFO(this->get_logger(), "循环次数: %d (0=无限循环)", loop_count_);
        RCLCPP_INFO(this->get_logger(), "自动开始: %s", auto_start_ ? "true" : "false");

        // 加载路点
        if (loadWaypoints()) {
            if (auto_start_) {
                RCLCPP_INFO(this->get_logger(), "5秒后自动开始导航...");
                start_timer_ = this->create_wall_timer(
                    5s, std::bind(&CyclicWaypointNavigator::startNavigation, this));
            } else {
                RCLCPP_INFO(this->get_logger(), "等待手动触发导航 (发布到 /start_waypoint_navigation)");
                start_trigger_sub_ = this->create_subscription<std_msgs::msg::String>(
                    "/start_waypoint_navigation", 10,
                    std::bind(&CyclicWaypointNavigator::startTriggerCallback, this, std::placeholders::_1));
            }
        }
    }

private:
    // 成员变量
    std::string waypoints_file_;
    int loop_count_;
    bool auto_start_;
    bool return_to_start_;
    double wait_time_;
    double goal_tolerance_;
    double timeout_;

    std::vector<geometry_msgs::msg::PoseStamped> waypoints_;
    geometry_msgs::msg::PoseStamped start_pose_;
    bool has_start_pose_ = false;
    
    size_t current_waypoint_index_;
    int current_loop_;
    NavigationState state_;

    // ROS2组件
    rclcpp_action::Client<NavigateToPose>::SharedPtr nav_client_;
    rclcpp::Publisher<std_msgs::msg::String>::SharedPtr status_pub_;
    rclcpp::Subscription<geometry_msgs::msg::PoseWithCovarianceStamped>::SharedPtr initial_pose_sub_;
    rclcpp::Subscription<std_msgs::msg::String>::SharedPtr start_trigger_sub_;
    rclcpp::TimerBase::SharedPtr status_timer_;
    rclcpp::TimerBase::SharedPtr start_timer_;
    rclcpp::TimerBase::SharedPtr wait_timer_;

    bool loadWaypoints()
    {
        try {
            if (waypoints_file_.empty()) {
                RCLCPP_ERROR(this->get_logger(), "路点文件路径为空");
                return false;
            }

            YAML::Node config = YAML::LoadFile(waypoints_file_);
            
            // 检查文件格式
            if (config["waypoint_follower_node"]) {
                return loadWaypointFollowerFormat(config);
            } else if (config["waypoints"]) {
                return loadPoseArrayFormat(config);
            } else {
                RCLCPP_ERROR(this->get_logger(), "不支持的路点文件格式");
                return false;
            }
        } catch (const std::exception& e) {
            RCLCPP_ERROR(this->get_logger(), "加载路点文件失败: %s", e.what());
            return false;
        }
    }

    bool loadWaypointFollowerFormat(const YAML::Node& config)
    {
        try {
            auto wp_params = config["waypoint_follower_node"]["ros__parameters"]["waypoints"];
            
            auto names = wp_params["names"].as<std::vector<std::string>>();
            auto xs = wp_params["xs"].as<std::vector<double>>();
            auto ys = wp_params["ys"].as<std::vector<double>>();
            auto thetas = wp_params["thetas"].as<std::vector<double>>();

            if (xs.size() != ys.size() || xs.size() != thetas.size()) {
                RCLCPP_ERROR(this->get_logger(), "路点坐标数组长度不一致");
                return false;
            }

            waypoints_.clear();
            for (size_t i = 0; i < xs.size(); ++i) {
                geometry_msgs::msg::PoseStamped pose;
                pose.header.frame_id = "map";
                pose.header.stamp = this->get_clock()->now();
                
                pose.pose.position.x = xs[i];
                pose.pose.position.y = ys[i];
                pose.pose.position.z = 0.0;
                
                // 从角度转换为四元数
                double theta = thetas[i];
                pose.pose.orientation.x = 0.0;
                pose.pose.orientation.y = 0.0;
                pose.pose.orientation.z = std::sin(theta / 2.0);
                pose.pose.orientation.w = std::cos(theta / 2.0);
                
                waypoints_.push_back(pose);
                
                std::string name = (i < names.size()) ? names[i] : "waypoint_" + std::to_string(i+1);
                RCLCPP_INFO(this->get_logger(), "加载路点 %s: (%.2f, %.2f, %.1f°)", 
                           name.c_str(), xs[i], ys[i], theta * 180.0 / M_PI);
            }

            RCLCPP_INFO(this->get_logger(), "成功加载 %zu 个路点", waypoints_.size());
            return true;
        } catch (const std::exception& e) {
            RCLCPP_ERROR(this->get_logger(), "解析waypoint_follower格式失败: %s", e.what());
            return false;
        }
    }

    bool loadPoseArrayFormat(const YAML::Node& config)
    {
        try {
            auto waypoints_node = config["waypoints"];
            
            waypoints_.clear();
            for (size_t i = 0; i < waypoints_node.size(); ++i) {
                auto wp = waypoints_node[i];
                
                geometry_msgs::msg::PoseStamped pose;
                pose.header.frame_id = "map";
                pose.header.stamp = this->get_clock()->now();
                
                pose.pose.position.x = wp["position"]["x"].as<double>();
                pose.pose.position.y = wp["position"]["y"].as<double>();
                pose.pose.position.z = wp["position"]["z"].as<double>(0.0);
                
                pose.pose.orientation.x = wp["orientation"]["x"].as<double>();
                pose.pose.orientation.y = wp["orientation"]["y"].as<double>();
                pose.pose.orientation.z = wp["orientation"]["z"].as<double>();
                pose.pose.orientation.w = wp["orientation"]["w"].as<double>();
                
                waypoints_.push_back(pose);
                
                RCLCPP_INFO(this->get_logger(), "加载路点 %zu: (%.2f, %.2f)", 
                           i+1, pose.pose.position.x, pose.pose.position.y);
            }

            RCLCPP_INFO(this->get_logger(), "成功加载 %zu 个路点", waypoints_.size());
            return true;
        } catch (const std::exception& e) {
            RCLCPP_ERROR(this->get_logger(), "解析PoseArray格式失败: %s", e.what());
            return false;
        }
    }

    void initialPoseCallback(const geometry_msgs::msg::PoseWithCovarianceStamped::SharedPtr msg)
    {
        if (!has_start_pose_) {
            start_pose_.header = msg->header;
            start_pose_.pose = msg->pose.pose;
            has_start_pose_ = true;
            RCLCPP_INFO(this->get_logger(), "记录起始位置: (%.2f, %.2f)", 
                       msg->pose.pose.position.x, msg->pose.pose.position.y);
        }
    }

    void startTriggerCallback(const std_msgs::msg::String::SharedPtr msg)
    {
        std::string data = msg->data;
        std::transform(data.begin(), data.end(), data.begin(), ::tolower);
        
        if (data == "start" || data == "begin" || data == "go") {
            RCLCPP_INFO(this->get_logger(), "收到手动启动信号");
            startNavigation();
        }
    }

    void startNavigation()
    {
        if (waypoints_.empty()) {
            RCLCPP_ERROR(this->get_logger(), "没有可用的路点");
            return;
        }

        if (!nav_client_->wait_for_action_server(std::chrono::seconds(5))) {
            RCLCPP_ERROR(this->get_logger(), "导航服务器不可用");
            return;
        }

        current_loop_ = 0;
        current_waypoint_index_ = 0;
        state_ = NavigationState::NAVIGATING;

        RCLCPP_INFO(this->get_logger(), "开始循环多点导航!");
        navigateToNextWaypoint();
        
        // 取消启动定时器
        if (start_timer_) {
            start_timer_->cancel();
            start_timer_.reset();
        }
    }

    void navigateToNextWaypoint()
    {
        if (current_waypoint_index_ >= waypoints_.size()) {
            // 完成一轮循环
            current_loop_++;
            RCLCPP_INFO(this->get_logger(), "完成第 %d 轮循环", current_loop_);

            // 检查是否需要继续循环
            if (loop_count_ > 0 && current_loop_ >= loop_count_) {
                completeNavigation();
                return;
            }

            // 返回起始点 (如果启用)
            if (return_to_start_ && has_start_pose_) {
                RCLCPP_INFO(this->get_logger(), "返回起始位置...");
                navigateToPose(start_pose_, true);
                return;
            }

            // 重新开始下一轮循环
            current_waypoint_index_ = 0;
        }

        // 导航到当前路点
        auto current_waypoint = waypoints_[current_waypoint_index_];
        RCLCPP_INFO(this->get_logger(), "导航到路点 %zu/%zu (循环 %d)", 
                   current_waypoint_index_ + 1, waypoints_.size(), current_loop_ + 1);

        navigateToPose(current_waypoint, false);
    }

    void navigateToPose(const geometry_msgs::msg::PoseStamped& pose, bool is_return_to_start)
    {
        auto goal_msg = NavigateToPose::Goal();
        goal_msg.pose = pose;

        state_ = NavigationState::NAVIGATING;

        auto send_goal_options = rclcpp_action::Client<NavigateToPose>::SendGoalOptions();

        send_goal_options.goal_response_callback =
            [this, is_return_to_start](const GoalHandleNavigateToPose::SharedPtr& goal_handle) {
                if (!goal_handle) {
                    RCLCPP_ERROR(this->get_logger(), "导航目标被拒绝");
                    state_ = NavigationState::FAILED;
                    return;
                }
            };

        send_goal_options.result_callback =
            [this, is_return_to_start](const GoalHandleNavigateToPose::WrappedResult& result) {
                navigationResultCallback(result, is_return_to_start);
            };

        nav_client_->async_send_goal(goal_msg, send_goal_options);
    }

    void navigationResultCallback(const GoalHandleNavigateToPose::WrappedResult& result, bool is_return_to_start)
    {
        switch (result.code) {
            case rclcpp_action::ResultCode::SUCCEEDED:
                if (is_return_to_start) {
                    RCLCPP_INFO(this->get_logger(), "成功返回起始位置");
                    current_waypoint_index_ = 0;
                    wait_timer_ = this->create_wall_timer(
                        std::chrono::duration<double>(wait_time_),
                        std::bind(&CyclicWaypointNavigator::navigateToNextWaypoint, this));
                } else {
                    RCLCPP_INFO(this->get_logger(), "成功到达路点 %zu", current_waypoint_index_ + 1);
                    state_ = NavigationState::WAITING;
                    
                    // 在路点停留指定时间
                    wait_timer_ = this->create_wall_timer(
                        std::chrono::duration<double>(wait_time_),
                        std::bind(&CyclicWaypointNavigator::waypointWaitComplete, this));
                }
                break;
            default:
                RCLCPP_ERROR(this->get_logger(), "导航到路点 %zu 失败", current_waypoint_index_ + 1);
                state_ = NavigationState::FAILED;
                break;
        }
    }

    void waypointWaitComplete()
    {
        if (wait_timer_) {
            wait_timer_->cancel();
            wait_timer_.reset();
        }
        
        current_waypoint_index_++;
        navigateToNextWaypoint();
    }

    void completeNavigation()
    {
        state_ = NavigationState::COMPLETED;
        RCLCPP_INFO(this->get_logger(), "循环多点导航完成! 总共完成 %d 轮循环", current_loop_);
    }

    void publishStatus()
    {
        auto status_msg = std_msgs::msg::String();
        
        std::string state_str;
        switch (state_) {
            case NavigationState::IDLE: state_str = "idle"; break;
            case NavigationState::NAVIGATING: state_str = "navigating"; break;
            case NavigationState::WAITING: state_str = "waiting"; break;
            case NavigationState::COMPLETED: state_str = "completed"; break;
            case NavigationState::FAILED: state_str = "failed"; break;
        }
        
        status_msg.data = "state: " + state_str + 
                         ", current_loop: " + std::to_string(current_loop_) +
                         ", total_loops: " + std::to_string(loop_count_) +
                         ", current_waypoint: " + std::to_string(current_waypoint_index_ + 1) +
                         ", total_waypoints: " + std::to_string(waypoints_.size());
        
        status_pub_->publish(status_msg);
    }
};

int main(int argc, char** argv)
{
    rclcpp::init(argc, argv);
    auto node = std::make_shared<CyclicWaypointNavigator>();
    
    try {
        rclcpp::spin(node);
    } catch (const std::exception& e) {
        RCLCPP_ERROR(node->get_logger(), "节点异常: %s", e.what());
    }
    
    rclcpp::shutdown();
    return 0;
}
