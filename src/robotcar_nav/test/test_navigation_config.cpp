#include <gtest/gtest.h>
#include <rclcpp/rclcpp.hpp>
#include <yaml-cpp/yaml.h>
#include <fstream>
#include <filesystem>

class NavigationConfigTest : public ::testing::Test {
protected:
    void SetUp() override {
        rclcpp::init(0, nullptr);
    }
    
    void TearDown() override {
        rclcpp::shutdown();
    }
};

// 测试配置文件是否存在且格式正确
TEST_F(NavigationConfigTest, ConfigFilesExist) {
    std::vector<std::string> config_files = {
        "config/nav2_simple.yaml",
        "config/cartographer_mapping.lua",
        "config/cartographer_localization.lua"
    };
    
    for (const auto& file : config_files) {
        EXPECT_TRUE(std::filesystem::exists(file)) 
            << "Configuration file does not exist: " << file;
    }
}

// 测试YAML配置文件格式
TEST_F(NavigationConfigTest, YamlConfigValid) {
    try {
        YAML::Node config = YAML::LoadFile("config/nav2_simple.yaml");
        
        // 检查关键配置节点
        EXPECT_TRUE(config["controller_server"]) << "controller_server config missing";
        EXPECT_TRUE(config["local_costmap"]) << "local_costmap config missing";
        EXPECT_TRUE(config["global_costmap"]) << "global_costmap config missing";
        EXPECT_TRUE(config["planner_server"]) << "planner_server config missing";
        
        // 检查控制器频率配置
        auto controller = config["controller_server"]["ros__parameters"];
        EXPECT_TRUE(controller["controller_frequency"]) << "controller_frequency missing";
        
        double freq = controller["controller_frequency"].as<double>();
        EXPECT_GT(freq, 0.0) << "controller_frequency must be positive";
        EXPECT_LE(freq, 50.0) << "controller_frequency too high";
        
    } catch (const YAML::Exception& e) {
        FAIL() << "YAML parsing error: " << e.what();
    }
}

// 测试Launch文件存在性
TEST_F(NavigationConfigTest, LaunchFilesExist) {
    std::vector<std::string> launch_files = {
        "launch/nav_01_bringup.launch.py",
        "launch/nav_02_ekf.launch.py",
        "launch/nav_03_mapping.launch.py",
        "launch/nav_04_localization.launch.py",
        "launch/nav_05_navigation.launch.py"
    };
    
    for (const auto& file : launch_files) {
        EXPECT_TRUE(std::filesystem::exists(file)) 
            << "Launch file does not exist: " << file;
    }
}

// 测试地图文件
TEST_F(NavigationConfigTest, MapFilesExist) {
    // 检查是否有地图文件目录
    EXPECT_TRUE(std::filesystem::exists("maps")) << "Maps directory does not exist";
    
    // 检查是否有地图文件（至少一个）
    bool has_map_files = false;
    for (const auto& entry : std::filesystem::directory_iterator("maps")) {
        if (entry.path().extension() == ".yaml" || 
            entry.path().extension() == ".pgm" ||
            entry.path().extension() == ".pbstream") {
            has_map_files = true;
            break;
        }
    }
    EXPECT_TRUE(has_map_files) << "No map files found in maps directory";
}

// 测试RViz配置
TEST_F(NavigationConfigTest, RvizConfigExists) {
    EXPECT_TRUE(std::filesystem::exists("rviz")) << "RViz directory does not exist";
    EXPECT_TRUE(std::filesystem::exists("rviz/nav2.rviz")) << "nav2.rviz config does not exist";
}

int main(int argc, char **argv) {
    ::testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
}
