import os
from ament_index_python.packages import get_package_share_directory
from launch import LaunchDescription
from launch.actions import DeclareLaunchArgument, IncludeLaunchDescription, TimerAction
from launch_ros.actions import Node
from launch.substitutions import LaunchConfiguration, PathJoinSubstitution
from launch.launch_description_sources import PythonLaunchDescriptionSource
from launch.conditions import IfCondition

def generate_launch_description():
    """
    循环多点导航启动文件
    功能: 读取预设路点文件，支持设置循环次数的自动多点导航
    使用方法:
    1. 先在RViz中记录路点到waypoints.yaml文件
    2. 启动此launch文件，设置循环次数
    3. 机器人将自动按顺序访问所有路点，并循环指定次数
    """
    pkg_robotcar_nav = get_package_share_directory('robotcar_nav')

    # 启动参数配置
    use_sim_time = LaunchConfiguration('use_sim_time')
    use_ekf = LaunchConfiguration('use_ekf')
    start_rviz = LaunchConfiguration('start_rviz')
    waypoints_file = LaunchConfiguration('waypoints_file')
    loop_count = LaunchConfiguration('loop_count')
    auto_start = LaunchConfiguration('auto_start')
    namespace = LaunchConfiguration('namespace')
    # 只使用C++导航器

    # 默认路点文件路径
    default_waypoints_file = PathJoinSubstitution([
        pkg_robotcar_nav, 'config', 'waypoints.yaml'
    ])

    return LaunchDescription([
        # ========== 启动参数声明 ==========
        DeclareLaunchArgument(
            'use_sim_time',
            default_value='false',
            description='使用仿真时间(Gazebo)还是真实时间'
        ),
        DeclareLaunchArgument(
            'use_ekf',
            default_value='true',
            description='是否使用EKF进行里程计融合'
        ),
        DeclareLaunchArgument(
            'start_rviz',
            default_value='true',
            description='是否启动RViz进行可视化监控'
        ),
        DeclareLaunchArgument(
            'waypoints_file',
            default_value=default_waypoints_file,
            description='路点文件路径 (YAML格式)'
        ),
        DeclareLaunchArgument(
            'loop_count',
            default_value='4',
            description='循环次数 (0表示无限循环, >0表示循环指定次数)'
        ),
        DeclareLaunchArgument(
            'auto_start',
            default_value='true',
            description='是否自动开始导航 (false需要手动触发)'
        ),

        DeclareLaunchArgument(
            'namespace',
            default_value='chassis',
            description='ROS2 namespace for multi-robot isolation'
        ),
        # Python导航器已移除，只使用C++版本

        # ========== 基础导航系统启动 ==========
        # 1. 启动完整的导航系统 (包含定位、路径规划、控制等)
        IncludeLaunchDescription(
            PythonLaunchDescriptionSource(
                os.path.join(pkg_robotcar_nav, 'launch', 'nav_05_navigation.launch.py')),
            launch_arguments={
                'use_sim_time': use_sim_time,
                'use_ekf': use_ekf,
                'start_rviz': 'false',  # 先不启动RViz，等导航系统就绪后再启动
                'namespace': namespace,
            }.items()
        ),

        # ========== C++循环多点导航控制器 ==========
        # 2a. 启动C++循环多点导航控制节点 (延迟启动，确保导航系统就绪)
        TimerAction(
            period=8.0,  # 等待8秒让导航系统完全启动
            actions=[
                Node(
                    package='robotcar_nav',
                    executable='cyclic_waypoint_navigator',
                    name='cyclic_waypoint_navigator_cpp',
                    namespace=namespace,
                    output='screen',
                    parameters=[{
                        'use_sim_time': use_sim_time,
                        'waypoints_file': waypoints_file,
                        'loop_count': loop_count,
                        'auto_start': auto_start,
                        'return_to_start': True,  # 每次循环后返回起始点
                        'wait_time_at_waypoint': 1.0,  # 在每个路点停留时间(秒)
                        'goal_tolerance': 0.1,  # 到达路点的容差距离(米)
                        'timeout_per_waypoint': 60.0,  # 每个路点的超时时间(秒)
                    }],
                    # 只使用C++导航器，移除条件
                )
            ]
        ),

        # Python导航器已移除，只使用C++版本

        # ========== RViz可视化 ==========
        # 3. 启动RViz进行导航监控 (延迟启动，确保所有系统就绪)
        TimerAction(
            period=5.0,  # 等待10秒让所有系统完全启动
            actions=[
                Node(
                    package='rviz2',
                    executable='rviz2',
                    name='rviz2_multi_point_nav',
                    namespace=namespace,
                    arguments=['-d', PathJoinSubstitution([pkg_robotcar_nav, 'rviz', 'nav2.rviz'])],
                    parameters=[{'use_sim_time': use_sim_time}],
                    condition=IfCondition(start_rviz),
                    output='screen'
                )
            ]
        ),
    ])