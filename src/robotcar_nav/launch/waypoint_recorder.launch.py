#!/usr/bin/env python3
# TODO fix this launch file
import os
from ament_index_python.packages import get_package_share_directory
from launch import LaunchDescription
from launch.actions import DeclareLaunchArgument
from launch_ros.actions import Node
from launch.substitutions import LaunchConfiguration

def generate_launch_description():

    pkg_robotcar_nav = get_package_share_directory('robotcar_nav')

    # 启动参数配置
    output_file = LaunchConfiguration('output_file')
    format_type = LaunchConfiguration('format')
    record_from_initialpose = LaunchConfiguration('record_from_initialpose')
    record_from_goal = LaunchConfiguration('record_from_goal')
    enable_interactive = LaunchConfiguration('enable_interactive')
    frame_id = LaunchConfiguration('frame_id')
    robot_frame = LaunchConfiguration('robot_frame')

    return LaunchDescription([
        # ========== 启动参数声明 ==========
        DeclareLaunchArgument(
            'output_file',
            default_value=os.path.join(pkg_robotcar_nav, 'config', 'waypoints.yaml'),
            description='输出路点文件路径'
        ),
        DeclareLaunchArgument(
            'format',
            default_value='waypoint_follower',
            description='输出格式: waypoint_follower 或 pose_array'
        ),
        DeclareLaunchArgument(
            'record_from_initialpose',
            default_value='true',
            description='是否从/initialpose话题记录路点 (2D Pose Estimate)'
        ),
        DeclareLaunchArgument(
            'record_from_goal',
            default_value='false',
            description='是否从/goal_pose话题记录路点 (2D Nav Goal)'
        ),
        DeclareLaunchArgument(
            'enable_interactive',
            default_value='true',
            description='启用交互式记录 (按Enter键记录当前位置)'
        ),
        DeclareLaunchArgument(
            'frame_id',
            default_value='map',
            description='目标坐标系'
        ),
        DeclareLaunchArgument(
            'robot_frame',
            default_value='base_footprint',
            description='机器人坐标系'
        ),

        # ========== C++路点记录器 ==========
        Node(
            package='robotcar_nav',
            executable='waypoint_recorder_cpp',
            name='waypoint_recorder',
            output='screen',
            parameters=[{
                'output_file': output_file,
                'format': format_type,
                'record_from_initialpose': record_from_initialpose,
                'record_from_goal': record_from_goal,
                'enable_interactive': enable_interactive,
                'frame_id': frame_id,
                'robot_frame': robot_frame,
            }],
            # 只使用C++记录器，移除use_cpp_recorder变量，移除条件
        ),

        # Python路点记录器已移除，只使用C++版本
    ])

# 使用说明:
#
# 1. 启动记录器:
#    ros2 launch robotcar_nav waypoint_recorder.launch.py
#
# 2. 自定义参数启动:
#    ros2 launch robotcar_nav waypoint_recorder.launch.py \
#        output_file:=/path/to/my_waypoints.yaml \
#        format:=pose_array \
#        record_from_goal:=true
#
# 3. 在RViz中记录路点:
#    - 使用"2D Pose Estimate"工具点击位置 (如果record_from_initialpose=true)
#    - 使用"2D Nav Goal"工具点击位置 (如果record_from_goal=true)
#
# 4. 保存路点:
#    ros2 topic pub /save_waypoints std_msgs/String "data: 'save'" --once
#
# 5. 清除路点:
#    ros2 topic pub /clear_waypoints std_msgs/String "data: 'clear'" --once
#
# 6. 查看状态:
#    ros2 topic echo /waypoint_recorder_status
