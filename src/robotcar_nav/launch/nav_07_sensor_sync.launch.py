import os
from ament_index_python.packages import get_package_share_directory
from launch import LaunchDescription
from launch.actions import DeclareLaunchArgument
from launch.substitutions import LaunchConfiguration
from launch_ros.actions import Node

def generate_launch_description():
    """
    传感器同步节点启动文件
    功能: 使用message_filters同步IMU、激光雷达和里程计数据
    适用场景: 需要严格时间同步的高精度应用
    """
    use_sim_time = LaunchConfiguration('use_sim_time', default='false')
    sync_mode = LaunchConfiguration('sync_mode', default='approximate')
    sync_slop = LaunchConfiguration('sync_slop', default='0.1')
    use_filtered_scan = LaunchConfiguration('use_filtered_scan', default='false')
    use_filtered_odom = LaunchConfiguration('use_filtered_odom', default='true')
    imu_topic = LaunchConfiguration('imu_topic', default='/imu')
    scan_topic = LaunchConfiguration('scan_topic', default='/scan')
    odom_topic = LaunchConfiguration('odom_topic', default='/odometry/filtered')

    return LaunchDescription([
        # ========== 启动参数声明 ==========
        DeclareLaunchArgument(
            'use_sim_time',
            default_value='false',
            description='使用仿真时间(Gazebo)还是真实时间'
        ),
        DeclareLaunchArgument(
            'sync_mode',
            default_value='approximate',
            description='同步模式: "exact"(精确) 或 "approximate"(近似)'
        ),
        DeclareLaunchArgument(
            'sync_slop',
            default_value='0.1',
            description='近似同步的时间容差(秒)'
        ),
        DeclareLaunchArgument(
            'use_filtered_scan',
            default_value='false',
            description='是否使用过滤后的激光雷达数据'
        ),
        DeclareLaunchArgument(
            'use_filtered_odom',
            default_value='true',
            description='是否使用EKF融合后的里程计数据'
        ),
        DeclareLaunchArgument(
            'imu_topic',
            default_value='/imu',
            description='IMU数据话题名称'
        ),
        DeclareLaunchArgument(
            'scan_topic',
            default_value='/scan',
            description='激光雷达数据话题名称'
        ),
        DeclareLaunchArgument(
            'odom_topic',
            default_value='/odometry/filtered',
            description='里程计数据话题名称'
        ),

        # ========== 传感器同步节点 ==========
        Node(
            package='robotcar_nav',
            executable='sensor_sync_node',
            name='sensor_sync_node',
            output='screen',
            parameters=[{
                'use_sim_time': use_sim_time,
                'sync_mode': sync_mode,
                'sync_slop': sync_slop,
                'use_filtered_scan': use_filtered_scan,
                'use_filtered_odom': use_filtered_odom,
                'imu_topic': imu_topic,
                'scan_topic': scan_topic,
                'odom_topic': odom_topic,
            }]
        ),
    ])