#!/usr/bin/env python3
"""
地标检测节点启动文件

功能:
1. 启动landmark_detector_node节点
2. 配置反光板检测参数
3. 设置话题重映射
4. 提供可视化支持

适用场景:
- 与Cartographer SLAM协同工作
- 反光板定位约束
- 多地标约束定位

"""

import os
from launch import LaunchDescription
from launch.actions import DeclareLaunchArgument
from launch.substitutions import LaunchConfiguration, PathJoinSubstitution
from launch_ros.actions import Node
from launch_ros.substitutions import FindPackageShare

def generate_launch_description():
    """
    生成地标检测节点的启动描述
    """
    
    # 获取包路径
    pkg_robotcar_nav = FindPackageShare('robotcar_nav')
    
    # 启动参数
    namespace = LaunchConfiguration('namespace')
    laser_topic = LaunchConfiguration('laser_topic')
    laser_frame = LaunchConfiguration('laser_frame')
    
    # 地标检测参数
    intensity_threshold = LaunchConfiguration('intensity_threshold')
    min_cluster_size = LaunchConfiguration('min_cluster_size')
    max_cluster_size = LaunchConfiguration('max_cluster_size')
    cluster_tolerance = LaunchConfiguration('cluster_tolerance')
    
    # 约束参数
    min_landmark_distance = LaunchConfiguration('min_landmark_distance')
    max_landmark_distance = LaunchConfiguration('max_landmark_distance')
    base_translation_weight = LaunchConfiguration('base_translation_weight')
    base_rotation_weight = LaunchConfiguration('base_rotation_weight')
    
    # 发布参数
    publish_rate = LaunchConfiguration('publish_rate')
    enable_visualization = LaunchConfiguration('enable_visualization')
    
    # 地标检测节点
    landmark_detector_node = Node(
        package='robotcar_nav',
        executable='landmark_detector_node',
        name='landmark_detector',
        namespace=namespace,
        output='screen',
        parameters=[{
            # 反光板检测参数
            'intensity_threshold': intensity_threshold,
            'min_cluster_size': min_cluster_size,
            'max_cluster_size': max_cluster_size,
            'cluster_tolerance': cluster_tolerance,
            
            # 地标约束参数
            'min_landmark_distance': min_landmark_distance,
            'max_landmark_distance': max_landmark_distance,
            'base_translation_weight': base_translation_weight,
            'base_rotation_weight': base_rotation_weight,
            
            # 发布参数
            'publish_rate': publish_rate,
            'enable_visualization': enable_visualization,
            'laser_frame': laser_frame,
        }],
        remappings=[
            ('/scan', laser_topic),
            ('/landmarks', '/landmarks'),
            ('/landmark_markers', '/landmark_markers')
        ]
    )
    
    return LaunchDescription([
        # === 启动参数声明 ===
        DeclareLaunchArgument(
            'namespace',
            default_value='',
            description='ROS2 namespace for the landmark detector'
        ),
        
        DeclareLaunchArgument(
            'laser_topic',
            default_value='/scan',
            description='Laser scan topic name'
        ),
        
        DeclareLaunchArgument(
            'laser_frame',
            default_value='laser_frame',
            description='Laser frame ID'
        ),
        
        # === 反光板检测参数 ===
        DeclareLaunchArgument(
            'intensity_threshold',
            default_value='2000.0',
            description='Intensity threshold for reflector detection'
        ),
        
        DeclareLaunchArgument(
            'min_cluster_size',
            default_value='3',
            description='Minimum cluster size for reflector detection'
        ),
        
        DeclareLaunchArgument(
            'max_cluster_size',
            default_value='10',
            description='Maximum cluster size for reflector detection'
        ),
        
        DeclareLaunchArgument(
            'cluster_tolerance',
            default_value='0.1',
            description='Cluster tolerance in meters'
        ),
        
        # === 地标约束参数 ===
        DeclareLaunchArgument(
            'min_landmark_distance',
            default_value='1.0',
            description='Minimum distance between landmarks (meters)'
        ),
        
        DeclareLaunchArgument(
            'max_landmark_distance',
            default_value='10.0',
            description='Maximum distance between landmarks (meters)'
        ),
        
        DeclareLaunchArgument(
            'base_translation_weight',
            default_value='1000.0',
            description='Base translation weight for landmark constraints'
        ),
        
        DeclareLaunchArgument(
            'base_rotation_weight',
            default_value='100.0',
            description='Base rotation weight for landmark constraints'
        ),
        
        # === 发布参数 ===
        DeclareLaunchArgument(
            'publish_rate',
            default_value='10.0',
            description='Landmark publishing rate (Hz)'
        ),
        
        DeclareLaunchArgument(
            'enable_visualization',
            default_value='true',
            description='Enable visualization markers'
        ),
        
        # === 节点启动 ===
        landmark_detector_node,
    ])
