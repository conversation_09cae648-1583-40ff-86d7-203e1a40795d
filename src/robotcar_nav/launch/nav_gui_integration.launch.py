#!/usr/bin/env python3
"""
RobotCar GUI 集成启动文件
专门为新的GUI界面优化的导航系统启动配置
包含所有必要的Nav2组件以支持GUI的完整功能
"""

import os
from ament_index_python.packages import get_package_share_directory
from launch import LaunchDescription
from launch.actions import DeclareLaunchArgument, IncludeLaunchDescription, TimerAction, GroupAction
from launch.substitutions import LaunchConfiguration, PathJoinSubstitution
from launch.launch_description_sources import PythonLaunchDescriptionSource
from launch_ros.actions import Node, PushRosNamespace
from launch.conditions import IfCondition

def generate_launch_description():
    """
    为RobotCar GUI优化的导航系统启动配置
    包含完整的Nav2栈以支持GUI的所有功能
    """
    pkg_robotcar_nav = get_package_share_directory('robotcar_nav')
    pkg_nav2_bringup = get_package_share_directory('nav2_bringup')

    # 启动参数
    use_sim_time = LaunchConfiguration('use_sim_time')
    namespace = LaunchConfiguration('namespace')
    start_rviz = LaunchConfiguration('start_rviz')
    
    # 使用pb控制器的配置文件
    nav2_params_file = PathJoinSubstitution([
        pkg_robotcar_nav, 'config', 'nav2_fusion_pb.yaml'
    ])
    
    # 地图文件
    map_file = LaunchConfiguration('map_file')
    
    # RViz配置文件
    rviz_config_file = PathJoinSubstitution([
        pkg_robotcar_nav, 'rviz', 'nav2_gui.rviz'
    ])

    return LaunchDescription([
        # 声明启动参数
        DeclareLaunchArgument(
            'use_sim_time',
            default_value='false',
            description='Use simulation (Gazebo) clock if true'
        ),
        DeclareLaunchArgument(
            'namespace',
            default_value='',
            description='Robot namespace'
        ),
        DeclareLaunchArgument(
            'start_rviz',
            default_value='false',
            description='Whether to start RViz'
        ),
        DeclareLaunchArgument(
            'map_file',
            default_value=PathJoinSubstitution([
                pkg_robotcar_nav, 'maps', 'map_0624.yaml'
            ]),
            description='Path to the map file'
        ),

        # 使用GroupAction和PushRosNamespace来管理命名空间
        GroupAction([
            PushRosNamespace(namespace),
            
            # 1. 启动地图服务器
            Node(
                package='nav2_map_server',
                executable='map_server',
                name='map_server',
                output='screen',
                parameters=[{
                    'use_sim_time': use_sim_time,
                    'yaml_filename': map_file
                }]
            ),

            # 2. 启动AMCL定位
            Node(
                package='nav2_amcl',
                executable='amcl',
                name='amcl',
                output='screen',
                parameters=[nav2_params_file]
            ),

            # 3. 启动Nav2核心组件
            # 行为树导航器
            Node(
                package='nav2_bt_navigator',
                executable='bt_navigator',
                name='bt_navigator',
                output='screen',
                parameters=[nav2_params_file]
            ),

            # 路径规划器
            Node(
                package='nav2_planner',
                executable='planner_server',
                name='planner_server',
                output='screen',
                parameters=[nav2_params_file]
            ),

            # 控制器服务器（使用pb控制器）
            Node(
                package='nav2_controller',
                executable='controller_server',
                name='controller_server',
                output='screen',
                parameters=[nav2_params_file]
            ),

            # 恢复行为服务器
            Node(
                package='nav2_behaviors',
                executable='behavior_server',
                name='behavior_server',
                output='screen',
                parameters=[nav2_params_file]
            ),

            # 路径点跟随器（支持多路径点导航）
            Node(
                package='nav2_waypoint_follower',
                executable='waypoint_follower',
                name='waypoint_follower',
                output='screen',
                parameters=[nav2_params_file]
            ),

            # 速度平滑器
            Node(
                package='nav2_velocity_smoother',
                executable='velocity_smoother',
                name='velocity_smoother',
                output='screen',
                parameters=[nav2_params_file]
            ),

            # 生命周期管理器
            Node(
                package='nav2_lifecycle_manager',
                executable='lifecycle_manager',
                name='lifecycle_manager_navigation',
                output='screen',
                parameters=[{
                    'use_sim_time': use_sim_time,
                    'autostart': True,
                    'node_names': [
                        'map_server',
                        'amcl',
                        'bt_navigator',
                        'planner_server',
                        'controller_server',
                        'behavior_server',
                        'waypoint_follower',
                        'velocity_smoother'
                    ]
                }]
            ),
        ]),

        # 4. 可选启动RViz（延迟启动确保所有组件就绪）
        TimerAction(
            period=3.0,
            actions=[
                Node(
                    package='rviz2',
                    executable='rviz2',
                    name='rviz2_navigation',
                    arguments=['-d', rviz_config_file],
                    parameters=[{'use_sim_time': use_sim_time}],
                    condition=IfCondition(start_rviz),
                    output='screen'
                )
            ]
        ),

        # 5. 启动RobotCar GUI
        TimerAction(
            period=5.0,  # 等待5秒确保所有导航组件启动完成
            actions=[
                Node(
                    package='robotcar_gui',
                    executable='robotcar_gui',
                    name='robotcar_gui',
                    output='screen',
                    parameters=[{
                        'use_sim_time': use_sim_time,
                        'namespace': namespace
                    }]
                )
            ]
        ),
    ])

if __name__ == '__main__':
    generate_launch_description()
