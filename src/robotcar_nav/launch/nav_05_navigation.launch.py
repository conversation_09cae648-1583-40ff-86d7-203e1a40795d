import os
from ament_index_python.packages import get_package_share_directory
from launch import LaunchDescription
from launch.actions import DeclareLaunchArgument, IncludeLaunchDescription, TimerAction
from launch.substitutions import LaunchConfiguration, PathJoinSubstitution
from launch.launch_description_sources import PythonLaunchDescriptionSource
from launch_ros.actions import Node
from launch.conditions import IfCondition

def generate_launch_description():
    """
    Launch file that builds on top of nav_04_localization to run the full Nav2 stack.
    Uses the adjusted nav1234 configurations and 0624 map for navigation.
    """
    pkg_robotcar_nav = get_package_share_directory('robotcar_nav')


    use_sim_time = LaunchConfiguration('use_sim_time')
    use_ekf = LaunchConfiguration('use_ekf')
    start_rviz = LaunchConfiguration('start_rviz')
    namespace = LaunchConfiguration('namespace')

    # Nav2 configuration file -
    nav2_params_file_path = PathJoinSubstitution(
        [pkg_robotcar_nav, 'config', 'nav2_simple.yaml']
    )
    nav2_params_file = LaunchConfiguration('nav2_params_file')

    # Nav2 uses YAML format map for navigation
    navigation_map_file = LaunchConfiguration('navigation_map_file')

    # Cartographer requires a .pbstream file - 使用0624地图
    cartographer_map_file = LaunchConfiguration('cartographer_map_file')

    # RViz配置文件 - 使用相对路径避免硬编码
    rviz_config_file = PathJoinSubstitution([pkg_robotcar_nav, 'rviz', 'nav2.rviz'])

    # Cartographer配置
    cartographer_config_dir = PathJoinSubstitution([pkg_robotcar_nav, 'config'])
    cartographer_config_basename = 'robotcar_localization.lua'
    
    return LaunchDescription([
        # 声明启动参数
        DeclareLaunchArgument(
            'use_sim_time',
            default_value='false',
            description='Use simulation (Gazebo) clock if true'
        ),
        DeclareLaunchArgument(
            'use_ekf',
            default_value='true',  # 默认启用EKF
            description='Set to "true" to use EKF for odometry fusion.'
        ),
        DeclareLaunchArgument(
            'start_rviz',
            default_value='false',
            description='Whether to start RViz with navigation configuration'
        ),
        DeclareLaunchArgument(
            'navigation_map_file',
            default_value=PathJoinSubstitution([pkg_robotcar_nav, 'maps', '0703.yaml']),
            description='Full path to the YAML map file for Nav2 navigation'
        ),

        DeclareLaunchArgument(
            'nav2_params_file',
            default_value=nav2_params_file_path,  # 简化：默认使用标准nav2配置
            description='Full path to the Nav2 parameters file'
        ),
        DeclareLaunchArgument(
            'cartographer_map_file',
            default_value=PathJoinSubstitution([pkg_robotcar_nav, 'maps', '0703.pbstream']),
            description='Full path to the .pbstream map file to load for Cartographer localization'
        ),

        DeclareLaunchArgument(
            'namespace',
            default_value='chassis',
            description='ROS2 namespace for multi-robot isolation'
        ),

        # 1. 启动Cartographer定位（假设robotcar_base已由fusion_01启动）
        # 注意：不再启动nav_04，避免重复启动robotcar_base
        Node(
            package='cartographer_ros',
            executable='cartographer_node',
            name='cartographer_node',
            namespace=namespace,
            output='screen',
            parameters=[{'use_sim_time': use_sim_time}],
            arguments=[
                '-configuration_directory', cartographer_config_dir,
                '-configuration_basename', cartographer_config_basename,
                '-load_state_filename', cartographer_map_file
            ],
            remappings=[
                ('scan', 'scan'),
                ('imu', 'imu'),
                ('odom', 'odom'),
            ]
        ),

        Node(
            package='cartographer_ros',
            executable='cartographer_occupancy_grid_node',
            name='cartographer_occupancy_grid_node',
            namespace=namespace,
            output='screen',
            parameters=[{'use_sim_time': use_sim_time}],
            arguments=['-resolution', '0.03', '-publish_period_sec', '1.0']
        ),

        # 2. Launch Nav2 navigation using source code (without AMCL, without map_server)
        IncludeLaunchDescription(
            PythonLaunchDescriptionSource(
                '/home/<USER>/test_ws/src/navigation/nav2_bringup/launch/navigation_launch.py'),
            launch_arguments={
                'use_sim_time': use_sim_time,
                'params_file': nav2_params_file,
                'autostart': 'true',
                'namespace': namespace,
            }.items()),

        # 3. 注意：移除了topic_tools依赖，因为系统中没有安装
        # 如果需要话题重映射，可以在Nav2配置中直接配置或使用其他方法

        # 4. Launch RViz with localization configuration (延迟启动，确保所有系统就绪)
        TimerAction(
            period=3.0,  # 等待3秒让定位系统完全启动
            actions=[
                Node(
                    package='rviz2',
                    executable='rviz2',
                    name='rviz2_localization',
                    namespace=namespace,
                    arguments=['-d', rviz_config_file],
                    parameters=[{'use_sim_time': use_sim_time}],
                    condition=IfCondition(start_rviz),
                    output='screen'
                )
            ]
        ),
    ])