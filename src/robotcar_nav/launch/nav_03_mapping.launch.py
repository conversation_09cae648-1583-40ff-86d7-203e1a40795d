#!/usr/bin/env python3

import os
from ament_index_python.packages import get_package_share_directory
from launch import LaunchDescription
from launch.actions import DeclareLaunchArgument, IncludeLaunchDescription, TimerAction
from launch.launch_description_sources import PythonLaunchDescriptionSource
from launch.substitutions import LaunchConfiguration, PathJoinSubstitution
from launch_ros.actions import Node
from launch.conditions import IfCondition


def generate_launch_description():
    """
    Launch file that:
    1. First starts hardware and lidar
    2. Starts nav02 EKF for sensor fusion 
    3. Then starts Cartographer SLAM for mapping

    This provides a complete mapping solution with EKF fusion and proper startup sequence.
    """

    # Get package directories
    pkg_robotcar_nav = get_package_share_directory('robotcar_nav')

    # Launch configuration
    use_sim_time = LaunchConfiguration('use_sim_time', default='false')
    start_rviz = LaunchConfiguration('start_rviz', default='true')
    use_ekf = LaunchConfiguration('use_ekf', default='true')
    namespace = LaunchConfiguration('namespace', default='chassis')
    
    # Cartographer configuration
    cartographer_config_dir = PathJoinSubstitution([pkg_robotcar_nav, 'config'])
    cartographer_config_basename = 'cartographer_mapping.lua'
    rviz_config_file = PathJoinSubstitution([pkg_robotcar_nav, 'config', 'rviz.rviz'])
    
    # 1. Launch hardware and lidar only (without RViz from nav1)
    # We'll use our own RViz with mapping configuration
    robotcar_bringup_launch = IncludeLaunchDescription(
        PythonLaunchDescriptionSource(
            os.path.join(get_package_share_directory('robotcar_base'), 'launch', 'bringup.launch.py')
        ),
        launch_arguments={
            'use_sim_time': use_sim_time,
            'namespace': namespace,
        }.items()
    )

    # Launch ORadar LiDAR only (without RViz)
    oradar_scan_launch = IncludeLaunchDescription(
        PythonLaunchDescriptionSource(
            os.path.join(get_package_share_directory('oradar_ros'), 'launch', 'ms500_scan.launch.py')
        ),
        launch_arguments={
            'namespace': namespace,
        }.items()
    )

    # Launch nav02 EKF for sensor fusion (即插即用的融合节点)
    nav02_ekf_launch = IncludeLaunchDescription(
        PythonLaunchDescriptionSource(
            os.path.join(pkg_robotcar_nav, 'launch', 'nav_02_ekf.launch.py')
        ),
        launch_arguments={
            'use_sim_time': use_sim_time,
            'namespace': namespace,
        }.items(),
        condition=IfCondition(use_ekf)
    )
    
    # 2. Cartographer SLAM node (delayed start to ensure hardware is ready)
    cartographer_node = TimerAction(
        period=1.0,  # Wait 1 seconds for hardware to initialize
        actions=[
            Node(
                package='cartographer_ros',
                executable='cartographer_node',
                name='cartographer_node',
                namespace=namespace,
                output='screen',
                parameters=[{'use_sim_time': use_sim_time}],
                arguments=[
                    '-configuration_directory', cartographer_config_dir,
                    '-configuration_basename', cartographer_config_basename
                ],
                remappings=[
                    ('scan', 'scan'),
                    ('imu', 'imu'),
                    ('odom', 'odom'),  # 使用EKF融合后的里程计
                ]
            )
        ]
    )
    
    # 3. Cartographer occupancy grid node (delayed start)
    occupancy_grid_node = TimerAction(
        period=2.0,  # Wait 2 seconds
        actions=[
            Node(
                package='cartographer_ros',
                executable='cartographer_occupancy_grid_node',
                name='cartographer_occupancy_grid_node',
                namespace=namespace,
                output='screen',
                parameters=[{'use_sim_time': use_sim_time}],
                arguments=['-resolution', '0.03', '-publish_period_sec', '1.0']
            )
        ]
    )

    # 5. RViz with mapping configuration (delayed start, only if this is top-level launch)
    mapping_rviz_node = TimerAction(
        period=2.0,  # Wait 2 seconds
        actions=[
            Node(
                package='rviz2',
                executable='rviz2',
                name='rviz2_mapping',
                namespace=namespace,
                arguments=['-d', rviz_config_file],
                parameters=[{'use_sim_time': use_sim_time}],
                condition=IfCondition(start_rviz),
                output='screen'
            )
        ]
    )
    
    return LaunchDescription([
        # Declare launch arguments
        DeclareLaunchArgument(
            'use_sim_time',
            default_value='false',
            description='Use simulation (Gazebo) clock if true'
        ),
        
        DeclareLaunchArgument(
            'start_rviz',
            default_value='true',
            description='Whether to start RViz with mapping configuration'
        ),

        DeclareLaunchArgument(
            'use_ekf',
            default_value='true',
            description='Whether to use EKF for sensor fusion (recommended for mapping)'
        ),

        DeclareLaunchArgument(
            'namespace',
            default_value='chassis',
            description='ROS2 namespace for multi-robot isolation'
        ),
        
        # Launch sequence
        robotcar_bringup_launch,     # Start hardware immediately (includes robot_state_publisher for TF)
        oradar_scan_launch,          # Start lidar immediately
        nav02_ekf_launch,            # Start EKF fusion immediately (即插即用)
        cartographer_node,           # Start SLAM after 1 seconds
        occupancy_grid_node,         # Start occupancy grid after 2 seconds
        mapping_rviz_node,           # Start mapping RViz after 2 seconds
    ])
