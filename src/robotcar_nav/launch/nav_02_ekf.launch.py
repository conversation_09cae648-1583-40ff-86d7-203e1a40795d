import os
from ament_index_python.packages import get_package_share_directory
from launch import LaunchDescription
from launch.actions import DeclareLaunchArgument
from launch.substitutions import LaunchConfiguration
from launch_ros.actions import Node

def generate_launch_description():
    """
    Launch file for starting the EKF-based odometry system.
    It launches the EKF filter node, which fuses odometry from the diff_drive_controller
    and IMU data. The EKF is responsible for publishing the odom->base_footprint transform.
    """
    pkg_robotcar_nav = get_package_share_directory('robotcar_nav')
    use_sim_time = LaunchConfiguration('use_sim_time', default='false')
    namespace = LaunchConfiguration('namespace', default='chassis')
    ekf_config_path = os.path.join(pkg_robotcar_nav, 'config', 'ekf.yaml')

    return LaunchDescription([
        DeclareLaunchArgument(
            'use_sim_time',
            default_value='false',
            description='Use simulation (Gazebo) clock if true'
        ),

        DeclareLaunchArgument(
            'namespace',
            default_value='chassis',
            description='ROS2 namespace for multi-robot isolation'
        ),

        # Launch the EKF node to fuse odometry and IMU
        Node(
            package='robot_localization',
            executable='ekf_node',
            name='ekf_filter_node',
            namespace=namespace,
            output='screen',
            parameters=[ekf_config_path, {'use_sim_time': use_sim_time}],
            remappings=[
                ('odometry/filtered', 'odom')
            ]
        ),
    ])