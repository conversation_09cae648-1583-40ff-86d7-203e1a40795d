#!/usr/bin/env python3
from launch import LaunchDescription
from launch_ros.actions import Node
from launch.substitutions import LaunchConfiguration
from launch.actions import DeclareLaunchArgument

def generate_launch_description():
  # 启动参数
  use_sim_time = LaunchConfiguration('use_sim_time')
  laser_1_ip = LaunchConfiguration('laser_1_ip')
  laser_2_ip = LaunchConfiguration('laser_2_ip')
  namespace = LaunchConfiguration('namespace')
  # 第一个雷达节点 - 右前方雷达
  ordlidar_node_1 = Node(
      package='oradar_ros',
      executable='oradar_scan',
      name='MS500_front_right',
      namespace=namespace,
      output='screen',
      parameters=[
        {'device_model': 'MS500'},
        {'frame_id': 'front_right_laser'},               # {{ AURA-X: Fix - 使用标准frame名称. }}
        {'scan_topic': 'lidar1/scan'},  # 使用相对路径，命名空间会自动添加
        {'lidar_ip': laser_1_ip},  # 使用启动参数
        {'lidar_port': 2007},
        {'angle_min': -135.0},
        {'angle_max': 135.0},
        {'range_min': 0.05},
        {'range_max': 20.0},
        {'inverted': False},
        {'motor_speed': 25},
        {'filter_size': 1},
        {'motor_dir': 0},
        {'use_sim_time': use_sim_time}
      ]
  )

  # 第二个雷达节点 - 左后方雷达
  ordlidar_node_2 = Node(
      package='oradar_ros',
      executable='oradar_scan',
      name='MS500_rear_left',
      namespace=namespace,
      output='screen',
      parameters=[
        {'device_model': 'MS500'},
        {'frame_id': 'rear_left_laser'},                # {{ AURA-X: Fix - 使用标准frame名称. }}
        {'scan_topic': 'lidar2/scan'},  # 使用相对路径，命名空间会自动添加
        {'lidar_ip': laser_2_ip},  # 使用启动参数
        {'lidar_port': 2007},
        {'angle_min': -135.0},
        {'angle_max': 135.0},
        {'range_min': 0.05},
        {'range_max': 20.0},
        {'inverted': False},
        {'motor_speed': 25},
        {'filter_size': 1},
        {'motor_dir': 0},
        {'use_sim_time': use_sim_time}
      ]
  )

  # 校准参数现在通过dual_laser_merger的内置参数系统处理
  # 不需要额外的static_transform_publisher

  return LaunchDescription([
    # === 启动参数声明 ===
    DeclareLaunchArgument(
        'use_sim_time',
        default_value='false',
        description='Use simulation time if true'
    ),

    DeclareLaunchArgument(
        'laser_1_ip',
        default_value='*************',
        description='IP address of first laser scanner'
    ),

    DeclareLaunchArgument(
        'laser_2_ip',
        default_value='*************',
        description='IP address of second laser scanner'
    ),

    DeclareLaunchArgument(
        'namespace',
        default_value='',
        description='ROS2 namespace for multi-robot isolation'
    ),

    # === 双激光雷达节点 ===
    ordlidar_node_1,
    ordlidar_node_2,
  ])
