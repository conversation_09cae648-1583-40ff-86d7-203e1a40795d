#!/usr/bin/env python3
import os
from ament_index_python.packages import get_package_share_directory
from launch import LaunchDescription
from launch_ros.actions import Node
from launch.actions import IncludeLaunchDescription
from launch.launch_description_sources import PythonLaunchDescriptionSource

def generate_launch_description():
    # 获取配置文件路径
    merger_config = os.path.join(
        get_package_share_directory('ros2_laser_scan_merger'),
        'config',
        'dual_oradar_params.yaml'
    )
    
    # RViZ2配置文件
    rviz2_config = os.path.join(
        get_package_share_directory('oradar_ros'),
        'rviz2',
        'oradar_scan.rviz'
    )

    # 第一个雷达节点 - 右前方雷达
    ordlidar_node_1 = Node(
        package='oradar_ros',
        executable='oradar_scan',
        name='MS500_front_right',
        output='screen',
        parameters=[
            {'device_model': 'MS500'},
            {'frame_id': 'laser_front'},
            {'scan_topic': '/lidar_1/scan'},
            {'lidar_ip': '*************'},  # 第一个雷达的IP
            {'lidar_port': 2007},
            {'angle_min': -135.0},
            {'angle_max': 135.0},
            {'range_min': 0.05},
            {'range_max': 20.0},
            {'inverted': False},
            {'motor_speed': 25},
            {'filter_size': 1},
            {'motor_dir': 0}
        ]
    )

    # 第二个雷达节点 - 左后方雷达
    ordlidar_node_2 = Node(
        package='oradar_ros',
        executable='oradar_scan',
        name='MS500_rear_left',
        output='screen',
        parameters=[
            {'device_model': 'MS500'},
            {'frame_id': 'laser_rear'},
            {'scan_topic': '/lidar_2/scan'},
            {'lidar_ip': '*************'},  # 第二个雷达的IP
            {'lidar_port': 2007},
            {'angle_min': -135.0},
            {'angle_max': 135.0},
            {'range_min': 0.05},
            {'range_max': 20.0},
            {'inverted': False},
            {'motor_speed': 25},
            {'filter_size': 1},
            {'motor_dir': 0}
        ]
    )

    # base_link到右前方雷达的TF变换
    base_link_to_laser_front_tf = Node(
        package='tf2_ros',
        executable='static_transform_publisher',
        name='base_link_to_laser_front',
        arguments=['--x', '0.326', '--y', '-0.263', '--z', '0.215',  # 右上角位置
               '--roll', '0', '--pitch', '0', '--yaw', '-0.7783981',  # -45度角
                '--frame-id', 'base_link', '--child-frame-id', 'laser_front']
    )

    # base_link到左后方雷达的TF变换
    base_link_to_laser_rear_tf = Node(
        package='tf2_ros',
        executable='static_transform_publisher',
        name='base_link_to_laser_rear',
        arguments=['--x', '-0.326', '--y', '0.263', '--z', '0.215',  # 左下角位置
               '--roll', '0', '--pitch', '0', '--yaw', '2.3611944',  # 135度角
                '--frame-id', 'base_link', '--child-frame-id', 'laser_rear']
    )
        
#         # base_link到右上角雷达的TF变换
#     base_link_to_laser_front_tf = Node(
#     package='tf2_ros',
#     executable='static_transform_publisher',
#     name='base_link_to_laser_front',
#     namespace=namespace,
#     arguments=['--x', '0.32', '--y', '-0.225', '--z', '0.255',  # 右上角位置
#                '--roll', '0', '--pitch', '0', '--yaw', '-0.7783981',  # -45度角
#                '--frame-id', 'chassis/base_link', '--child-frame-id', 'chassis/laser_front']
#   )

#   # base_link到左下角雷达的TF变换
#     base_link_to_laser_rear_tf = Node(
#     package='tf2_ros',
#     executable='static_transform_publisher',
#     name='base_link_to_laser_rear',
#     namespace=namespace,
#     arguments=['--x', '-0.318', '--y', '0.276', '--z', '0.255',  # 左下角位置
#                '--roll', '0', '--pitch', '0', '--yaw', '2.3611944',  # 135度角
#                '--frame-id', 'chassis/base_link', '--child-frame-id', 'chassis/laser_rear']
#   )

    # 激光雷达合并节点 - 按照官方推荐方式
    laser_merger_node = Node(
        package='ros2_laser_scan_merger',
        executable='ros2_laser_scan_merger',
        parameters=[merger_config],
        output='screen',
        respawn=True,
        respawn_delay=2,
    )

    # 点云转激光扫描节点 - 按照官方推荐方式
    pointcloud_to_laserscan_node = Node(
        name='pointcloud_to_laserscan',
        package='pointcloud_to_laserscan',
        executable='pointcloud_to_laserscan_node',
        parameters=[merger_config],
        output='screen'
    )

    # RViZ2可视化节点
    rviz2_node = Node(
        package='rviz2',
        executable='rviz2',
        name='rviz2_show',
        arguments=['-d', rviz2_config],
        output='screen'
    )

    # Define LaunchDescription variable
    ord = LaunchDescription()

    # 添加所有节点
    ord.add_action(ordlidar_node_1)
    ord.add_action(ordlidar_node_2)
    ord.add_action(base_link_to_laser_front_tf)
    ord.add_action(base_link_to_laser_rear_tf)
    ord.add_action(laser_merger_node)
    ord.add_action(pointcloud_to_laserscan_node)
    ord.add_action(rviz2_node)

    return ord
