<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Atomics</title>
<link rel="stylesheet" href="../../../boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.75.2">
<link rel="home" href="../../../index.html" title="Asio">
<link rel="up" href="../cpp2011.html" title="C++ 2011 Support">
<link rel="prev" href="array.html" title="Array Container">
<link rel="next" href="shared_ptr.html" title="Shared Pointers">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr><td valign="top"><img alt="asio C++ library" width="250" height="60" src="../../../asio.png"></td></tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="array.html"><img src="../../../prev.png" alt="Prev"></a><a accesskey="u" href="../cpp2011.html"><img src="../../../up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../home.png" alt="Home"></a><a accesskey="n" href="shared_ptr.html"><img src="../../../next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="asio.overview.cpp2011.atomic"></a><a class="link" href="atomic.html" title="Atomics">Atomics</a>
</h4></div></div></div>
<p>
          Asio's implementation can use <code class="computeroutput">std::atomic&lt;&gt;</code> in preference
          to <code class="computeroutput">boost::detail::atomic_count</code>.
        </p>
<p>
          Support for the standard atomic integer template is automatically enabled
          for <code class="literal">g++</code> 4.5 and later, when the <code class="literal">-std=c++0x</code>
          or <code class="literal">-std=gnu++0x</code> compiler options are used. It may be
          disabled by defining <code class="computeroutput">ASIO_DISABLE_STD_ATOMIC</code>, or explicitly
          enabled for other compilers by defining <code class="computeroutput">ASIO_HAS_STD_ATOMIC</code>.
        </p>
</div>
<table xmlns:rev="http://www.cs.rpi.edu/~gregod/boost/tools/doc/revision" width="100%"><tr>
<td align="left"></td>
<td align="right"><div class="copyright-footer">Copyright © 2003-2021 Christopher
      M. Kohlhoff<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="array.html"><img src="../../../prev.png" alt="Prev"></a><a accesskey="u" href="../cpp2011.html"><img src="../../../up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../home.png" alt="Home"></a><a accesskey="n" href="shared_ptr.html"><img src="../../../next.png" alt="Next"></a>
</div>
</body>
</html>
