<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Handler Tracking</title>
<link rel="stylesheet" href="../../../boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.75.2">
<link rel="home" href="../../../index.html" title="Asio">
<link rel="up" href="../core.html" title="Core Concepts and Functionality">
<link rel="prev" href="allocation.html" title="Custom Memory Allocation">
<link rel="next" href="concurrency_hint.html" title="Concurrency Hints">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr><td valign="top"><img alt="asio C++ library" width="250" height="60" src="../../../asio.png"></td></tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="allocation.html"><img src="../../../prev.png" alt="Prev"></a><a accesskey="u" href="../core.html"><img src="../../../up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../home.png" alt="Home"></a><a accesskey="n" href="concurrency_hint.html"><img src="../../../next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="asio.overview.core.handler_tracking"></a><a class="link" href="handler_tracking.html" title="Handler Tracking">Handler Tracking</a>
</h4></div></div></div>
<p>
          To aid in debugging asynchronous programs, Asio provides support for handler
          tracking. When enabled by defining <code class="computeroutput"><span class="identifier">ASIO_ENABLE_HANDLER_TRACKING</span></code>,
          Asio writes debugging output to the standard error stream. The output records
          asynchronous operations and the relationships between their handlers.
        </p>
<p>
          This feature is useful when debugging and you need to know how your asynchronous
          operations are chained together, or what the pending asynchronous operations
          are. As an illustration, here is the output when you run the HTTP Server
          example, handle a single request, then shut down via Ctrl+C:
        </p>
<pre class="programlisting">@asio|1589424178.741850|0*1|signal_set@0x7ffee977d878.async_wait
@asio|1589424178.742593|0*2|socket@0x7ffee977d8a8.async_accept
@asio|1589424178.742619|.2|non_blocking_accept,ec=asio.system:11
@asio|1589424178.742625|0|<EMAIL>
@asio|1589424195.830382|.2|non_blocking_accept,ec=system:0
@asio|1589424195.830413|&gt;2|ec=system:0
@asio|1589424195.830473|2*3|socket@0x7fa71d808230.async_receive
@asio|1589424195.830496|.3|non_blocking_recv,ec=system:0,bytes_transferred=151
@asio|1589424195.830503|2*4|socket@0x7ffee977d8a8.async_accept
@asio|1589424195.830507|.4|non_blocking_accept,ec=asio.system:11
@asio|1589424195.830510|&lt;2|
@asio|1589424195.830529|&gt;3|ec=system:0,bytes_transferred=151
@asio|1589424195.831143|3^5|in 'async_write' (./../../../include/asio/impl/write.hpp:330)
@asio|1589424195.831143|3*5|socket@0x7fa71d808230.async_send
@asio|1589424195.831186|.5|non_blocking_send,ec=system:0,bytes_transferred=1090
@asio|1589424195.831194|&lt;3|
@asio|1589424195.831218|&gt;5|ec=system:0,bytes_transferred=1090
@asio|1589424195.831263|5|<EMAIL>
@asio|1589424195.831298|&lt;5|
@asio|1589424199.793770|&gt;1|ec=system:0,signal_number=2
@asio|1589424199.793781|1|<EMAIL>
@asio|1589424199.793809|&lt;1|
@asio|1589424199.793840|&gt;4|ec=asio.system:125
@asio|1589424199.793854|&lt;4|
@asio|1589424199.793883|0|<EMAIL>
</pre>
<p>
          Each line is of the form:
        </p>
<pre class="programlisting">&lt;tag&gt;|&lt;timestamp&gt;|&lt;action&gt;|&lt;description&gt;
</pre>
<p>
          The <code class="computeroutput">&lt;tag&gt;</code> is always <code class="computeroutput">@asio</code>, and is used
          to identify and extract the handler tracking messages from the program
          output.
        </p>
<p>
          The <code class="computeroutput">&lt;timestamp&gt;</code> is seconds and microseconds from 1 Jan
          1970 UTC.
        </p>
<p>
          The <code class="computeroutput">&lt;action&gt;</code> takes one of the following forms:
        </p>
<div class="variablelist">
<p class="title"><b></b></p>
<dl>
<dt><span class="term">&gt;n</span></dt>
<dd><p>
                The program entered the handler number <code class="computeroutput">n</code>. The <code class="computeroutput">&lt;description&gt;</code>
                shows the arguments to the handler.
              </p></dd>
<dt><span class="term">&lt;n</span></dt>
<dd><p>
                The program left handler number <code class="computeroutput">n</code>.
              </p></dd>
<dt><span class="term">!n</span></dt>
<dd><p>
                The program left handler number n due to an exception.
              </p></dd>
<dt><span class="term">~n</span></dt>
<dd><p>
                The handler number <code class="computeroutput">n</code> was destroyed without having been
                invoked. This is usually the case for any unfinished asynchronous
                operations when the <code class="computeroutput">io_context</code> is destroyed.
              </p></dd>
<dt><span class="term">n^m</span></dt>
<dd><p>
                The handler number <code class="computeroutput">n</code> is about to create a new asynchronous
                operation with completion handler number <code class="computeroutput">m</code>. The <code class="computeroutput">&lt;description&gt;</code>
                contains source location information to help identify where in the
                program the asynchronous operation is being started.
              </p></dd>
<dt><span class="term">n*m</span></dt>
<dd><p>
                The handler number <code class="computeroutput">n</code> created a new asynchronous operation
                with completion handler number <code class="computeroutput">m</code>. The <code class="computeroutput">&lt;description&gt;</code>
                shows what asynchronous operation was started.
              </p></dd>
<dt><span class="term">n</span></dt>
<dd><p>
                The handler number <code class="computeroutput">n</code> performed some other operation.
                The <code class="computeroutput">&lt;description&gt;</code> shows what function was called.
                Currently only <code class="computeroutput">close()</code> and <code class="computeroutput">cancel()</code> operations
                are logged, as these may affect the state of pending asynchronous
                operations.
              </p></dd>
<dt><span class="term">.n</span></dt>
<dd><p>
                The implementation performed a system call as part of the asynchronous
                operation for which handler number <code class="computeroutput">n</code> is the completion
                handler. The <code class="computeroutput">&lt;description&gt;</code> shows what function
                was called and its results. These tracking events are only emitted
                when using a reactor-based implementation.
              </p></dd>
</dl>
</div>
<p>
          Where the <code class="computeroutput">&lt;description&gt;</code> shows a synchronous or asynchronous
          operation, the format is <code class="computeroutput">&lt;object-type&gt;@&lt;pointer&gt;.&lt;operation&gt;</code>.
          For handler entry, it shows a comma-separated list of arguments and their
          values.
        </p>
<p>
          As shown above, Each handler is assigned a numeric identifier. Where the
          handler tracking output shows a handler number of 0, it means that the
          action was performed outside of any handler.
        </p>
<h6>
<a name="asio.overview.core.handler_tracking.h0"></a>
          <span><a name="asio.overview.core.handler_tracking.adding_location_information"></a></span><a class="link" href="handler_tracking.html#asio.overview.core.handler_tracking.adding_location_information">Adding
          Location Information</a>
        </h6>
<p>
          Programs may augment the handler tracking output's location information
          by using the macro <code class="computeroutput"><span class="identifier">ASIO_HANDLER_LOCATION</span></code>
          in the source code. For example:
        </p>
<pre class="programlisting"><span class="preprocessor">#define</span> <span class="identifier">HANDLER_LOCATION</span> <span class="special">\</span>
  <span class="identifier">ASIO_HANDLER_LOCATION</span><span class="special">((</span><span class="identifier">__FILE__</span><span class="special">,</span> <span class="identifier">__LINE__</span><span class="special">,</span> <span class="identifier">__func__</span><span class="special">))</span>

<span class="comment">// ...</span>

<span class="keyword">void</span> <span class="identifier">do_read</span><span class="special">()</span>
<span class="special">{</span>
  <span class="identifier">HANDLER_LOCATION</span><span class="special">;</span>

  <span class="keyword">auto</span> <span class="identifier">self</span><span class="special">(</span><span class="identifier">shared_from_this</span><span class="special">());</span>
  <span class="identifier">socket_</span><span class="special">.</span><span class="identifier">async_read_some</span><span class="special">(</span><span class="identifier">asio</span><span class="special">::</span><span class="identifier">buffer</span><span class="special">(</span><span class="identifier">data_</span><span class="special">,</span> <span class="identifier">max_length</span><span class="special">),</span>
      <span class="special">[</span><span class="keyword">this</span><span class="special">,</span> <span class="identifier">self</span><span class="special">](</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">error_code</span> <span class="identifier">ec</span><span class="special">,</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">size_t</span> <span class="identifier">length</span><span class="special">)</span>
      <span class="special">{</span>
        <span class="identifier">HANDLER_LOCATION</span><span class="special">;</span>

        <span class="keyword">if</span> <span class="special">(!</span><span class="identifier">ec</span><span class="special">)</span>
        <span class="special">{</span>
          <span class="identifier">do_write</span><span class="special">(</span><span class="identifier">length</span><span class="special">);</span>
        <span class="special">}</span>
      <span class="special">});</span>
<span class="special">}</span>
</pre>
<p>
          With the additional location information available, the handler tracking
          output may include a call stack of source locations:
        </p>
<pre class="programlisting">@asio|1589423304.861944|&gt;7|ec=system:0,bytes_transferred=5
@asio|1589423304.861952|7^8|in 'async_write' (./../../../include/asio/impl/write.hpp:330)
@asio|1589423304.861952|7^8|called from 'do_write' (handler_tracking/async_tcp_echo_server.cpp:62)
@asio|1589423304.861952|7^8|called from 'operator()' (handler_tracking/async_tcp_echo_server.cpp:51)
@asio|1589423304.861952|7*8|socket@0x7ff61c008230.async_send
@asio|1589423304.861975|.8|non_blocking_send,ec=system:0,bytes_transferred=5
@asio|1589423304.861980|&lt;7|
</pre>
<p>
          Furthermore, if <code class="computeroutput">std::source_location</code> or <code class="computeroutput">std::experimental::source_location</code>
          are available, the <a class="link" href="../../reference/use_awaitable_t.html" title="use_awaitable_t"><code class="computeroutput">use_awaitable_t</code></a>
          token (when default-constructed or used as a default completion token)
          will also cause handler tracking to output a source location for each newly
          created asynchronous operation. A <code class="computeroutput">use_awaitable_t</code> object may
          also be explicitly constructed with location information.
        </p>
<h6>
<a name="asio.overview.core.handler_tracking.h1"></a>
          <span><a name="asio.overview.core.handler_tracking.visual_representations"></a></span><a class="link" href="handler_tracking.html#asio.overview.core.handler_tracking.visual_representations">Visual
          Representations</a>
        </h6>
<p>
          The handler tracking output may be post-processed using the included <code class="literal">handlerviz.pl</code>
          tool to create a visual representation of the handlers (requires the GraphViz
          tool <code class="literal">dot</code>).
        </p>
<h6>
<a name="asio.overview.core.handler_tracking.h2"></a>
          <span><a name="asio.overview.core.handler_tracking.custom_tracking"></a></span><a class="link" href="handler_tracking.html#asio.overview.core.handler_tracking.custom_tracking">Custom Tracking</a>
        </h6>
<p>
          Handling tracking may be customised by defining the <code class="computeroutput"><span class="identifier">ASIO_CUSTOM_HANDLER_TRACKING</span></code>
          macro to the name of a header file (enclosed in <code class="computeroutput"><span class="string">""</span></code>
          or <code class="computeroutput"><span class="special">&lt;&gt;</span></code>). This header
          file must implement the following preprocessor macros:
        </p>
<div class="informaltable"><table class="table">
<colgroup>
<col>
<col>
</colgroup>
<thead><tr>
<th>
                  <p>
                    Macro
                  </p>
                </th>
<th>
                  <p>
                    Description
                  </p>
                </th>
</tr></thead>
<tbody>
<tr>
<td>
                  <p>
                    <code class="computeroutput"><span class="identifier">ASIO_INHERIT_TRACKED_HANDLER</span></code>
                  </p>
                </td>
<td>
                  <p>
                    Specifies a base class for classes that implement asynchronous
                    operations. When used, the macro immediately follows the class
                    name, so it must have the form <code class="computeroutput"><span class="special">:</span>
                    <span class="keyword">public</span> <span class="identifier">my_class</span></code>.
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <code class="computeroutput"><span class="identifier">ASIO_ALSO_INHERIT_TRACKED_HANDLER</span></code>
                  </p>
                </td>
<td>
                  <p>
                    Specifies a base class for classes that implement asynchronous
                    operations. When used, the macro follows other base classes,
                    so it must have the form <code class="computeroutput"><span class="special">,</span>
                    <span class="keyword">public</span> <span class="identifier">my_class</span></code>.
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <code class="computeroutput"><span class="identifier">ASIO_HANDLER_TRACKING_INIT</span><span class="special">(</span><span class="identifier">args</span><span class="special">)</span></code>
                  </p>
                </td>
<td>
                  <p>
                    An expression that is used to initialise the tracking mechanism.
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <code class="computeroutput"><span class="identifier">ASIO_HANDLER_LOCATION</span><span class="special">(</span><span class="identifier">args</span><span class="special">)</span></code>
                  </p>
                </td>
<td>
                  <p>
                    A variable declaration that is used to define a source code location.
                    <code class="computeroutput"><span class="identifier">args</span></code> is a parenthesised
                    function argument list containing the file name, line number,
                    and function name.
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <code class="computeroutput"><span class="identifier">ASIO_HANDLER_CREATION</span><span class="special">(</span><span class="identifier">args</span><span class="special">)</span></code>
                  </p>
                </td>
<td>
                  <p>
                    An expression that is called on creation of an asynchronous operation.
                    <code class="computeroutput"><span class="identifier">args</span></code> is a parenthesised
                    function argument list containing the owning execution context,
                    the tracked handler, the name of the object type, a pointer to
                    the object, the object's native handle, and the operation name.
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <code class="computeroutput"><span class="identifier">ASIO_HANDLER_COMPLETION</span><span class="special">(</span><span class="identifier">args</span><span class="special">)</span></code>
                  </p>
                </td>
<td>
                  <p>
                    An expression that is called on completion of an asynchronous
                    operation. <code class="computeroutput"><span class="identifier">args</span></code>
                    is a parenthesised function argument list containing the tracked
                    handler.
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <code class="computeroutput"><span class="identifier">ASIO_HANDLER_INVOCATION_BEGIN</span><span class="special">(</span><span class="identifier">args</span><span class="special">)</span></code>
                  </p>
                </td>
<td>
                  <p>
                    An expression that is called immediately before a completion
                    handler is invoked. <code class="computeroutput"><span class="identifier">args</span></code>
                    is a parenthesised function argument list containing the arguments
                    to the completion handler.
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <code class="computeroutput"><span class="identifier">ASIO_HANDLER_INVOCATION_END</span></code>
                  </p>
                </td>
<td>
                  <p>
                    An expression that is called immediately after a completion handler
                    is invoked.
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <code class="computeroutput"><span class="identifier">ASIO_HANDLER_OPERATION</span><span class="special">(</span><span class="identifier">args</span><span class="special">)</span></code>
                  </p>
                </td>
<td>
                  <p>
                    An expression that is called when some synchronous object operation
                    is called (such as <code class="computeroutput"><span class="identifier">close</span><span class="special">()</span></code> or <code class="computeroutput"><span class="identifier">cancel</span><span class="special">()</span></code>). <code class="computeroutput"><span class="identifier">args</span></code>
                    is a parenthesised function argument list containing the owning
                    execution context, the name of the object type, a pointer to
                    the object, the object's native handle, and the operation name.
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <code class="computeroutput"><span class="identifier">ASIO_HANDLER_REACTOR_REGISTRATION</span><span class="special">(</span><span class="identifier">args</span><span class="special">)</span></code>
                  </p>
                </td>
<td>
                  <p>
                    An expression that is called when an object is registered with
                    the reactor. <code class="computeroutput"><span class="identifier">args</span></code>
                    is a parenthesised function argument list containing the owning
                    execution context, the object's native handle, and a unique registration
                    key.
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <code class="computeroutput"><span class="identifier">ASIO_HANDLER_REACTOR_DEREGISTRATION</span><span class="special">(</span><span class="identifier">args</span><span class="special">)</span></code>
                  </p>
                </td>
<td>
                  <p>
                    An expression that is called when an object is deregistered from
                    the reactor. <code class="computeroutput"><span class="identifier">args</span></code>
                    is a parenthesised function argument list containing the owning
                    execution context, the object's native handle, and a unique registration
                    key.
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <code class="computeroutput"><span class="identifier">ASIO_HANDLER_REACTOR_READ_EVENT</span></code>
                  </p>
                </td>
<td>
                  <p>
                    A bitmask constant used to identify reactor read readiness events.
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <code class="computeroutput"><span class="identifier">ASIO_HANDLER_REACTOR_WRITE_EVENT</span></code>
                  </p>
                </td>
<td>
                  <p>
                    A bitmask constant used to identify reactor write readiness events.
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <code class="computeroutput"><span class="identifier">ASIO_HANDLER_REACTOR_ERROR_EVENT</span></code>
                  </p>
                </td>
<td>
                  <p>
                    A bitmask constant used to identify reactor error readiness events.
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <code class="computeroutput"><span class="identifier">ASIO_HANDLER_REACTOR_EVENTS</span><span class="special">(</span><span class="identifier">args</span><span class="special">)</span></code>
                  </p>
                </td>
<td>
                  <p>
                    An expression that is called when an object registered with the
                    reactor becomes ready. <code class="computeroutput"><span class="identifier">args</span></code>
                    is a parenthesised function argument list containing the owning
                    execution context, the unique registration key, and a bitmask
                    of the ready events.
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <code class="computeroutput"><span class="identifier">ASIO_HANDLER_REACTOR_OPERATION</span><span class="special">(</span><span class="identifier">args</span><span class="special">)</span></code>
                  </p>
                </td>
<td>
                  <p>
                    An expression that is called when the implementation performs
                    a system call as part of a reactor-based asynchronous operation.
                    <code class="computeroutput"><span class="identifier">args</span></code> is a parenthesised
                    function argument list containing the tracked handler, the operation
                    name, the error code produced by the operation, and (optionally)
                    the number of bytes transferred.
                  </p>
                </td>
</tr>
</tbody>
</table></div>
<h6>
<a name="asio.overview.core.handler_tracking.h3"></a>
          <span><a name="asio.overview.core.handler_tracking.see_also"></a></span><a class="link" href="handler_tracking.html#asio.overview.core.handler_tracking.see_also">See
          Also</a>
        </h6>
<p>
          <a class="link" href="../../examples/cpp11_examples.html#asio.examples.cpp11_examples.handler_tracking">Handler tracking
          examples</a>.
        </p>
</div>
<table xmlns:rev="http://www.cs.rpi.edu/~gregod/boost/tools/doc/revision" width="100%"><tr>
<td align="left"></td>
<td align="right"><div class="copyright-footer">Copyright © 2003-2021 Christopher
      M. Kohlhoff<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="allocation.html"><img src="../../../prev.png" alt="Prev"></a><a accesskey="u" href="../core.html"><img src="../../../up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../home.png" alt="Home"></a><a accesskey="n" href="concurrency_hint.html"><img src="../../../next.png" alt="Next"></a>
</div>
</body>
</html>
