<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>async_result::initiate</title>
<link rel="stylesheet" href="../../../boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.75.2">
<link rel="home" href="../../../index.html" title="Asio">
<link rel="up" href="../async_result.html" title="async_result">
<link rel="prev" href="get.html" title="async_result::get">
<link rel="next" href="return_type.html" title="async_result::return_type">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr><td valign="top"><img alt="asio C++ library" width="250" height="60" src="../../../asio.png"></td></tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="get.html"><img src="../../../prev.png" alt="Prev"></a><a accesskey="u" href="../async_result.html"><img src="../../../up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../home.png" alt="Home"></a><a accesskey="n" href="return_type.html"><img src="../../../next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="asio.reference.async_result.initiate"></a><a class="link" href="initiate.html" title="async_result::initiate">async_result::initiate</a>
</h4></div></div></div>
<p>
          <a class="indexterm" name="asio.indexterm.async_result.initiate"></a> 
Initiate the asynchronous
          operation that will produce the result, and obtain the value to be returned
          from the initiating function.
        </p>
<pre class="programlisting">template&lt;
    typename Initiation,
    typename RawCompletionToken,
    typename... Args&gt;
static return_type initiate(
    Initiation &amp;&amp; initiation,
    RawCompletionToken &amp;&amp; token,
    Args &amp;&amp;... args);
</pre>
</div>
<table xmlns:rev="http://www.cs.rpi.edu/~gregod/boost/tools/doc/revision" width="100%"><tr>
<td align="left"></td>
<td align="right"><div class="copyright-footer">Copyright © 2003-2021 Christopher
      M. Kohlhoff<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="get.html"><img src="../../../prev.png" alt="Prev"></a><a accesskey="u" href="../async_result.html"><img src="../../../up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../home.png" alt="Home"></a><a accesskey="n" href="return_type.html"><img src="../../../next.png" alt="Next"></a>
</div>
</body>
</html>
