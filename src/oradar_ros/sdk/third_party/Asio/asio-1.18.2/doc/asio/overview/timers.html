<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Timers</title>
<link rel="stylesheet" href="../../boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.75.2">
<link rel="home" href="../../index.html" title="Asio">
<link rel="up" href="../overview.html" title="Overview">
<link rel="prev" href="networking/bsd_sockets.html" title="The BSD Socket API and Asio">
<link rel="next" href="serial_ports.html" title="Serial Ports">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr><td valign="top"><img alt="asio C++ library" width="250" height="60" src="../../asio.png"></td></tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="networking/bsd_sockets.html"><img src="../../prev.png" alt="Prev"></a><a accesskey="u" href="../overview.html"><img src="../../up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../home.png" alt="Home"></a><a accesskey="n" href="serial_ports.html"><img src="../../next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="asio.overview.timers"></a><a class="link" href="timers.html" title="Timers">Timers</a>
</h3></div></div></div>
<p>
        Long running I/O operations will often have a deadline by which they must
        have completed. These deadlines may be expressed as absolute times, but are
        often calculated relative to the current time.
      </p>
<p>
        As a simple example, to perform a synchronous wait operation on a timer using
        a relative time one may write:
      </p>
<pre class="programlisting">io_context i;
...
deadline_timer t(i);
t.expires_from_now(boost::posix_time::seconds(5));
t.wait();
</pre>
<p>
        More commonly, a program will perform an asynchronous wait operation on a
        timer:
      </p>
<pre class="programlisting">void handler(asio::error_code ec) { ... }
...
io_context i;
...
deadline_timer t(i);
t.expires_from_now(boost::posix_time::milliseconds(400));
t.async_wait(handler);
...
i.run();
</pre>
<p>
        The deadline associated with a timer may also be obtained as a relative time:
      </p>
<pre class="programlisting">boost::posix_time::time_duration time_until_expiry
  = t.expires_from_now();
</pre>
<p>
        or as an absolute time to allow composition of timers:
      </p>
<pre class="programlisting">deadline_timer t2(i);
t2.expires_at(t.expires_at() + boost::posix_time::seconds(30));
</pre>
<h5>
<a name="asio.overview.timers.h0"></a>
        <span><a name="asio.overview.timers.see_also"></a></span><a class="link" href="timers.html#asio.overview.timers.see_also">See
        Also</a>
      </h5>
<p>
        <a class="link" href="../reference/basic_deadline_timer.html" title="basic_deadline_timer">basic_deadline_timer</a>,
        <a class="link" href="../reference/deadline_timer.html" title="deadline_timer">deadline_timer</a>, <a class="link" href="../tutorial/tuttimer1.html" title="Timer.1 - Using a timer synchronously">timer tutorials</a>.
      </p>
</div>
<table xmlns:rev="http://www.cs.rpi.edu/~gregod/boost/tools/doc/revision" width="100%"><tr>
<td align="left"></td>
<td align="right"><div class="copyright-footer">Copyright © 2003-2021 Christopher
      M. Kohlhoff<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="networking/bsd_sockets.html"><img src="../../prev.png" alt="Prev"></a><a accesskey="u" href="../overview.html"><img src="../../up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../home.png" alt="Home"></a><a accesskey="n" href="serial_ports.html"><img src="../../next.png" alt="Next"></a>
</div>
</body>
</html>
