<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Stackless Coroutines</title>
<link rel="stylesheet" href="../../../boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.75.2">
<link rel="home" href="../../../index.html" title="Asio">
<link rel="up" href="../core.html" title="Core Concepts and Functionality">
<link rel="prev" href="concurrency_hint.html" title="Concurrency Hints">
<link rel="next" href="spawn.html" title="Stackful Coroutines">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr><td valign="top"><img alt="asio C++ library" width="250" height="60" src="../../../asio.png"></td></tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="concurrency_hint.html"><img src="../../../prev.png" alt="Prev"></a><a accesskey="u" href="../core.html"><img src="../../../up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../home.png" alt="Home"></a><a accesskey="n" href="spawn.html"><img src="../../../next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="asio.overview.core.coroutine"></a><a class="link" href="coroutine.html" title="Stackless Coroutines">Stackless Coroutines</a>
</h4></div></div></div>
<p>
          The <a class="link" href="../../reference/coroutine.html" title="coroutine"><code class="computeroutput">coroutine</code></a>
          class provides support for stackless coroutines. Stackless coroutines enable
          programs to implement asynchronous logic in a synchronous manner, with
          minimal overhead, as shown in the following example:
        </p>
<pre class="programlisting">struct session : asio::coroutine
{
  boost::shared_ptr&lt;tcp::socket&gt; socket_;
  boost::shared_ptr&lt;std::vector&lt;char&gt; &gt; buffer_;

  session(boost::shared_ptr&lt;tcp::socket&gt; socket)
    : socket_(socket),
      buffer_(new std::vector&lt;char&gt;(1024))
  {
  }

  void operator()(asio::error_code ec = asio::error_code(), std::size_t n = 0)
  {
    if (!ec) reenter (this)
    {
      for (;;)
      {
        yield socket_-&gt;async_read_some(asio::buffer(*buffer_), *this);
        yield asio::async_write(*socket_, asio::buffer(*buffer_, n), *this);
      }
    }
  }
};
</pre>
<p>
          The <code class="computeroutput">coroutine</code> class is used in conjunction with the pseudo-keywords
          <code class="computeroutput">reenter</code>, <code class="computeroutput">yield</code> and <code class="computeroutput">fork</code>. These are
          preprocessor macros, and are implemented in terms of a <code class="computeroutput">switch</code>
          statement using a technique similar to Duff's Device. The <a class="link" href="../../reference/coroutine.html" title="coroutine"><code class="computeroutput">coroutine</code></a>
          class's documentation provides a complete description of these pseudo-keywords.
        </p>
<h6>
<a name="asio.overview.core.coroutine.h0"></a>
          <span><a name="asio.overview.core.coroutine.see_also"></a></span><a class="link" href="coroutine.html#asio.overview.core.coroutine.see_also">See
          Also</a>
        </h6>
<p>
          <a class="link" href="../../reference/coroutine.html" title="coroutine">coroutine</a>, <a class="link" href="../../examples/cpp03_examples.html#asio.examples.cpp03_examples.http_server_4">HTTP
          Server 4 example</a>, <a class="link" href="spawn.html" title="Stackful Coroutines">Stackful
          Coroutines</a>.
        </p>
</div>
<table xmlns:rev="http://www.cs.rpi.edu/~gregod/boost/tools/doc/revision" width="100%"><tr>
<td align="left"></td>
<td align="right"><div class="copyright-footer">Copyright © 2003-2021 Christopher
      M. Kohlhoff<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="concurrency_hint.html"><img src="../../../prev.png" alt="Prev"></a><a accesskey="u" href="../core.html"><img src="../../../up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../home.png" alt="Home"></a><a accesskey="n" href="spawn.html"><img src="../../../next.png" alt="Next"></a>
</div>
</body>
</html>
