<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Custom Memory Allocation</title>
<link rel="stylesheet" href="../../../boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.75.2">
<link rel="home" href="../../../index.html" title="Asio">
<link rel="up" href="../core.html" title="Core Concepts and Functionality">
<link rel="prev" href="line_based.html" title="Line-Based Operations">
<link rel="next" href="handler_tracking.html" title="Handler Tracking">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr><td valign="top"><img alt="asio C++ library" width="250" height="60" src="../../../asio.png"></td></tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="line_based.html"><img src="../../../prev.png" alt="Prev"></a><a accesskey="u" href="../core.html"><img src="../../../up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../home.png" alt="Home"></a><a accesskey="n" href="handler_tracking.html"><img src="../../../next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="asio.overview.core.allocation"></a><a class="link" href="allocation.html" title="Custom Memory Allocation">Custom Memory Allocation</a>
</h4></div></div></div>
<p>
          Many asynchronous operations need to allocate an object to store state
          associated with the operation. For example, a Win32 implementation needs
          <code class="computeroutput"><span class="identifier">OVERLAPPED</span></code>-derived objects
          to pass to Win32 API functions.
        </p>
<p>
          Furthermore, programs typically contain easily identifiable chains of asynchronous
          operations. A half duplex protocol implementation (e.g. an HTTP server)
          would have a single chain of operations per client (receives followed by
          sends). A full duplex protocol implementation would have two chains executing
          in parallel. Programs should be able to leverage this knowledge to reuse
          memory for all asynchronous operations in a chain.
        </p>
<p>
          Given a copy of a user-defined <code class="computeroutput"><span class="identifier">Handler</span></code>
          object <code class="computeroutput"><span class="identifier">h</span></code>, if the implementation
          needs to allocate memory associated with that handler it will obtain an
          allocator using the <code class="computeroutput"><span class="identifier">get_associated_allocator</span></code>
          function. For example:
        </p>
<pre class="programlisting"><span class="identifier">asio</span><span class="special">::</span><span class="identifier">associated_allocator_t</span><span class="special">&lt;</span><span class="identifier">Handler</span><span class="special">&gt;</span> <span class="identifier">a</span> <span class="special">=</span> <span class="identifier">asio</span><span class="special">::</span><span class="identifier">get_associated_allocator</span><span class="special">(</span><span class="identifier">h</span><span class="special">);</span>
</pre>
<p>
          The associated allocator must satisfy the standard Allocator requirements.
        </p>
<p>
          By default, handlers use the standard allocator (which is implemented in
          terms of <code class="computeroutput"><span class="special">::</span><span class="keyword">operator</span>
          <span class="keyword">new</span><span class="special">()</span></code>
          and <code class="computeroutput"><span class="special">::</span><span class="keyword">operator</span>
          <span class="keyword">delete</span><span class="special">()</span></code>).
          The allocator may be customised for a particular handler type by specifying
          a nested type <code class="computeroutput"><span class="identifier">allocator_type</span></code>
          and member function <code class="computeroutput"><span class="identifier">get_allocator</span><span class="special">()</span></code>:
        </p>
<pre class="programlisting"><span class="keyword">class</span> <span class="identifier">my_handler</span>
<span class="special">{</span>
<span class="keyword">public</span><span class="special">:</span>
  <span class="comment">// Custom implementation of Allocator type requirements.</span>
  <span class="keyword">typedef</span> <span class="identifier">my_allocator</span> <span class="identifier">allocator_type</span><span class="special">;</span>

  <span class="comment">// Return a custom allocator implementation.</span>
  <span class="identifier">allocator_type</span> <span class="identifier">get_allocator</span><span class="special">()</span> <span class="keyword">const</span> <span class="keyword">noexcept</span>
  <span class="special">{</span>
    <span class="keyword">return</span> <span class="identifier">my_allocator</span><span class="special">();</span>
  <span class="special">}</span>

  <span class="keyword">void</span> <span class="keyword">operator</span><span class="special">()()</span> <span class="special">{</span> <span class="special">...</span> <span class="special">}</span>
<span class="special">};</span>
</pre>
<p>
          In more complex cases, the <code class="computeroutput"><span class="identifier">associated_allocator</span></code>
          template may be partially specialised directly:
        </p>
<pre class="programlisting"><span class="keyword">namespace</span> <span class="identifier">asio</span> <span class="special">{</span>

  <span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">Allocator</span><span class="special">&gt;</span>
  <span class="keyword">struct</span> <span class="identifier">associated_allocator</span><span class="special">&lt;</span><span class="identifier">my_handler</span><span class="special">,</span> <span class="identifier">Allocator</span><span class="special">&gt;</span>
  <span class="special">{</span>
    <span class="comment">// Custom implementation of Allocator type requirements.</span>
    <span class="keyword">typedef</span> <span class="identifier">my_allocator</span> <span class="identifier">type</span><span class="special">;</span>

    <span class="comment">// Return a custom allocator implementation.</span>
    <span class="keyword">static</span> <span class="identifier">type</span> <span class="identifier">get</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">my_handler</span><span class="special">&amp;,</span>
        <span class="keyword">const</span> <span class="identifier">Allocator</span><span class="special">&amp;</span> <span class="identifier">a</span> <span class="special">=</span> <span class="identifier">Allocator</span><span class="special">())</span> <span class="keyword">noexcept</span>
    <span class="special">{</span>
      <span class="keyword">return</span> <span class="identifier">my_allocator</span><span class="special">();</span>
    <span class="special">}</span>
  <span class="special">};</span>

<span class="special">}</span> <span class="comment">// namespace asio</span>
</pre>
<p>
          The implementation guarantees that the deallocation will occur before the
          associated handler is invoked, which means the memory is ready to be reused
          for any new asynchronous operations started by the handler.
        </p>
<p>
          The custom memory allocation functions may be called from any user-created
          thread that is calling a library function. The implementation guarantees
          that, for the asynchronous operations included the library, the implementation
          will not make concurrent calls to the memory allocation functions for that
          handler. The implementation will insert appropriate memory barriers to
          ensure correct memory visibility should allocation functions need to be
          called from different threads.
        </p>
<h6>
<a name="asio.overview.core.allocation.h0"></a>
          <span><a name="asio.overview.core.allocation.see_also"></a></span><a class="link" href="allocation.html#asio.overview.core.allocation.see_also">See
          Also</a>
        </h6>
<p>
          <a class="link" href="../../reference/associated_allocator.html" title="associated_allocator">associated_allocator</a>,
          <a class="link" href="../../reference/get_associated_allocator.html" title="get_associated_allocator">get_associated_allocator</a>,
          <a class="link" href="../../examples/cpp03_examples.html#asio.examples.cpp03_examples.allocation">custom memory allocation
          example (C++03)</a>, <a class="link" href="../../examples/cpp11_examples.html#asio.examples.cpp11_examples.allocation">custom
          memory allocation example (C++11)</a>.
        </p>
</div>
<table xmlns:rev="http://www.cs.rpi.edu/~gregod/boost/tools/doc/revision" width="100%"><tr>
<td align="left"></td>
<td align="right"><div class="copyright-footer">Copyright © 2003-2021 Christopher
      M. Kohlhoff<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="line_based.html"><img src="../../../prev.png" alt="Prev"></a><a accesskey="u" href="../core.html"><img src="../../../up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../home.png" alt="Home"></a><a accesskey="n" href="handler_tracking.html"><img src="../../../next.png" alt="Next"></a>
</div>
</body>
</html>
