<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Object HANDLEs</title>
<link rel="stylesheet" href="../../../boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.75.2">
<link rel="home" href="../../../index.html" title="Asio">
<link rel="up" href="../windows.html" title="Windows-Specific Functionality">
<link rel="prev" href="random_access_handle.html" title="Random-Access HANDLEs">
<link rel="next" href="../ssl.html" title="SSL">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr><td valign="top"><img alt="asio C++ library" width="250" height="60" src="../../../asio.png"></td></tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="random_access_handle.html"><img src="../../../prev.png" alt="Prev"></a><a accesskey="u" href="../windows.html"><img src="../../../up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../home.png" alt="Home"></a><a accesskey="n" href="../ssl.html"><img src="../../../next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="asio.overview.windows.object_handle"></a><a class="link" href="object_handle.html" title="Object HANDLEs">Object HANDLEs</a>
</h4></div></div></div>
<p>
          Asio provides Windows-specific classes that permit asynchronous wait operations
          to be performed on HANDLEs to kernel objects of the following types:
        </p>
<div class="itemizedlist"><ul class="itemizedlist" type="disc">
<li class="listitem">
              Change notification
            </li>
<li class="listitem">
              Console input
            </li>
<li class="listitem">
              Event
            </li>
<li class="listitem">
              Memory resource notification
            </li>
<li class="listitem">
              Process
            </li>
<li class="listitem">
              Semaphore
            </li>
<li class="listitem">
              Thread
            </li>
<li class="listitem">
              Waitable timer
            </li>
</ul></div>
<p>
          For example, to perform asynchronous operations on an event, the following
          object may be created:
        </p>
<pre class="programlisting">HANDLE handle = ::CreateEvent(...);
windows::object_handle file(my_io_context, handle);
</pre>
<p>
          The <code class="computeroutput">wait()</code> and <code class="computeroutput">async_wait()</code> member functions
          may then be used to wait until the kernel object is signalled.
        </p>
<h6>
<a name="asio.overview.windows.object_handle.h0"></a>
          <span><a name="asio.overview.windows.object_handle.see_also"></a></span><a class="link" href="object_handle.html#asio.overview.windows.object_handle.see_also">See
          Also</a>
        </h6>
<p>
          <a class="link" href="../../reference/windows__object_handle.html" title="windows::object_handle">windows::object_handle</a>.
        </p>
<h6>
<a name="asio.overview.windows.object_handle.h1"></a>
          <span><a name="asio.overview.windows.object_handle.notes"></a></span><a class="link" href="object_handle.html#asio.overview.windows.object_handle.notes">Notes</a>
        </h6>
<p>
          Windows object <code class="computeroutput">HANDLE</code>s are only available at compile time
          when targeting Windows. Programs may test for the macro <code class="computeroutput">ASIO_HAS_WINDOWS_OBJECT_HANDLE</code>
          to determine whether they are supported.
        </p>
</div>
<table xmlns:rev="http://www.cs.rpi.edu/~gregod/boost/tools/doc/revision" width="100%"><tr>
<td align="left"></td>
<td align="right"><div class="copyright-footer">Copyright © 2003-2021 Christopher
      M. Kohlhoff<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="random_access_handle.html"><img src="../../../prev.png" alt="Prev"></a><a accesskey="u" href="../windows.html"><img src="../../../up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../home.png" alt="Home"></a><a accesskey="n" href="../ssl.html"><img src="../../../next.png" alt="Next"></a>
</div>
</body>
</html>
