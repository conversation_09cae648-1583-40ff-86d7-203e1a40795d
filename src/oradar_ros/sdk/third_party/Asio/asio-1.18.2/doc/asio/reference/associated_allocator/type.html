<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>associated_allocator::type</title>
<link rel="stylesheet" href="../../../boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.75.2">
<link rel="home" href="../../../index.html" title="Asio">
<link rel="up" href="../associated_allocator.html" title="associated_allocator">
<link rel="prev" href="get.html" title="associated_allocator::get">
<link rel="next" href="../associated_executor.html" title="associated_executor">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr><td valign="top"><img alt="asio C++ library" width="250" height="60" src="../../../asio.png"></td></tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="get.html"><img src="../../../prev.png" alt="Prev"></a><a accesskey="u" href="../associated_allocator.html"><img src="../../../up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../home.png" alt="Home"></a><a accesskey="n" href="../associated_executor.html"><img src="../../../next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="asio.reference.associated_allocator.type"></a><a class="link" href="type.html" title="associated_allocator::type">associated_allocator::type</a>
</h4></div></div></div>
<p>
          <a class="indexterm" name="asio.indexterm.associated_allocator.type"></a> 
If <code class="computeroutput">T</code>
          has a nested type <code class="computeroutput">allocator_type</code>, <code class="computeroutput">T::allocator_type</code>.
          Otherwise <code class="computeroutput">Allocator</code>.
        </p>
<pre class="programlisting">typedef see_below type;
</pre>
<h6>
<a name="asio.reference.associated_allocator.type.h0"></a>
          <span><a name="asio.reference.associated_allocator.type.requirements"></a></span><a class="link" href="type.html#asio.reference.associated_allocator.type.requirements">Requirements</a>
        </h6>
<p>
          <span class="emphasis"><em>Header: </em></span><code class="literal">asio/associated_allocator.hpp</code>
        </p>
<p>
          <span class="emphasis"><em>Convenience header: </em></span><code class="literal">asio.hpp</code>
        </p>
</div>
<table xmlns:rev="http://www.cs.rpi.edu/~gregod/boost/tools/doc/revision" width="100%"><tr>
<td align="left"></td>
<td align="right"><div class="copyright-footer">Copyright © 2003-2021 Christopher
      M. Kohlhoff<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="get.html"><img src="../../../prev.png" alt="Prev"></a><a accesskey="u" href="../associated_allocator.html"><img src="../../../up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../home.png" alt="Home"></a><a accesskey="n" href="../associated_executor.html"><img src="../../../next.png" alt="Next"></a>
</div>
</body>
</html>
