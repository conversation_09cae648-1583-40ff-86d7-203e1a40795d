<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>basic_datagram_socket::basic_datagram_socket (7 of 10 overloads)</title>
<link rel="stylesheet" href="../../../../boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.75.2">
<link rel="home" href="../../../../index.html" title="Asio">
<link rel="up" href="../basic_datagram_socket.html" title="basic_datagram_socket::basic_datagram_socket">
<link rel="prev" href="overload6.html" title="basic_datagram_socket::basic_datagram_socket (6 of 10 overloads)">
<link rel="next" href="overload8.html" title="basic_datagram_socket::basic_datagram_socket (8 of 10 overloads)">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr><td valign="top"><img alt="asio C++ library" width="250" height="60" src="../../../../asio.png"></td></tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="overload6.html"><img src="../../../../prev.png" alt="Prev"></a><a accesskey="u" href="../basic_datagram_socket.html"><img src="../../../../up.png" alt="Up"></a><a accesskey="h" href="../../../../index.html"><img src="../../../../home.png" alt="Home"></a><a accesskey="n" href="overload8.html"><img src="../../../../next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h5 class="title">
<a name="asio.reference.basic_datagram_socket.basic_datagram_socket.overload7"></a><a class="link" href="overload7.html" title="basic_datagram_socket::basic_datagram_socket (7 of 10 overloads)">basic_datagram_socket::basic_datagram_socket
          (7 of 10 overloads)</a>
</h5></div></div></div>
<p>
            Construct a <a class="link" href="../../basic_datagram_socket.html" title="basic_datagram_socket"><code class="computeroutput">basic_datagram_socket</code></a>
            on an existing native socket.
          </p>
<pre class="programlisting">basic_datagram_socket(
    const executor_type &amp; ex,
    const protocol_type &amp; protocol,
    const native_handle_type &amp; native_socket);
</pre>
<p>
            This constructor creates a datagram socket object to hold an existing
            native socket.
          </p>
<h6>
<a name="asio.reference.basic_datagram_socket.basic_datagram_socket.overload7.h0"></a>
            <span><a name="asio.reference.basic_datagram_socket.basic_datagram_socket.overload7.parameters"></a></span><a class="link" href="overload7.html#asio.reference.basic_datagram_socket.basic_datagram_socket.overload7.parameters">Parameters</a>
          </h6>
<div class="variablelist">
<p class="title"><b></b></p>
<dl>
<dt><span class="term">ex</span></dt>
<dd><p>
                  The I/O executor that the socket will use, by default, to dispatch
                  handlers for any asynchronous operations performed on the socket.
                </p></dd>
<dt><span class="term">protocol</span></dt>
<dd><p>
                  An object specifying protocol parameters to be used.
                </p></dd>
<dt><span class="term">native_socket</span></dt>
<dd><p>
                  The new underlying socket implementation.
                </p></dd>
</dl>
</div>
<h6>
<a name="asio.reference.basic_datagram_socket.basic_datagram_socket.overload7.h1"></a>
            <span><a name="asio.reference.basic_datagram_socket.basic_datagram_socket.overload7.exceptions"></a></span><a class="link" href="overload7.html#asio.reference.basic_datagram_socket.basic_datagram_socket.overload7.exceptions">Exceptions</a>
          </h6>
<div class="variablelist">
<p class="title"><b></b></p>
<dl>
<dt><span class="term">asio::system_error</span></dt>
<dd><p>
                  Thrown on failure.
                </p></dd>
</dl>
</div>
</div>
<table xmlns:rev="http://www.cs.rpi.edu/~gregod/boost/tools/doc/revision" width="100%"><tr>
<td align="left"></td>
<td align="right"><div class="copyright-footer">Copyright © 2003-2021 Christopher
      M. Kohlhoff<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="overload6.html"><img src="../../../../prev.png" alt="Prev"></a><a accesskey="u" href="../basic_datagram_socket.html"><img src="../../../../up.png" alt="Up"></a><a accesskey="h" href="../../../../index.html"><img src="../../../../home.png" alt="Home"></a><a accesskey="n" href="overload8.html"><img src="../../../../next.png" alt="Next"></a>
</div>
</body>
</html>
