<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Socket Iostreams</title>
<link rel="stylesheet" href="../../../boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.75.2">
<link rel="home" href="../../../index.html" title="Asio">
<link rel="up" href="../networking.html" title="Networking">
<link rel="prev" href="other_protocols.html" title="Support for Other Protocols">
<link rel="next" href="bsd_sockets.html" title="The BSD Socket API and Asio">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr><td valign="top"><img alt="asio C++ library" width="250" height="60" src="../../../asio.png"></td></tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="other_protocols.html"><img src="../../../prev.png" alt="Prev"></a><a accesskey="u" href="../networking.html"><img src="../../../up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../home.png" alt="Home"></a><a accesskey="n" href="bsd_sockets.html"><img src="../../../next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="asio.overview.networking.iostreams"></a><a class="link" href="iostreams.html" title="Socket Iostreams">Socket Iostreams</a>
</h4></div></div></div>
<p>
          Asio includes classes that implement iostreams on top of sockets. These
          hide away the complexities associated with endpoint resolution, protocol
          independence, etc. To create a connection one might simply write:
        </p>
<pre class="programlisting">ip::tcp::iostream stream("www.boost.org", "http");
if (!stream)
{
  // Can't connect.
}
</pre>
<p>
          The iostream class can also be used in conjunction with an acceptor to
          create simple servers. For example:
        </p>
<pre class="programlisting">io_context ioc;

ip::tcp::endpoint endpoint(tcp::v4(), 80);
ip::tcp::acceptor acceptor(ios, endpoint);

for (;;)
{
  ip::tcp::iostream stream;
  acceptor.accept(stream.socket());
  ...
}
</pre>
<p>
          Timeouts may be set by calling <code class="computeroutput">expires_at()</code> or <code class="computeroutput">expires_from_now()</code>
          to establish a deadline. Any socket operations that occur past the deadline
          will put the iostream into a "bad" state.
        </p>
<p>
          For example, a simple client program like this:
        </p>
<pre class="programlisting">ip::tcp::iostream stream;
stream.expires_from_now(boost::posix_time::seconds(60));
stream.connect("www.boost.org", "http");
stream &lt;&lt; "GET /LICENSE_1_0.txt HTTP/1.0\r\n";
stream &lt;&lt; "Host: www.boost.org\r\n";
stream &lt;&lt; "Accept: */*\r\n";
stream &lt;&lt; "Connection: close\r\n\r\n";
stream.flush();
std::cout &lt;&lt; stream.rdbuf();
</pre>
<p>
          will fail if all the socket operations combined take longer than 60 seconds.
        </p>
<p>
          If an error does occur, the iostream's <code class="computeroutput">error()</code> member function
          may be used to retrieve the error code from the most recent system call:
        </p>
<pre class="programlisting">if (!stream)
{
  std::cout &lt;&lt; "Error: " &lt;&lt; stream.error().message() &lt;&lt; "\n";
}
</pre>
<h6>
<a name="asio.overview.networking.iostreams.h0"></a>
          <span><a name="asio.overview.networking.iostreams.see_also"></a></span><a class="link" href="iostreams.html#asio.overview.networking.iostreams.see_also">See
          Also</a>
        </h6>
<p>
          <a class="link" href="../../reference/ip__tcp/iostream.html" title="ip::tcp::iostream">ip::tcp::iostream</a>,
          <a class="link" href="../../reference/basic_socket_iostream.html" title="basic_socket_iostream">basic_socket_iostream</a>,
          <a class="link" href="../../examples/cpp03_examples.html#asio.examples.cpp03_examples.iostreams">iostreams examples</a>.
        </p>
<h6>
<a name="asio.overview.networking.iostreams.h1"></a>
          <span><a name="asio.overview.networking.iostreams.notes"></a></span><a class="link" href="iostreams.html#asio.overview.networking.iostreams.notes">Notes</a>
        </h6>
<p>
          These iostream templates only support <code class="computeroutput">char</code>, not <code class="computeroutput">wchar_t</code>,
          and do not perform any code conversion.
        </p>
</div>
<table xmlns:rev="http://www.cs.rpi.edu/~gregod/boost/tools/doc/revision" width="100%"><tr>
<td align="left"></td>
<td align="right"><div class="copyright-footer">Copyright © 2003-2021 Christopher
      M. Kohlhoff<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="other_protocols.html"><img src="../../../prev.png" alt="Prev"></a><a accesskey="u" href="../networking.html"><img src="../../../up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../home.png" alt="Home"></a><a accesskey="n" href="bsd_sockets.html"><img src="../../../next.png" alt="Next"></a>
</div>
</body>
</html>
