<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Streams, Short Reads and Short Writes</title>
<link rel="stylesheet" href="../../../boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.75.2">
<link rel="home" href="../../../index.html" title="Asio">
<link rel="up" href="../core.html" title="Core Concepts and Functionality">
<link rel="prev" href="buffers.html" title="Buffers">
<link rel="next" href="reactor.html" title="Reactor-Style Operations">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr><td valign="top"><img alt="asio C++ library" width="250" height="60" src="../../../asio.png"></td></tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="buffers.html"><img src="../../../prev.png" alt="Prev"></a><a accesskey="u" href="../core.html"><img src="../../../up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../home.png" alt="Home"></a><a accesskey="n" href="reactor.html"><img src="../../../next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="asio.overview.core.streams"></a><a class="link" href="streams.html" title="Streams, Short Reads and Short Writes">Streams, Short Reads and
        Short Writes</a>
</h4></div></div></div>
<p>
          Many I/O objects in Asio are stream-oriented. This means that:
        </p>
<div class="itemizedlist"><ul class="itemizedlist" type="disc">
<li class="listitem">
              There are no message boundaries. The data being transferred is a continuous
              sequence of bytes.
            </li>
<li class="listitem">
              Read or write operations may transfer fewer bytes than requested. This
              is referred to as a short read or short write.
            </li>
</ul></div>
<p>
          Objects that provide stream-oriented I/O model one or more of the following
          type requirements:
        </p>
<div class="itemizedlist"><ul class="itemizedlist" type="disc">
<li class="listitem">
              <code class="computeroutput"><span class="identifier">SyncReadStream</span></code>, where
              synchronous read operations are performed using a member function called
              <code class="computeroutput"><span class="identifier">read_some</span><span class="special">()</span></code>.
            </li>
<li class="listitem">
              <code class="computeroutput"><span class="identifier">AsyncReadStream</span></code>, where
              asynchronous read operations are performed using a member function
              called <code class="computeroutput"><span class="identifier">async_read_some</span><span class="special">()</span></code>.
            </li>
<li class="listitem">
              <code class="computeroutput"><span class="identifier">SyncWriteStream</span></code>, where
              synchronous write operations are performed using a member function
              called <code class="computeroutput"><span class="identifier">write_some</span><span class="special">()</span></code>.
            </li>
<li class="listitem">
              <code class="computeroutput"><span class="identifier">AsyncWriteStream</span></code>, where
              asynchronous write operations are performed using a member function
              called <code class="computeroutput"><span class="identifier">async_write_some</span><span class="special">()</span></code>.
            </li>
</ul></div>
<p>
          Examples of stream-oriented I/O objects include <code class="computeroutput"><span class="identifier">ip</span><span class="special">::</span><span class="identifier">tcp</span><span class="special">::</span><span class="identifier">socket</span></code>,
          <code class="computeroutput"><span class="identifier">ssl</span><span class="special">::</span><span class="identifier">stream</span><span class="special">&lt;&gt;</span></code>,
          <code class="computeroutput"><span class="identifier">posix</span><span class="special">::</span><span class="identifier">stream_descriptor</span></code>, <code class="computeroutput"><span class="identifier">windows</span><span class="special">::</span><span class="identifier">stream_handle</span></code>,
          etc.
        </p>
<p>
          Programs typically want to transfer an exact number of bytes. When a short
          read or short write occurs the program must restart the operation, and
          continue to do so until the required number of bytes has been transferred.
          Asio provides generic functions that do this automatically: <code class="computeroutput"><span class="identifier">read</span><span class="special">()</span></code>,
          <code class="computeroutput"><span class="identifier">async_read</span><span class="special">()</span></code>,
          <code class="computeroutput"><span class="identifier">write</span><span class="special">()</span></code>
          and <code class="computeroutput"><span class="identifier">async_write</span><span class="special">()</span></code>.
        </p>
<h6>
<a name="asio.overview.core.streams.h0"></a>
          <span><a name="asio.overview.core.streams.why_eof_is_an_error"></a></span><a class="link" href="streams.html#asio.overview.core.streams.why_eof_is_an_error">Why
          EOF is an Error</a>
        </h6>
<div class="itemizedlist"><ul class="itemizedlist" type="disc">
<li class="listitem">
              The end of a stream can cause <code class="computeroutput"><span class="identifier">read</span></code>,
              <code class="computeroutput"><span class="identifier">async_read</span></code>, <code class="computeroutput"><span class="identifier">read_until</span></code> or <code class="computeroutput"><span class="identifier">async_read_until</span></code>
              functions to violate their contract. E.g. a read of N bytes may finish
              early due to EOF.
            </li>
<li class="listitem">
              An EOF error may be used to distinguish the end of a stream from a
              successful read of size 0.
            </li>
</ul></div>
<h6>
<a name="asio.overview.core.streams.h1"></a>
          <span><a name="asio.overview.core.streams.see_also"></a></span><a class="link" href="streams.html#asio.overview.core.streams.see_also">See
          Also</a>
        </h6>
<p>
          <a class="link" href="../../reference/async_read.html" title="async_read">async_read()</a>, <a class="link" href="../../reference/async_write.html" title="async_write">async_write()</a>,
          <a class="link" href="../../reference/read.html" title="read">read()</a>, <a class="link" href="../../reference/write.html" title="write">write()</a>,
          <a class="link" href="../../reference/AsyncReadStream.html" title="Buffer-oriented asynchronous read stream requirements">AsyncReadStream</a>,
          <a class="link" href="../../reference/AsyncWriteStream.html" title="Buffer-oriented asynchronous write stream requirements">AsyncWriteStream</a>,
          <a class="link" href="../../reference/SyncReadStream.html" title="Buffer-oriented synchronous read stream requirements">SyncReadStream</a>, <a class="link" href="../../reference/SyncWriteStream.html" title="Buffer-oriented synchronous write stream requirements">SyncWriteStream</a>.
        </p>
</div>
<table xmlns:rev="http://www.cs.rpi.edu/~gregod/boost/tools/doc/revision" width="100%"><tr>
<td align="left"></td>
<td align="right"><div class="copyright-footer">Copyright © 2003-2021 Christopher
      M. Kohlhoff<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="buffers.html"><img src="../../../prev.png" alt="Prev"></a><a accesskey="u" href="../core.html"><img src="../../../up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../home.png" alt="Home"></a><a accesskey="n" href="reactor.html"><img src="../../../next.png" alt="Next"></a>
</div>
</body>
</html>
