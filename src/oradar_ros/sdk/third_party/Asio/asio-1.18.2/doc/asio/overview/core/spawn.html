<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Stackful Coroutines</title>
<link rel="stylesheet" href="../../../boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.75.2">
<link rel="home" href="../../../index.html" title="Asio">
<link rel="up" href="../core.html" title="Core Concepts and Functionality">
<link rel="prev" href="coroutine.html" title="Stackless Coroutines">
<link rel="next" href="coroutines_ts.html" title="Coroutines TS Support">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr><td valign="top"><img alt="asio C++ library" width="250" height="60" src="../../../asio.png"></td></tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="coroutine.html"><img src="../../../prev.png" alt="Prev"></a><a accesskey="u" href="../core.html"><img src="../../../up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../home.png" alt="Home"></a><a accesskey="n" href="coroutines_ts.html"><img src="../../../next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="asio.overview.core.spawn"></a><a class="link" href="spawn.html" title="Stackful Coroutines">Stackful Coroutines</a>
</h4></div></div></div>
<p>
          The <a class="link" href="../../reference/spawn.html" title="spawn"><code class="computeroutput">spawn()</code></a> function
          is a high-level wrapper for running stackful coroutines. It is based on
          the Boost.Coroutine library. The <code class="computeroutput">spawn()</code> function enables
          programs to implement asynchronous logic in a synchronous manner, as shown
          in the following example:
        </p>
<pre class="programlisting">asio::spawn(my_strand, do_echo);

// ...

void do_echo(asio::yield_context yield)
{
  try
  {
    char data[128];
    for (;;)
    {
      std::size_t length =
        my_socket.async_read_some(
          asio::buffer(data), yield);

      asio::async_write(my_socket,
          asio::buffer(data, length), yield);
    }
  }
  catch (std::exception&amp; e)
  {
    // ...
  }
}
</pre>
<p>
          The first argument to <code class="computeroutput">spawn()</code> may be a <a class="link" href="../../reference/io_context__strand.html" title="io_context::strand"><code class="computeroutput">strand</code></a>,
          <a class="link" href="../../reference/io_context.html" title="io_context"><code class="computeroutput">io_context</code></a>,
          or <a class="link" href="../../reference/CompletionHandler.html" title="Completion handler requirements">completion handler</a>.
          This argument determines the context in which the coroutine is permitted
          to execute. For example, a server's per-client object may consist of multiple
          coroutines; they should all run on the same <code class="computeroutput">strand</code> so that
          no explicit synchronisation is required.
        </p>
<p>
          The second argument is a function object with signature:
        </p>
<pre class="programlisting">void coroutine(asio::yield_context yield);
</pre>
<p>
          that specifies the code to be run as part of the coroutine. The parameter
          <code class="computeroutput">yield</code> may be passed to an asynchronous operation in place
          of the completion handler, as in:
        </p>
<pre class="programlisting">std::size_t length =
  my_socket.async_read_some(
    asio::buffer(data), yield);
</pre>
<p>
          This starts the asynchronous operation and suspends the coroutine. The
          coroutine will be resumed automatically when the asynchronous operation
          completes.
        </p>
<p>
          Where an asynchronous operation's handler signature has the form:
        </p>
<pre class="programlisting">void handler(asio::error_code ec, result_type result);
</pre>
<p>
          the initiating function returns the result_type. In the <code class="computeroutput">async_read_some</code>
          example above, this is <code class="computeroutput">size_t</code>. If the asynchronous operation
          fails, the <code class="computeroutput">error_code</code> is converted into a <code class="computeroutput">system_error</code>
          exception and thrown.
        </p>
<p>
          Where a handler signature has the form:
        </p>
<pre class="programlisting">void handler(asio::error_code ec);
</pre>
<p>
          the initiating function returns <code class="computeroutput">void</code>. As above, an error is
          passed back to the coroutine as a <code class="computeroutput">system_error</code> exception.
        </p>
<p>
          To collect the <code class="computeroutput">error_code</code> from an operation, rather than have
          it throw an exception, associate the output variable with the <code class="computeroutput">yield_context</code>
          as follows:
        </p>
<pre class="programlisting">asio::error_code ec;
std::size_t length =
  my_socket.async_read_some(
    asio::buffer(data), yield[ec]);
</pre>
<p>
          <span class="bold"><strong>Note:</strong></span> if <code class="computeroutput">spawn()</code> is used
          with a custom completion handler of type <code class="computeroutput">Handler</code>, the function
          object signature is actually:
        </p>
<pre class="programlisting">void coroutine(asio::basic_yield_context&lt;Handler&gt; yield);
</pre>
<h6>
<a name="asio.overview.core.spawn.h0"></a>
          <span><a name="asio.overview.core.spawn.see_also"></a></span><a class="link" href="spawn.html#asio.overview.core.spawn.see_also">See
          Also</a>
        </h6>
<p>
          <a class="link" href="../../reference/spawn.html" title="spawn">spawn</a>, <a class="link" href="../../reference/yield_context.html" title="yield_context">yield_context</a>,
          <a class="link" href="../../reference/basic_yield_context.html" title="basic_yield_context">basic_yield_context</a>,
          <a class="link" href="../../examples/cpp03_examples.html#asio.examples.cpp03_examples.spawn">Spawn example (C++03)</a>,
          <a class="link" href="../../examples/cpp11_examples.html#asio.examples.cpp11_examples.spawn">Spawn example (C++11)</a>,
          <a class="link" href="coroutine.html" title="Stackless Coroutines">Stackless Coroutines</a>.
        </p>
</div>
<table xmlns:rev="http://www.cs.rpi.edu/~gregod/boost/tools/doc/revision" width="100%"><tr>
<td align="left"></td>
<td align="right"><div class="copyright-footer">Copyright © 2003-2021 Christopher
      M. Kohlhoff<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="coroutine.html"><img src="../../../prev.png" alt="Prev"></a><a accesskey="u" href="../core.html"><img src="../../../up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../home.png" alt="Home"></a><a accesskey="n" href="coroutines_ts.html"><img src="../../../next.png" alt="Next"></a>
</div>
</body>
</html>
