<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Array Container</title>
<link rel="stylesheet" href="../../../boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.75.2">
<link rel="home" href="../../../index.html" title="Asio">
<link rel="up" href="../cpp2011.html" title="C++ 2011 Support">
<link rel="prev" href="variadic.html" title="Variadic Templates">
<link rel="next" href="atomic.html" title="Atomics">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr><td valign="top"><img alt="asio C++ library" width="250" height="60" src="../../../asio.png"></td></tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="variadic.html"><img src="../../../prev.png" alt="Prev"></a><a accesskey="u" href="../cpp2011.html"><img src="../../../up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../home.png" alt="Home"></a><a accesskey="n" href="atomic.html"><img src="../../../next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="asio.overview.cpp2011.array"></a><a class="link" href="array.html" title="Array Container">Array Container</a>
</h4></div></div></div>
<p>
          Where the standard library provides <code class="computeroutput">std::array&lt;&gt;</code>, Asio:
        </p>
<div class="itemizedlist"><ul class="itemizedlist" type="disc">
<li class="listitem">
              Provides overloads for the <a class="link" href="../../reference/buffer.html" title="buffer">buffer()</a>
              function.
            </li>
<li class="listitem">
              Uses it in preference to <code class="computeroutput">boost::array&lt;&gt;</code> for the
              <a class="link" href="../../reference/ip__address_v4/bytes_type.html" title="ip::address_v4::bytes_type">ip::address_v4::bytes_type</a>
              and <a class="link" href="../../reference/ip__address_v6/bytes_type.html" title="ip::address_v6::bytes_type">ip::address_v6::bytes_type</a>
              types.
            </li>
<li class="listitem">
              Uses it in preference to <code class="computeroutput">boost::array&lt;&gt;</code> where a
              fixed size array type is needed in the implementation.
            </li>
</ul></div>
<p>
          Support for <code class="computeroutput">std::array&lt;&gt;</code> is automatically enabled for
          <code class="literal">g++</code> 4.3 and later, when the <code class="literal">-std=c++0x</code>
          or <code class="literal">-std=gnu++0x</code> compiler options are used, as well as
          for Microsoft Visual C++ 10. It may be disabled by defining <code class="computeroutput">ASIO_DISABLE_STD_ARRAY</code>,
          or explicitly enabled for other compilers by defining <code class="computeroutput">ASIO_HAS_STD_ARRAY</code>.
        </p>
</div>
<table xmlns:rev="http://www.cs.rpi.edu/~gregod/boost/tools/doc/revision" width="100%"><tr>
<td align="left"></td>
<td align="right"><div class="copyright-footer">Copyright © 2003-2021 Christopher
      M. Kohlhoff<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="variadic.html"><img src="../../../prev.png" alt="Prev"></a><a accesskey="u" href="../cpp2011.html"><img src="../../../up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../home.png" alt="Home"></a><a accesskey="n" href="atomic.html"><img src="../../../next.png" alt="Next"></a>
</div>
</body>
</html>
