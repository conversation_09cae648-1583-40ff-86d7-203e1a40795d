<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Stream-Oriented HANDLEs</title>
<link rel="stylesheet" href="../../../boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.75.2">
<link rel="home" href="../../../index.html" title="Asio">
<link rel="up" href="../windows.html" title="Windows-Specific Functionality">
<link rel="prev" href="../windows.html" title="Windows-Specific Functionality">
<link rel="next" href="random_access_handle.html" title="Random-Access HANDLEs">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr><td valign="top"><img alt="asio C++ library" width="250" height="60" src="../../../asio.png"></td></tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="../windows.html"><img src="../../../prev.png" alt="Prev"></a><a accesskey="u" href="../windows.html"><img src="../../../up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../home.png" alt="Home"></a><a accesskey="n" href="random_access_handle.html"><img src="../../../next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="asio.overview.windows.stream_handle"></a><a class="link" href="stream_handle.html" title="Stream-Oriented HANDLEs">Stream-Oriented
        HANDLEs</a>
</h4></div></div></div>
<p>
          Asio contains classes to allow asynchronous read and write operations to
          be performed on Windows <code class="computeroutput">HANDLE</code>s, such as named pipes.
        </p>
<p>
          For example, to perform asynchronous operations on a named pipe, the following
          object may be created:
        </p>
<pre class="programlisting">HANDLE handle = ::CreateFile(...);
windows::stream_handle pipe(my_io_context, handle);
</pre>
<p>
          These are then used as synchronous or asynchronous read and write streams.
          This means the objects can be used with any of the <a class="link" href="../../reference/read.html" title="read">read()</a>,
          <a class="link" href="../../reference/async_read.html" title="async_read">async_read()</a>, <a class="link" href="../../reference/write.html" title="write">write()</a>,
          <a class="link" href="../../reference/async_write.html" title="async_write">async_write()</a>, <a class="link" href="../../reference/read_until.html" title="read_until">read_until()</a> or <a class="link" href="../../reference/async_read_until.html" title="async_read_until">async_read_until()</a>
          free functions.
        </p>
<p>
          The kernel object referred to by the <code class="computeroutput">HANDLE</code> must support use
          with I/O completion ports (which means that named pipes are supported,
          but anonymous pipes and console streams are not).
        </p>
<h6>
<a name="asio.overview.windows.stream_handle.h0"></a>
          <span><a name="asio.overview.windows.stream_handle.see_also"></a></span><a class="link" href="stream_handle.html#asio.overview.windows.stream_handle.see_also">See
          Also</a>
        </h6>
<p>
          <a class="link" href="../../reference/windows__stream_handle.html" title="windows::stream_handle">windows::stream_handle</a>.
        </p>
<h6>
<a name="asio.overview.windows.stream_handle.h1"></a>
          <span><a name="asio.overview.windows.stream_handle.notes"></a></span><a class="link" href="stream_handle.html#asio.overview.windows.stream_handle.notes">Notes</a>
        </h6>
<p>
          Windows stream <code class="computeroutput">HANDLE</code>s are only available at compile time
          when targeting Windows and only when the I/O completion port backend is
          used (which is the default). A program may test for the macro <code class="computeroutput">ASIO_HAS_WINDOWS_STREAM_HANDLE</code>
          to determine whether they are supported.
        </p>
</div>
<table xmlns:rev="http://www.cs.rpi.edu/~gregod/boost/tools/doc/revision" width="100%"><tr>
<td align="left"></td>
<td align="right"><div class="copyright-footer">Copyright © 2003-2021 Christopher
      M. Kohlhoff<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="../windows.html"><img src="../../../prev.png" alt="Prev"></a><a accesskey="u" href="../windows.html"><img src="../../../up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../home.png" alt="Home"></a><a accesskey="n" href="random_access_handle.html"><img src="../../../next.png" alt="Next"></a>
</div>
</body>
</html>
