<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>async_read_at (1 of 4 overloads)</title>
<link rel="stylesheet" href="../../../boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.75.2">
<link rel="home" href="../../../index.html" title="Asio">
<link rel="up" href="../async_read_at.html" title="async_read_at">
<link rel="prev" href="../async_read_at.html" title="async_read_at">
<link rel="next" href="overload2.html" title="async_read_at (2 of 4 overloads)">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr><td valign="top"><img alt="asio C++ library" width="250" height="60" src="../../../asio.png"></td></tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="../async_read_at.html"><img src="../../../prev.png" alt="Prev"></a><a accesskey="u" href="../async_read_at.html"><img src="../../../up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../home.png" alt="Home"></a><a accesskey="n" href="overload2.html"><img src="../../../next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="asio.reference.async_read_at.overload1"></a><a class="link" href="overload1.html" title="async_read_at (1 of 4 overloads)">async_read_at
        (1 of 4 overloads)</a>
</h4></div></div></div>
<p>
          Start an asynchronous operation to read a certain amount of data at the
          specified offset.
        </p>
<pre class="programlisting">template&lt;
    typename <a class="link" href="../AsyncRandomAccessReadDevice.html" title="Buffer-oriented asynchronous random-access read device requirements">AsyncRandomAccessReadDevice</a>,
    typename <a class="link" href="../MutableBufferSequence.html" title="Mutable buffer sequence requirements">MutableBufferSequence</a>,
    typename <a class="link" href="../ReadHandler.html" title="Read handler requirements">ReadHandler</a> = <a class="link" href="../asynchronous_operations.html#asio.reference.asynchronous_operations.default_completion_tokens"><span class="emphasis"><em>DEFAULT</em></span></a>&gt;
<a class="link" href="../asynchronous_operations.html#asio.reference.asynchronous_operations.automatic_deduction_of_initiating_function_return_type"><span class="emphasis"><em>DEDUCED</em></span></a> async_read_at(
    AsyncRandomAccessReadDevice &amp; d,
    uint64_t offset,
    const MutableBufferSequence &amp; buffers,
    ReadHandler &amp;&amp; handler = <a class="link" href="../asynchronous_operations.html#asio.reference.asynchronous_operations.default_completion_tokens"><span class="emphasis"><em>DEFAULT</em></span></a>);
</pre>
<p>
          This function is used to asynchronously read a certain number of bytes
          of data from a random access device at the specified offset. The function
          call always returns immediately. The asynchronous operation will continue
          until one of the following conditions is true:
        </p>
<div class="itemizedlist"><ul class="itemizedlist" type="disc">
<li class="listitem">
              The supplied buffers are full. That is, the bytes transferred is equal
              to the sum of the buffer sizes.
            </li>
<li class="listitem">
              An error occurred.
            </li>
</ul></div>
<p>
          This operation is implemented in terms of zero or more calls to the device's
          async_read_some_at function.
        </p>
<h6>
<a name="asio.reference.async_read_at.overload1.h0"></a>
          <span><a name="asio.reference.async_read_at.overload1.parameters"></a></span><a class="link" href="overload1.html#asio.reference.async_read_at.overload1.parameters">Parameters</a>
        </h6>
<div class="variablelist">
<p class="title"><b></b></p>
<dl>
<dt><span class="term">d</span></dt>
<dd><p>
                The device from which the data is to be read. The type must support
                the AsyncRandomAccessReadDevice concept.
              </p></dd>
<dt><span class="term">offset</span></dt>
<dd><p>
                The offset at which the data will be read.
              </p></dd>
<dt><span class="term">buffers</span></dt>
<dd><p>
                One or more buffers into which the data will be read. The sum of
                the buffer sizes indicates the maximum number of bytes to read from
                the device. Although the buffers object may be copied as necessary,
                ownership of the underlying memory blocks is retained by the caller,
                which must guarantee that they remain valid until the handler is
                called.
              </p></dd>
<dt><span class="term">handler</span></dt>
<dd>
<p>
                The handler to be called when the read operation completes. Copies
                will be made of the handler as required. The function signature of
                the handler must be:
</p>
<pre class="programlisting">void handler(
  // Result of operation.
  const asio::error_code&amp; error,

  // Number of bytes copied into the buffers. If an error
  // occurred, this will be the number of bytes successfully
  // transferred prior to the error.
  std::size_t bytes_transferred
);
</pre>
<p>
                Regardless of whether the asynchronous operation completes immediately
                or not, the handler will not be invoked from within this function.
                On immediate completion, invocation of the handler will be performed
                in a manner equivalent to using <a class="link" href="../post.html" title="post"><code class="computeroutput">post</code></a>.
              </p>
</dd>
</dl>
</div>
<h6>
<a name="asio.reference.async_read_at.overload1.h1"></a>
          <span><a name="asio.reference.async_read_at.overload1.example"></a></span><a class="link" href="overload1.html#asio.reference.async_read_at.overload1.example">Example</a>
        </h6>
<p>
          To read into a single data buffer use the <a class="link" href="../buffer.html" title="buffer"><code class="computeroutput">buffer</code></a>
          function as follows:
        </p>
<pre class="programlisting">asio::async_read_at(d, 42, asio::buffer(data, size), handler);
</pre>
<p>
          See the <a class="link" href="../buffer.html" title="buffer"><code class="computeroutput">buffer</code></a>
          documentation for information on reading into multiple buffers in one go,
          and how to use it with arrays, boost::array or std::vector.
        </p>
<h6>
<a name="asio.reference.async_read_at.overload1.h2"></a>
          <span><a name="asio.reference.async_read_at.overload1.remarks"></a></span><a class="link" href="overload1.html#asio.reference.async_read_at.overload1.remarks">Remarks</a>
        </h6>
<p>
          This overload is equivalent to calling:
        </p>
<pre class="programlisting">asio::async_read_at(
    d, 42, buffers,
    asio::transfer_all(),
    handler);
</pre>
</div>
<table xmlns:rev="http://www.cs.rpi.edu/~gregod/boost/tools/doc/revision" width="100%"><tr>
<td align="left"></td>
<td align="right"><div class="copyright-footer">Copyright © 2003-2021 Christopher
      M. Kohlhoff<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="../async_read_at.html"><img src="../../../prev.png" alt="Prev"></a><a accesskey="u" href="../async_read_at.html"><img src="../../../up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../home.png" alt="Home"></a><a accesskey="n" href="overload2.html"><img src="../../../next.png" alt="Next"></a>
</div>
</body>
</html>
