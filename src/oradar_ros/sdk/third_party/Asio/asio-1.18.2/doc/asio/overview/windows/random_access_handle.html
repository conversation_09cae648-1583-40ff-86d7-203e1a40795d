<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Random-Access HANDLEs</title>
<link rel="stylesheet" href="../../../boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.75.2">
<link rel="home" href="../../../index.html" title="Asio">
<link rel="up" href="../windows.html" title="Windows-Specific Functionality">
<link rel="prev" href="stream_handle.html" title="Stream-Oriented HANDLEs">
<link rel="next" href="object_handle.html" title="Object HANDLEs">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr><td valign="top"><img alt="asio C++ library" width="250" height="60" src="../../../asio.png"></td></tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="stream_handle.html"><img src="../../../prev.png" alt="Prev"></a><a accesskey="u" href="../windows.html"><img src="../../../up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../home.png" alt="Home"></a><a accesskey="n" href="object_handle.html"><img src="../../../next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="asio.overview.windows.random_access_handle"></a><a class="link" href="random_access_handle.html" title="Random-Access HANDLEs">Random-Access
        HANDLEs</a>
</h4></div></div></div>
<p>
          Asio provides Windows-specific classes that permit asynchronous read and
          write operations to be performed on HANDLEs that refer to regular files.
        </p>
<p>
          For example, to perform asynchronous operations on a file the following
          object may be created:
        </p>
<pre class="programlisting">HANDLE handle = ::CreateFile(...);
windows::random_access_handle file(my_io_context, handle);
</pre>
<p>
          Data may be read from or written to the handle using one of the <code class="computeroutput">read_some_at()</code>,
          <code class="computeroutput">async_read_some_at()</code>, <code class="computeroutput">write_some_at()</code> or <code class="computeroutput">async_write_some_at()</code>
          member functions. However, like the equivalent functions (<code class="computeroutput">read_some()</code>,
          etc.) on streams, these functions are only required to transfer one or
          more bytes in a single operation. Therefore free functions called <a class="link" href="../../reference/read_at.html" title="read_at">read_at()</a>, <a class="link" href="../../reference/async_read_at.html" title="async_read_at">async_read_at()</a>,
          <a class="link" href="../../reference/write_at.html" title="write_at">write_at()</a> and <a class="link" href="../../reference/async_write_at.html" title="async_write_at">async_write_at()</a>
          have been created to repeatedly call the corresponding <code class="literal"><span class="bold"><strong>*</strong></span>_some_at()</code> function until all data has
          been transferred.
        </p>
<h6>
<a name="asio.overview.windows.random_access_handle.h0"></a>
          <span><a name="asio.overview.windows.random_access_handle.see_also"></a></span><a class="link" href="random_access_handle.html#asio.overview.windows.random_access_handle.see_also">See Also</a>
        </h6>
<p>
          <a class="link" href="../../reference/windows__random_access_handle.html" title="windows::random_access_handle">windows::random_access_handle</a>.
        </p>
<h6>
<a name="asio.overview.windows.random_access_handle.h1"></a>
          <span><a name="asio.overview.windows.random_access_handle.notes"></a></span><a class="link" href="random_access_handle.html#asio.overview.windows.random_access_handle.notes">Notes</a>
        </h6>
<p>
          Windows random-access <code class="computeroutput">HANDLE</code>s are only available at compile
          time when targeting Windows and only when the I/O completion port backend
          is used (which is the default). A program may test for the macro <code class="computeroutput">ASIO_HAS_WINDOWS_RANDOM_ACCESS_HANDLE</code>
          to determine whether they are supported.
        </p>
</div>
<table xmlns:rev="http://www.cs.rpi.edu/~gregod/boost/tools/doc/revision" width="100%"><tr>
<td align="left"></td>
<td align="right"><div class="copyright-footer">Copyright © 2003-2021 Christopher
      M. Kohlhoff<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="stream_handle.html"><img src="../../../prev.png" alt="Prev"></a><a accesskey="u" href="../windows.html"><img src="../../../up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../home.png" alt="Home"></a><a accesskey="n" href="object_handle.html"><img src="../../../next.png" alt="Next"></a>
</div>
</body>
</html>
