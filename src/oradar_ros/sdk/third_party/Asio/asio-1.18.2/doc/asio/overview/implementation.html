<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Platform-Specific Implementation Notes</title>
<link rel="stylesheet" href="../../boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.75.2">
<link rel="home" href="../../index.html" title="Asio">
<link rel="up" href="../overview.html" title="Overview">
<link rel="prev" href="cpp2011/futures.html" title="Futures">
<link rel="next" href="../using.html" title="Using, Building, and Configuring Asio">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr><td valign="top"><img alt="asio C++ library" width="250" height="60" src="../../asio.png"></td></tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="cpp2011/futures.html"><img src="../../prev.png" alt="Prev"></a><a accesskey="u" href="../overview.html"><img src="../../up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../home.png" alt="Home"></a><a accesskey="n" href="../using.html"><img src="../../next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="asio.overview.implementation"></a><a class="link" href="implementation.html" title="Platform-Specific Implementation Notes">Platform-Specific Implementation
      Notes</a>
</h3></div></div></div>
<p>
        This section lists platform-specific implementation details, such as the
        default demultiplexing mechanism, the number of threads created internally,
        and when threads are created.
      </p>
<h5>
<a name="asio.overview.implementation.h0"></a>
        <span><a name="asio.overview.implementation.linux_kernel_2_4"></a></span><a class="link" href="implementation.html#asio.overview.implementation.linux_kernel_2_4">Linux
        Kernel 2.4</a>
      </h5>
<p>
        Demultiplexing mechanism:
      </p>
<div class="itemizedlist"><ul class="itemizedlist" type="disc"><li class="listitem">
            Uses <code class="computeroutput">select</code> for demultiplexing. This means that the number
            of file descriptors in the process cannot be permitted to exceed <code class="computeroutput">FD_SETSIZE</code>.
          </li></ul></div>
<p>
        Threads:
      </p>
<div class="itemizedlist"><ul class="itemizedlist" type="disc">
<li class="listitem">
            Demultiplexing using <code class="computeroutput">select</code> is performed in one of the threads
            that calls <code class="computeroutput">io_context::run()</code>, <code class="computeroutput">io_context::run_one()</code>,
            <code class="computeroutput">io_context::poll()</code> or <code class="computeroutput">io_context::poll_one()</code>.
          </li>
<li class="listitem">
            An additional thread per <code class="computeroutput">io_context</code> is used to emulate asynchronous
            host resolution. This thread is created on the first call to either
            <code class="computeroutput">ip::tcp::resolver::async_resolve()</code> or <code class="computeroutput">ip::udp::resolver::async_resolve()</code>.
          </li>
</ul></div>
<p>
        Scatter-Gather:
      </p>
<div class="itemizedlist"><ul class="itemizedlist" type="disc"><li class="listitem">
            At most <code class="computeroutput">min(64,IOV_MAX)</code> buffers may be transferred in a
            single operation.
          </li></ul></div>
<h5>
<a name="asio.overview.implementation.h1"></a>
        <span><a name="asio.overview.implementation.linux_kernel_2_6"></a></span><a class="link" href="implementation.html#asio.overview.implementation.linux_kernel_2_6">Linux
        Kernel 2.6</a>
      </h5>
<p>
        Demultiplexing mechanism:
      </p>
<div class="itemizedlist"><ul class="itemizedlist" type="disc"><li class="listitem">
            Uses <code class="computeroutput">epoll</code> for demultiplexing.
          </li></ul></div>
<p>
        Threads:
      </p>
<div class="itemizedlist"><ul class="itemizedlist" type="disc">
<li class="listitem">
            Demultiplexing using <code class="computeroutput">epoll</code> is performed in one of the threads
            that calls <code class="computeroutput">io_context::run()</code>, <code class="computeroutput">io_context::run_one()</code>,
            <code class="computeroutput">io_context::poll()</code> or <code class="computeroutput">io_context::poll_one()</code>.
          </li>
<li class="listitem">
            An additional thread per <code class="computeroutput">io_context</code> is used to emulate asynchronous
            host resolution. This thread is created on the first call to either
            <code class="computeroutput">ip::tcp::resolver::async_resolve()</code> or <code class="computeroutput">ip::udp::resolver::async_resolve()</code>.
          </li>
</ul></div>
<p>
        Scatter-Gather:
      </p>
<div class="itemizedlist"><ul class="itemizedlist" type="disc"><li class="listitem">
            At most <code class="computeroutput">min(64,IOV_MAX)</code> buffers may be transferred in a
            single operation.
          </li></ul></div>
<h5>
<a name="asio.overview.implementation.h2"></a>
        <span><a name="asio.overview.implementation.solaris"></a></span><a class="link" href="implementation.html#asio.overview.implementation.solaris">Solaris</a>
      </h5>
<p>
        Demultiplexing mechanism:
      </p>
<div class="itemizedlist"><ul class="itemizedlist" type="disc"><li class="listitem">
            Uses <code class="literal">/dev/poll</code> for demultiplexing.
          </li></ul></div>
<p>
        Threads:
      </p>
<div class="itemizedlist"><ul class="itemizedlist" type="disc">
<li class="listitem">
            Demultiplexing using <code class="literal">/dev/poll</code> is performed in one
            of the threads that calls <code class="computeroutput">io_context::run()</code>, <code class="computeroutput">io_context::run_one()</code>,
            <code class="computeroutput">io_context::poll()</code> or <code class="computeroutput">io_context::poll_one()</code>.
          </li>
<li class="listitem">
            An additional thread per <code class="computeroutput">io_context</code> is used to emulate asynchronous
            host resolution. This thread is created on the first call to either
            <code class="computeroutput">ip::tcp::resolver::async_resolve()</code> or <code class="computeroutput">ip::udp::resolver::async_resolve()</code>.
          </li>
</ul></div>
<p>
        Scatter-Gather:
      </p>
<div class="itemizedlist"><ul class="itemizedlist" type="disc"><li class="listitem">
            At most <code class="computeroutput">min(64,IOV_MAX)</code> buffers may be transferred in a
            single operation.
          </li></ul></div>
<h5>
<a name="asio.overview.implementation.h3"></a>
        <span><a name="asio.overview.implementation.qnx_neutrino"></a></span><a class="link" href="implementation.html#asio.overview.implementation.qnx_neutrino">QNX
        Neutrino</a>
      </h5>
<p>
        Demultiplexing mechanism:
      </p>
<div class="itemizedlist"><ul class="itemizedlist" type="disc"><li class="listitem">
            Uses <code class="computeroutput">select</code> for demultiplexing. This means that the number
            of file descriptors in the process cannot be permitted to exceed <code class="computeroutput">FD_SETSIZE</code>.
          </li></ul></div>
<p>
        Threads:
      </p>
<div class="itemizedlist"><ul class="itemizedlist" type="disc">
<li class="listitem">
            Demultiplexing using <code class="computeroutput">select</code> is performed in one of the threads
            that calls <code class="computeroutput">io_context::run()</code>, <code class="computeroutput">io_context::run_one()</code>,
            <code class="computeroutput">io_context::poll()</code> or <code class="computeroutput">io_context::poll_one()</code>.
          </li>
<li class="listitem">
            An additional thread per <code class="computeroutput">io_context</code> is used to emulate asynchronous
            host resolution. This thread is created on the first call to either
            <code class="computeroutput">ip::tcp::resolver::async_resolve()</code> or <code class="computeroutput">ip::udp::resolver::async_resolve()</code>.
          </li>
</ul></div>
<p>
        Scatter-Gather:
      </p>
<div class="itemizedlist"><ul class="itemizedlist" type="disc"><li class="listitem">
            At most <code class="computeroutput">min(64,IOV_MAX)</code> buffers may be transferred in a
            single operation.
          </li></ul></div>
<h5>
<a name="asio.overview.implementation.h4"></a>
        <span><a name="asio.overview.implementation.mac_os_x"></a></span><a class="link" href="implementation.html#asio.overview.implementation.mac_os_x">Mac
        OS X</a>
      </h5>
<p>
        Demultiplexing mechanism:
      </p>
<div class="itemizedlist"><ul class="itemizedlist" type="disc"><li class="listitem">
            Uses <code class="computeroutput">kqueue</code> for demultiplexing.
          </li></ul></div>
<p>
        Threads:
      </p>
<div class="itemizedlist"><ul class="itemizedlist" type="disc">
<li class="listitem">
            Demultiplexing using <code class="computeroutput">kqueue</code> is performed in one of the threads
            that calls <code class="computeroutput">io_context::run()</code>, <code class="computeroutput">io_context::run_one()</code>,
            <code class="computeroutput">io_context::poll()</code> or <code class="computeroutput">io_context::poll_one()</code>.
          </li>
<li class="listitem">
            An additional thread per <code class="computeroutput">io_context</code> is used to emulate asynchronous
            host resolution. This thread is created on the first call to either
            <code class="computeroutput">ip::tcp::resolver::async_resolve()</code> or <code class="computeroutput">ip::udp::resolver::async_resolve()</code>.
          </li>
</ul></div>
<p>
        Scatter-Gather:
      </p>
<div class="itemizedlist"><ul class="itemizedlist" type="disc"><li class="listitem">
            At most <code class="computeroutput">min(64,IOV_MAX)</code> buffers may be transferred in a
            single operation.
          </li></ul></div>
<h5>
<a name="asio.overview.implementation.h5"></a>
        <span><a name="asio.overview.implementation.freebsd"></a></span><a class="link" href="implementation.html#asio.overview.implementation.freebsd">FreeBSD</a>
      </h5>
<p>
        Demultiplexing mechanism:
      </p>
<div class="itemizedlist"><ul class="itemizedlist" type="disc"><li class="listitem">
            Uses <code class="computeroutput">kqueue</code> for demultiplexing.
          </li></ul></div>
<p>
        Threads:
      </p>
<div class="itemizedlist"><ul class="itemizedlist" type="disc">
<li class="listitem">
            Demultiplexing using <code class="computeroutput">kqueue</code> is performed in one of the threads
            that calls <code class="computeroutput">io_context::run()</code>, <code class="computeroutput">io_context::run_one()</code>,
            <code class="computeroutput">io_context::poll()</code> or <code class="computeroutput">io_context::poll_one()</code>.
          </li>
<li class="listitem">
            An additional thread per <code class="computeroutput">io_context</code> is used to emulate asynchronous
            host resolution. This thread is created on the first call to either
            <code class="computeroutput">ip::tcp::resolver::async_resolve()</code> or <code class="computeroutput">ip::udp::resolver::async_resolve()</code>.
          </li>
</ul></div>
<p>
        Scatter-Gather:
      </p>
<div class="itemizedlist"><ul class="itemizedlist" type="disc"><li class="listitem">
            At most <code class="computeroutput">min(64,IOV_MAX)</code> buffers may be transferred in a
            single operation.
          </li></ul></div>
<h5>
<a name="asio.overview.implementation.h6"></a>
        <span><a name="asio.overview.implementation.aix"></a></span><a class="link" href="implementation.html#asio.overview.implementation.aix">AIX</a>
      </h5>
<p>
        Demultiplexing mechanism:
      </p>
<div class="itemizedlist"><ul class="itemizedlist" type="disc"><li class="listitem">
            Uses <code class="computeroutput">select</code> for demultiplexing. This means that the number
            of file descriptors in the process cannot be permitted to exceed <code class="computeroutput">FD_SETSIZE</code>.
          </li></ul></div>
<p>
        Threads:
      </p>
<div class="itemizedlist"><ul class="itemizedlist" type="disc">
<li class="listitem">
            Demultiplexing using <code class="computeroutput">select</code> is performed in one of the threads
            that calls <code class="computeroutput">io_context::run()</code>, <code class="computeroutput">io_context::run_one()</code>,
            <code class="computeroutput">io_context::poll()</code> or <code class="computeroutput">io_context::poll_one()</code>.
          </li>
<li class="listitem">
            An additional thread per <code class="computeroutput">io_context</code> is used to emulate asynchronous
            host resolution. This thread is created on the first call to either
            <code class="computeroutput">ip::tcp::resolver::async_resolve()</code> or <code class="computeroutput">ip::udp::resolver::async_resolve()</code>.
          </li>
</ul></div>
<p>
        Scatter-Gather:
      </p>
<div class="itemizedlist"><ul class="itemizedlist" type="disc"><li class="listitem">
            At most <code class="computeroutput">min(64,IOV_MAX)</code> buffers may be transferred in a
            single operation.
          </li></ul></div>
<h5>
<a name="asio.overview.implementation.h7"></a>
        <span><a name="asio.overview.implementation.hp_ux"></a></span><a class="link" href="implementation.html#asio.overview.implementation.hp_ux">HP-UX</a>
      </h5>
<p>
        Demultiplexing mechanism:
      </p>
<div class="itemizedlist"><ul class="itemizedlist" type="disc"><li class="listitem">
            Uses <code class="computeroutput">select</code> for demultiplexing. This means that the number
            of file descriptors in the process cannot be permitted to exceed <code class="computeroutput">FD_SETSIZE</code>.
          </li></ul></div>
<p>
        Threads:
      </p>
<div class="itemizedlist"><ul class="itemizedlist" type="disc">
<li class="listitem">
            Demultiplexing using <code class="computeroutput">select</code> is performed in one of the threads
            that calls <code class="computeroutput">io_context::run()</code>, <code class="computeroutput">io_context::run_one()</code>,
            <code class="computeroutput">io_context::poll()</code> or <code class="computeroutput">io_context::poll_one()</code>.
          </li>
<li class="listitem">
            An additional thread per <code class="computeroutput">io_context</code> is used to emulate asynchronous
            host resolution. This thread is created on the first call to either
            <code class="computeroutput">ip::tcp::resolver::async_resolve()</code> or <code class="computeroutput">ip::udp::resolver::async_resolve()</code>.
          </li>
</ul></div>
<p>
        Scatter-Gather:
      </p>
<div class="itemizedlist"><ul class="itemizedlist" type="disc"><li class="listitem">
            At most <code class="computeroutput">min(64,IOV_MAX)</code> buffers may be transferred in a
            single operation.
          </li></ul></div>
<h5>
<a name="asio.overview.implementation.h8"></a>
        <span><a name="asio.overview.implementation.tru64"></a></span><a class="link" href="implementation.html#asio.overview.implementation.tru64">Tru64</a>
      </h5>
<p>
        Demultiplexing mechanism:
      </p>
<div class="itemizedlist"><ul class="itemizedlist" type="disc"><li class="listitem">
            Uses <code class="computeroutput">select</code> for demultiplexing. This means that the number
            of file descriptors in the process cannot be permitted to exceed <code class="computeroutput">FD_SETSIZE</code>.
          </li></ul></div>
<p>
        Threads:
      </p>
<div class="itemizedlist"><ul class="itemizedlist" type="disc">
<li class="listitem">
            Demultiplexing using <code class="computeroutput">select</code> is performed in one of the threads
            that calls <code class="computeroutput">io_context::run()</code>, <code class="computeroutput">io_context::run_one()</code>,
            <code class="computeroutput">io_context::poll()</code> or <code class="computeroutput">io_context::poll_one()</code>.
          </li>
<li class="listitem">
            An additional thread per <code class="computeroutput">io_context</code> is used to emulate asynchronous
            host resolution. This thread is created on the first call to either
            <code class="computeroutput">ip::tcp::resolver::async_resolve()</code> or <code class="computeroutput">ip::udp::resolver::async_resolve()</code>.
          </li>
</ul></div>
<p>
        Scatter-Gather:
      </p>
<div class="itemizedlist"><ul class="itemizedlist" type="disc"><li class="listitem">
            At most <code class="computeroutput">min(64,IOV_MAX)</code> buffers may be transferred in a
            single operation.
          </li></ul></div>
<h5>
<a name="asio.overview.implementation.h9"></a>
        <span><a name="asio.overview.implementation.windows_95__98_and_me"></a></span><a class="link" href="implementation.html#asio.overview.implementation.windows_95__98_and_me">Windows
        95, 98 and Me</a>
      </h5>
<p>
        Demultiplexing mechanism:
      </p>
<div class="itemizedlist"><ul class="itemizedlist" type="disc"><li class="listitem">
            Uses <code class="computeroutput">select</code> for demultiplexing.
          </li></ul></div>
<p>
        Threads:
      </p>
<div class="itemizedlist"><ul class="itemizedlist" type="disc">
<li class="listitem">
            Demultiplexing using <code class="computeroutput">select</code> is performed in one of the threads
            that calls <code class="computeroutput">io_context::run()</code>, <code class="computeroutput">io_context::run_one()</code>,
            <code class="computeroutput">io_context::poll()</code> or <code class="computeroutput">io_context::poll_one()</code>.
          </li>
<li class="listitem">
            An additional thread per <code class="computeroutput">io_context</code> is used to emulate asynchronous
            host resolution. This thread is created on the first call to either
            <code class="computeroutput">ip::tcp::resolver::async_resolve()</code> or <code class="computeroutput">ip::udp::resolver::async_resolve()</code>.
          </li>
</ul></div>
<p>
        Scatter-Gather:
      </p>
<div class="itemizedlist"><ul class="itemizedlist" type="disc"><li class="listitem">
            For sockets, at most 16 buffers may be transferred in a single operation.
          </li></ul></div>
<h5>
<a name="asio.overview.implementation.h10"></a>
        <span><a name="asio.overview.implementation.windows_nt__2000__xp__2003__vista__7_and_8"></a></span><a class="link" href="implementation.html#asio.overview.implementation.windows_nt__2000__xp__2003__vista__7_and_8">Windows
        NT, 2000, XP, 2003, Vista, 7 and 8</a>
      </h5>
<p>
        Demultiplexing mechanism:
      </p>
<div class="itemizedlist"><ul class="itemizedlist" type="disc">
<li class="listitem">
            Uses overlapped I/O and I/O completion ports for all asynchronous socket
            operations except for asynchronous connect.
          </li>
<li class="listitem">
            Uses <code class="computeroutput">select</code> for emulating asynchronous connect.
          </li>
</ul></div>
<p>
        Threads:
      </p>
<div class="itemizedlist"><ul class="itemizedlist" type="disc">
<li class="listitem">
            Demultiplexing using I/O completion ports is performed in all threads
            that call <code class="computeroutput">io_context::run()</code>, <code class="computeroutput">io_context::run_one()</code>,
            <code class="computeroutput">io_context::poll()</code> or <code class="computeroutput">io_context::poll_one()</code>.
          </li>
<li class="listitem">
            An additional thread per <code class="computeroutput">io_context</code> is used to trigger timers.
            This thread is created on construction of the first <code class="computeroutput">basic_deadline_timer</code>
            or <code class="computeroutput">basic_waitable_timer</code> objects.
          </li>
<li class="listitem">
            An additional thread per <code class="computeroutput">io_context</code> may be used for <code class="computeroutput">select</code>
            demultiplexing. This thread is created on the first call to:
            <div class="itemizedlist"><ul class="itemizedlist" type="circle">
<li class="listitem">
                  A socket <code class="computeroutput">async_wait()</code> function, except when using
                  <code class="computeroutput">wait_read</code> on a stream-oriented socket. (For <code class="computeroutput">wait_read</code>
                  on a stream-oriented socket, the overlapped I/O operation <code class="computeroutput">WSARecv</code>
                  is used and no additional thread is required.)
                </li>
<li class="listitem">
                  A socket <code class="computeroutput">async_connect()</code> operation, if the overlapped
                  I/O operation <code class="computeroutput">ConnectEx</code> is unavailable. (On recent
                  versions of Windows, <code class="computeroutput">ConnectEx</code> is used and no additional
                  thread is required.)
                </li>
</ul></div>
          </li>
<li class="listitem">
            An additional thread per <code class="computeroutput">io_context</code> is used to emulate asynchronous
            host resolution. This thread is created on the first call to either
            <code class="computeroutput">ip::tcp::resolver::async_resolve()</code> or <code class="computeroutput">ip::udp::resolver::async_resolve()</code>.
          </li>
</ul></div>
<p>
        Scatter-Gather:
      </p>
<div class="itemizedlist"><ul class="itemizedlist" type="disc">
<li class="listitem">
            For sockets, at most 64 buffers may be transferred in a single operation.
          </li>
<li class="listitem">
            For stream-oriented handles, only one buffer may be transferred in a
            single operation.
          </li>
</ul></div>
<h5>
<a name="asio.overview.implementation.h11"></a>
        <span><a name="asio.overview.implementation.windows_runtime"></a></span><a class="link" href="implementation.html#asio.overview.implementation.windows_runtime">Windows
        Runtime</a>
      </h5>
<p>
        Asio provides limited support for the Windows Runtime. It requires that the
        language extensions be enabled. Due to the restricted facilities exposed
        by the Windows Runtime API, the support comes with the following caveats:
      </p>
<div class="itemizedlist"><ul class="itemizedlist" type="disc">
<li class="listitem">
            The core facilities such as the <code class="computeroutput">io_context</code>, <code class="computeroutput">strand</code>,
            buffers, composed operations, timers, etc., should all work as normal.
          </li>
<li class="listitem">
            For sockets, only client-side TCP is supported.
          </li>
<li class="listitem">
            Explicit binding of a client-side TCP socket is not supported.
          </li>
<li class="listitem">
            The <code class="computeroutput">cancel()</code> function is not supported for sockets. Asynchronous
            operations may only be cancelled by closing the socket.
          </li>
<li class="listitem">
            Operations that use <code class="computeroutput">null_buffers</code> are not supported.
          </li>
<li class="listitem">
            Only <code class="computeroutput">tcp::no_delay</code> and <code class="computeroutput">socket_base::keep_alive</code>
            options are supported.
          </li>
<li class="listitem">
            Resolvers do not support service names, only numbers. I.e. you must use
            "80" rather than "http".
          </li>
<li class="listitem">
            Most resolver query flags have no effect.
          </li>
</ul></div>
<p>
        Demultiplexing mechanism:
      </p>
<div class="itemizedlist"><ul class="itemizedlist" type="disc"><li class="listitem">
            Uses the <code class="computeroutput">Windows::Networking::Sockets::StreamSocket</code> class
            to implement asynchronous TCP socket operations.
          </li></ul></div>
<p>
        Threads:
      </p>
<div class="itemizedlist"><ul class="itemizedlist" type="disc">
<li class="listitem">
            Event completions are delivered to the Windows thread pool and posted
            to the <code class="computeroutput">io_context</code> for the handler to be executed.
          </li>
<li class="listitem">
            An additional thread per <code class="computeroutput">io_context</code> is used to trigger timers.
            This thread is created on construction of the first timer objects.
          </li>
</ul></div>
<p>
        Scatter-Gather:
      </p>
<div class="itemizedlist"><ul class="itemizedlist" type="disc"><li class="listitem">
            For sockets, at most one buffer may be transferred in a single operation.
          </li></ul></div>
</div>
<table xmlns:rev="http://www.cs.rpi.edu/~gregod/boost/tools/doc/revision" width="100%"><tr>
<td align="left"></td>
<td align="right"><div class="copyright-footer">Copyright © 2003-2021 Christopher
      M. Kohlhoff<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="cpp2011/futures.html"><img src="../../prev.png" alt="Prev"></a><a accesskey="u" href="../overview.html"><img src="../../up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../home.png" alt="Home"></a><a accesskey="n" href="../using.html"><img src="../../next.png" alt="Next"></a>
</div>
</body>
</html>
