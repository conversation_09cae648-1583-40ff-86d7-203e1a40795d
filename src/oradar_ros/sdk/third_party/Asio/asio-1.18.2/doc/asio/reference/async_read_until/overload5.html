<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>async_read_until (5 of 12 overloads)</title>
<link rel="stylesheet" href="../../../boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.75.2">
<link rel="home" href="../../../index.html" title="Asio">
<link rel="up" href="../async_read_until.html" title="async_read_until">
<link rel="prev" href="overload4.html" title="async_read_until (4 of 12 overloads)">
<link rel="next" href="overload6.html" title="async_read_until (6 of 12 overloads)">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr><td valign="top"><img alt="asio C++ library" width="250" height="60" src="../../../asio.png"></td></tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="overload4.html"><img src="../../../prev.png" alt="Prev"></a><a accesskey="u" href="../async_read_until.html"><img src="../../../up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../home.png" alt="Home"></a><a accesskey="n" href="overload6.html"><img src="../../../next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="asio.reference.async_read_until.overload5"></a><a class="link" href="overload5.html" title="async_read_until (5 of 12 overloads)">async_read_until
        (5 of 12 overloads)</a>
</h4></div></div></div>
<p>
          Start an asynchronous operation to read data into a streambuf until it
          contains a specified delimiter.
        </p>
<pre class="programlisting">template&lt;
    typename <a class="link" href="../AsyncReadStream.html" title="Buffer-oriented asynchronous read stream requirements">AsyncReadStream</a>,
    typename Allocator,
    typename <a class="link" href="../ReadHandler.html" title="Read handler requirements">ReadHandler</a> = <a class="link" href="../asynchronous_operations.html#asio.reference.asynchronous_operations.default_completion_tokens"><span class="emphasis"><em>DEFAULT</em></span></a>&gt;
<a class="link" href="../asynchronous_operations.html#asio.reference.asynchronous_operations.automatic_deduction_of_initiating_function_return_type"><span class="emphasis"><em>DEDUCED</em></span></a> async_read_until(
    AsyncReadStream &amp; s,
    asio::basic_streambuf&lt; Allocator &gt; &amp; b,
    char delim,
    ReadHandler &amp;&amp; handler = <a class="link" href="../asynchronous_operations.html#asio.reference.asynchronous_operations.default_completion_tokens"><span class="emphasis"><em>DEFAULT</em></span></a>);
</pre>
<p>
          This function is used to asynchronously read data into the specified streambuf
          until the streambuf's get area contains the specified delimiter. The function
          call always returns immediately. The asynchronous operation will continue
          until one of the following conditions is true:
        </p>
<div class="itemizedlist"><ul class="itemizedlist" type="disc">
<li class="listitem">
              The get area of the streambuf contains the specified delimiter.
            </li>
<li class="listitem">
              An error occurred.
            </li>
</ul></div>
<p>
          This operation is implemented in terms of zero or more calls to the stream's
          async_read_some function, and is known as a <span class="emphasis"><em>composed operation</em></span>.
          If the streambuf's get area already contains the delimiter, this asynchronous
          operation completes immediately. The program must ensure that the stream
          performs no other read operations (such as async_read, async_read_until,
          the stream's async_read_some function, or any other composed operations
          that perform reads) until this operation completes.
        </p>
<h6>
<a name="asio.reference.async_read_until.overload5.h0"></a>
          <span><a name="asio.reference.async_read_until.overload5.parameters"></a></span><a class="link" href="overload5.html#asio.reference.async_read_until.overload5.parameters">Parameters</a>
        </h6>
<div class="variablelist">
<p class="title"><b></b></p>
<dl>
<dt><span class="term">s</span></dt>
<dd><p>
                The stream from which the data is to be read. The type must support
                the AsyncReadStream concept.
              </p></dd>
<dt><span class="term">b</span></dt>
<dd><p>
                A streambuf object into which the data will be read. Ownership of
                the streambuf is retained by the caller, which must guarantee that
                it remains valid until the handler is called.
              </p></dd>
<dt><span class="term">delim</span></dt>
<dd><p>
                The delimiter character.
              </p></dd>
<dt><span class="term">handler</span></dt>
<dd>
<p>
                The handler to be called when the read operation completes. Copies
                will be made of the handler as required. The function signature of
                the handler must be:
</p>
<pre class="programlisting">void handler(
  // Result of operation.
  const asio::error_code&amp; error,

  // The number of bytes in the streambuf's get
  // area up to and including the delimiter.
  // 0 if an error occurred.
  std::size_t bytes_transferred
);
</pre>
<p>
                Regardless of whether the asynchronous operation completes immediately
                or not, the handler will not be invoked from within this function.
                On immediate completion, invocation of the handler will be performed
                in a manner equivalent to using <a class="link" href="../post.html" title="post"><code class="computeroutput">post</code></a>.
              </p>
</dd>
</dl>
</div>
<h6>
<a name="asio.reference.async_read_until.overload5.h1"></a>
          <span><a name="asio.reference.async_read_until.overload5.remarks"></a></span><a class="link" href="overload5.html#asio.reference.async_read_until.overload5.remarks">Remarks</a>
        </h6>
<p>
          After a successful async_read_until operation, the streambuf may contain
          additional data beyond the delimiter. An application will typically leave
          that data in the streambuf for a subsequent async_read_until operation
          to examine.
        </p>
<h6>
<a name="asio.reference.async_read_until.overload5.h2"></a>
          <span><a name="asio.reference.async_read_until.overload5.example"></a></span><a class="link" href="overload5.html#asio.reference.async_read_until.overload5.example">Example</a>
        </h6>
<p>
          To asynchronously read data into a streambuf until a newline is encountered:
        </p>
<pre class="programlisting">asio::streambuf b;
...
void handler(const asio::error_code&amp; e, std::size_t size)
{
  if (!e)
  {
    std::istream is(&amp;b);
    std::string line;
    std::getline(is, line);
    ...
  }
}
...
asio::async_read_until(s, b, '\n', handler);
</pre>
<p>
          After the <code class="computeroutput">async_read_until</code> operation completes successfully,
          the buffer <code class="computeroutput">b</code> contains the delimiter:
        </p>
<pre class="programlisting">{ 'a', 'b', ..., 'c', '\n', 'd', 'e', ... }
</pre>
<p>
          The call to <code class="computeroutput">std::getline</code> then extracts the data up to and
          including the newline (which is discarded), so that the string <code class="computeroutput">line</code>
          contains:
        </p>
<pre class="programlisting">{ 'a', 'b', ..., 'c' }
</pre>
<p>
          The remaining data is left in the buffer <code class="computeroutput">b</code> as follows:
        </p>
<pre class="programlisting">{ 'd', 'e', ... }
</pre>
<p>
          This data may be the start of a new line, to be extracted by a subsequent
          <code class="computeroutput">async_read_until</code> operation.
        </p>
</div>
<table xmlns:rev="http://www.cs.rpi.edu/~gregod/boost/tools/doc/revision" width="100%"><tr>
<td align="left"></td>
<td align="right"><div class="copyright-footer">Copyright © 2003-2021 Christopher
      M. Kohlhoff<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="overload4.html"><img src="../../../prev.png" alt="Prev"></a><a accesskey="u" href="../async_read_until.html"><img src="../../../up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../home.png" alt="Home"></a><a accesskey="n" href="overload6.html"><img src="../../../next.png" alt="Next"></a>
</div>
</body>
</html>
