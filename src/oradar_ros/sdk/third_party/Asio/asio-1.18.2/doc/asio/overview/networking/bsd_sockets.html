<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>The BSD Socket API and Asio</title>
<link rel="stylesheet" href="../../../boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.75.2">
<link rel="home" href="../../../index.html" title="Asio">
<link rel="up" href="../networking.html" title="Networking">
<link rel="prev" href="iostreams.html" title="Socket Iostreams">
<link rel="next" href="../timers.html" title="Timers">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr><td valign="top"><img alt="asio C++ library" width="250" height="60" src="../../../asio.png"></td></tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="iostreams.html"><img src="../../../prev.png" alt="Prev"></a><a accesskey="u" href="../networking.html"><img src="../../../up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../home.png" alt="Home"></a><a accesskey="n" href="../timers.html"><img src="../../../next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="asio.overview.networking.bsd_sockets"></a><a class="link" href="bsd_sockets.html" title="The BSD Socket API and Asio">The BSD Socket
        API and Asio</a>
</h4></div></div></div>
<p>
          The Asio library includes a low-level socket interface based on the BSD
          socket API, which is widely implemented and supported by extensive literature.
          It is also used as the basis for networking APIs in other languages, like
          Java. This low-level interface is designed to support the development of
          efficient and scalable applications. For example, it permits programmers
          to exert finer control over the number of system calls, avoid redundant
          data copying, minimise the use of resources like threads, and so on.
        </p>
<p>
          Unsafe and error prone aspects of the BSD socket API are not included.
          For example, the use of <code class="computeroutput">int</code> to represent all sockets lacks
          type safety. The socket representation in Asio uses a distinct type for
          each protocol, e.g. for TCP one would use <code class="computeroutput">ip::tcp::socket</code>,
          and for UDP one uses <code class="computeroutput">ip::udp::socket</code>.
        </p>
<p>
          The following table shows the mapping between the BSD socket API and Asio:
        </p>
<div class="informaltable"><table class="table">
<colgroup>
<col>
<col>
</colgroup>
<thead><tr>
<th>
                  <p>
                    BSD Socket API Elements
                  </p>
                </th>
<th>
                  <p>
                    Equivalents in Asio
                  </p>
                </th>
</tr></thead>
<tbody>
<tr>
<td>
                  <p>
                    socket descriptor - <code class="computeroutput">int</code> (POSIX) or <code class="computeroutput">SOCKET</code>
                    (Windows)
                  </p>
                </td>
<td>
                  <p>
                    For TCP: <a class="link" href="../../reference/ip__tcp/socket.html" title="ip::tcp::socket">ip::tcp::socket</a>,
                    <a class="link" href="../../reference/ip__tcp/acceptor.html" title="ip::tcp::acceptor">ip::tcp::acceptor</a>
                  </p>
                  <p>
                    For UDP: <a class="link" href="../../reference/ip__udp/socket.html" title="ip::udp::socket">ip::udp::socket</a>
                  </p>
                  <p>
                    <a class="link" href="../../reference/basic_socket.html" title="basic_socket">basic_socket</a>,
                    <a class="link" href="../../reference/basic_stream_socket.html" title="basic_stream_socket">basic_stream_socket</a>,
                    <a class="link" href="../../reference/basic_datagram_socket.html" title="basic_datagram_socket">basic_datagram_socket</a>,
                    <a class="link" href="../../reference/basic_raw_socket.html" title="basic_raw_socket">basic_raw_socket</a>
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <code class="computeroutput">in_addr</code>, <code class="computeroutput">in6_addr</code>
                  </p>
                </td>
<td>
                  <p>
                    <a class="link" href="../../reference/ip__address.html" title="ip::address">ip::address</a>,
                    <a class="link" href="../../reference/ip__address.html" title="ip::address">ip::address_v4</a>,
                    <a class="link" href="../../reference/ip__address.html" title="ip::address">ip::address_v6</a>
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <code class="computeroutput">sockaddr_in</code>, <code class="computeroutput">sockaddr_in6</code>
                  </p>
                </td>
<td>
                  <p>
                    For TCP: <a class="link" href="../../reference/ip__tcp/endpoint.html" title="ip::tcp::endpoint">ip::tcp::endpoint</a>
                  </p>
                  <p>
                    For UDP: <a class="link" href="../../reference/ip__udp/endpoint.html" title="ip::udp::endpoint">ip::udp::endpoint</a>
                  </p>
                  <p>
                    <a class="link" href="../../reference/ip__basic_endpoint.html" title="ip::basic_endpoint">ip::basic_endpoint</a>
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <code class="computeroutput">accept()</code>
                  </p>
                </td>
<td>
                  <p>
                    For TCP: <a class="link" href="../../reference/basic_socket_acceptor/accept.html" title="basic_socket_acceptor::accept">ip::tcp::acceptor::accept()</a>
                  </p>
                  <p>
                    <a class="link" href="../../reference/basic_socket_acceptor/accept.html" title="basic_socket_acceptor::accept">basic_socket_acceptor::accept()</a>
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <code class="computeroutput">bind()</code>
                  </p>
                </td>
<td>
                  <p>
                    For TCP: <a class="link" href="../../reference/basic_socket/bind.html" title="basic_socket::bind">ip::tcp::acceptor::bind()</a>,
                    <a class="link" href="../../reference/basic_socket/bind.html" title="basic_socket::bind">ip::tcp::socket::bind()</a>
                  </p>
                  <p>
                    For UDP: <a class="link" href="../../reference/basic_socket/bind.html" title="basic_socket::bind">ip::udp::socket::bind()</a>
                  </p>
                  <p>
                    <a class="link" href="../../reference/basic_socket/bind.html" title="basic_socket::bind">basic_socket::bind()</a>
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <code class="computeroutput">close()</code>
                  </p>
                </td>
<td>
                  <p>
                    For TCP: <a class="link" href="../../reference/basic_socket/close.html" title="basic_socket::close">ip::tcp::acceptor::close()</a>,
                    <a class="link" href="../../reference/basic_socket/close.html" title="basic_socket::close">ip::tcp::socket::close()</a>
                  </p>
                  <p>
                    For UDP: <a class="link" href="../../reference/basic_socket/close.html" title="basic_socket::close">ip::udp::socket::close()</a>
                  </p>
                  <p>
                    <a class="link" href="../../reference/basic_socket/close.html" title="basic_socket::close">basic_socket::close()</a>
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <code class="computeroutput">connect()</code>
                  </p>
                </td>
<td>
                  <p>
                    For TCP: <a class="link" href="../../reference/basic_socket/connect.html" title="basic_socket::connect">ip::tcp::socket::connect()</a>
                  </p>
                  <p>
                    For UDP: <a class="link" href="../../reference/basic_socket/connect.html" title="basic_socket::connect">ip::udp::socket::connect()</a>
                  </p>
                  <p>
                    <a class="link" href="../../reference/basic_socket/connect.html" title="basic_socket::connect">basic_socket::connect()</a>
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <code class="computeroutput">getaddrinfo()</code>, <code class="computeroutput">gethostbyaddr()</code>, <code class="computeroutput">gethostbyname()</code>,
                    <code class="computeroutput">getnameinfo()</code>, <code class="computeroutput">getservbyname()</code>, <code class="computeroutput">getservbyport()</code>
                  </p>
                </td>
<td>
                  <p>
                    For TCP: <a class="link" href="../../reference/ip__basic_resolver/resolve.html" title="ip::basic_resolver::resolve">ip::tcp::resolver::resolve()</a>,
                    <a class="link" href="../../reference/ip__basic_resolver/async_resolve.html" title="ip::basic_resolver::async_resolve">ip::tcp::resolver::async_resolve()</a>
                  </p>
                  <p>
                    For UDP: <a class="link" href="../../reference/ip__basic_resolver/resolve.html" title="ip::basic_resolver::resolve">ip::udp::resolver::resolve()</a>,
                    <a class="link" href="../../reference/ip__basic_resolver/async_resolve.html" title="ip::basic_resolver::async_resolve">ip::udp::resolver::async_resolve()</a>
                  </p>
                  <p>
                    <a class="link" href="../../reference/ip__basic_resolver/resolve.html" title="ip::basic_resolver::resolve">ip::basic_resolver::resolve()</a>,
                    <a class="link" href="../../reference/ip__basic_resolver/async_resolve.html" title="ip::basic_resolver::async_resolve">ip::basic_resolver::async_resolve()</a>
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <code class="computeroutput">gethostname()</code>
                  </p>
                </td>
<td>
                  <p>
                    <a class="link" href="../../reference/ip__host_name.html" title="ip::host_name">ip::host_name()</a>
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <code class="computeroutput">getpeername()</code>
                  </p>
                </td>
<td>
                  <p>
                    For TCP: <a class="link" href="../../reference/basic_socket/remote_endpoint.html" title="basic_socket::remote_endpoint">ip::tcp::socket::remote_endpoint()</a>
                  </p>
                  <p>
                    For UDP: <a class="link" href="../../reference/basic_socket/remote_endpoint.html" title="basic_socket::remote_endpoint">ip::udp::socket::remote_endpoint()</a>
                  </p>
                  <p>
                    <a class="link" href="../../reference/basic_socket/remote_endpoint.html" title="basic_socket::remote_endpoint">basic_socket::remote_endpoint()</a>
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <code class="computeroutput">getsockname()</code>
                  </p>
                </td>
<td>
                  <p>
                    For TCP: <a class="link" href="../../reference/basic_socket/local_endpoint.html" title="basic_socket::local_endpoint">ip::tcp::acceptor::local_endpoint()</a>,
                    <a class="link" href="../../reference/basic_socket/local_endpoint.html" title="basic_socket::local_endpoint">ip::tcp::socket::local_endpoint()</a>
                  </p>
                  <p>
                    For UDP: <a class="link" href="../../reference/basic_socket/local_endpoint.html" title="basic_socket::local_endpoint">ip::udp::socket::local_endpoint()</a>
                  </p>
                  <p>
                    <a class="link" href="../../reference/basic_socket/local_endpoint.html" title="basic_socket::local_endpoint">basic_socket::local_endpoint()</a>
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <code class="computeroutput">getsockopt()</code>
                  </p>
                </td>
<td>
                  <p>
                    For TCP: <a class="link" href="../../reference/basic_socket/get_option.html" title="basic_socket::get_option">ip::tcp::acceptor::get_option()</a>,
                    <a class="link" href="../../reference/basic_socket/get_option.html" title="basic_socket::get_option">ip::tcp::socket::get_option()</a>
                  </p>
                  <p>
                    For UDP: <a class="link" href="../../reference/basic_socket/get_option.html" title="basic_socket::get_option">ip::udp::socket::get_option()</a>
                  </p>
                  <p>
                    <a class="link" href="../../reference/basic_socket/get_option.html" title="basic_socket::get_option">basic_socket::get_option()</a>
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <code class="computeroutput">inet_addr()</code>, <code class="computeroutput">inet_aton()</code>, <code class="computeroutput">inet_pton()</code>
                  </p>
                </td>
<td>
                  <p>
                    <a class="link" href="../../reference/ip__address/from_string.html" title="ip::address::from_string">ip::address::from_string()</a>,
                    <a class="link" href="../../reference/ip__address/from_string.html" title="ip::address::from_string">ip::address_v4::from_string()</a>,
                    <a class="link" href="../../reference/ip__address/from_string.html" title="ip::address::from_string">ip_address_v6::from_string()</a>
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <code class="computeroutput">inet_ntoa()</code>, <code class="computeroutput">inet_ntop()</code>
                  </p>
                </td>
<td>
                  <p>
                    <a class="link" href="../../reference/ip__address/to_string.html" title="ip::address::to_string">ip::address::to_string()</a>,
                    <a class="link" href="../../reference/ip__address/to_string.html" title="ip::address::to_string">ip::address_v4::to_string()</a>,
                    <a class="link" href="../../reference/ip__address/to_string.html" title="ip::address::to_string">ip_address_v6::to_string()</a>
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <code class="computeroutput">ioctl()</code>
                  </p>
                </td>
<td>
                  <p>
                    For TCP: <a class="link" href="../../reference/basic_socket/io_control.html" title="basic_socket::io_control">ip::tcp::socket::io_control()</a>
                  </p>
                  <p>
                    For UDP: <a class="link" href="../../reference/basic_socket/io_control.html" title="basic_socket::io_control">ip::udp::socket::io_control()</a>
                  </p>
                  <p>
                    <a class="link" href="../../reference/basic_socket/io_control.html" title="basic_socket::io_control">basic_socket::io_control()</a>
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <code class="computeroutput">listen()</code>
                  </p>
                </td>
<td>
                  <p>
                    For TCP: <a class="link" href="../../reference/basic_socket_acceptor/listen.html" title="basic_socket_acceptor::listen">ip::tcp::acceptor::listen()</a>
                  </p>
                  <p>
                    <a class="link" href="../../reference/basic_socket_acceptor/listen.html" title="basic_socket_acceptor::listen">basic_socket_acceptor::listen()</a>
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <code class="computeroutput">poll()</code>, <code class="computeroutput">select()</code>, <code class="computeroutput">pselect()</code>
                  </p>
                </td>
<td>
                  <p>
                    <a class="link" href="../../reference/io_context/run.html" title="io_context::run">io_context::run()</a>,
                    <a class="link" href="../../reference/io_context/run_one.html" title="io_context::run_one">io_context::run_one()</a>,
                    <a class="link" href="../../reference/io_context/poll.html" title="io_context::poll">io_context::poll()</a>,
                    <a class="link" href="../../reference/io_context/poll_one.html" title="io_context::poll_one">io_context::poll_one()</a>
                  </p>
                  <p>
                    Note: in conjunction with asynchronous operations.
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <code class="computeroutput">readv()</code>, <code class="computeroutput">recv()</code>, <code class="computeroutput">read()</code>
                  </p>
                </td>
<td>
                  <p>
                    For TCP: <a class="link" href="../../reference/basic_stream_socket/read_some.html" title="basic_stream_socket::read_some">ip::tcp::socket::read_some()</a>,
                    <a class="link" href="../../reference/basic_stream_socket/async_read_some.html" title="basic_stream_socket::async_read_some">ip::tcp::socket::async_read_some()</a>,
                    <a class="link" href="../../reference/basic_stream_socket/receive.html" title="basic_stream_socket::receive">ip::tcp::socket::receive()</a>,
                    <a class="link" href="../../reference/basic_stream_socket/async_receive.html" title="basic_stream_socket::async_receive">ip::tcp::socket::async_receive()</a>
                  </p>
                  <p>
                    For UDP: <a class="link" href="../../reference/basic_datagram_socket/receive.html" title="basic_datagram_socket::receive">ip::udp::socket::receive()</a>,
                    <a class="link" href="../../reference/basic_datagram_socket/async_receive.html" title="basic_datagram_socket::async_receive">ip::udp::socket::async_receive()</a>
                  </p>
                  <p>
                    <a class="link" href="../../reference/basic_stream_socket/read_some.html" title="basic_stream_socket::read_some">basic_stream_socket::read_some()</a>,
                    <a class="link" href="../../reference/basic_stream_socket/async_read_some.html" title="basic_stream_socket::async_read_some">basic_stream_socket::async_read_some()</a>,
                    <a class="link" href="../../reference/basic_stream_socket/receive.html" title="basic_stream_socket::receive">basic_stream_socket::receive()</a>,
                    <a class="link" href="../../reference/basic_stream_socket/async_receive.html" title="basic_stream_socket::async_receive">basic_stream_socket::async_receive()</a>,
                    <a class="link" href="../../reference/basic_datagram_socket/receive.html" title="basic_datagram_socket::receive">basic_datagram_socket::receive()</a>,
                    <a class="link" href="../../reference/basic_datagram_socket/async_receive.html" title="basic_datagram_socket::async_receive">basic_datagram_socket::async_receive()</a>
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <code class="computeroutput">recvfrom()</code>
                  </p>
                </td>
<td>
                  <p>
                    For UDP: <a class="link" href="../../reference/basic_datagram_socket/receive_from.html" title="basic_datagram_socket::receive_from">ip::udp::socket::receive_from()</a>,
                    <a class="link" href="../../reference/basic_datagram_socket/async_receive_from.html" title="basic_datagram_socket::async_receive_from">ip::udp::socket::async_receive_from()</a>
                  </p>
                  <p>
                    <a class="link" href="../../reference/basic_datagram_socket/receive_from.html" title="basic_datagram_socket::receive_from">basic_datagram_socket::receive_from()</a>,
                    <a class="link" href="../../reference/basic_datagram_socket/async_receive_from.html" title="basic_datagram_socket::async_receive_from">basic_datagram_socket::async_receive_from()</a>
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <code class="computeroutput">send()</code>, <code class="computeroutput">write()</code>, <code class="computeroutput">writev()</code>
                  </p>
                </td>
<td>
                  <p>
                    For TCP: <a class="link" href="../../reference/basic_stream_socket/write_some.html" title="basic_stream_socket::write_some">ip::tcp::socket::write_some()</a>,
                    <a class="link" href="../../reference/basic_stream_socket/async_write_some.html" title="basic_stream_socket::async_write_some">ip::tcp::socket::async_write_some()</a>,
                    <a class="link" href="../../reference/basic_stream_socket/send.html" title="basic_stream_socket::send">ip::tcp::socket::send()</a>,
                    <a class="link" href="../../reference/basic_stream_socket/async_send.html" title="basic_stream_socket::async_send">ip::tcp::socket::async_send()</a>
                  </p>
                  <p>
                    For UDP: <a class="link" href="../../reference/basic_datagram_socket/send.html" title="basic_datagram_socket::send">ip::udp::socket::send()</a>,
                    <a class="link" href="../../reference/basic_datagram_socket/async_send.html" title="basic_datagram_socket::async_send">ip::udp::socket::async_send()</a>
                  </p>
                  <p>
                    <a class="link" href="../../reference/basic_stream_socket/write_some.html" title="basic_stream_socket::write_some">basic_stream_socket::write_some()</a>,
                    <a class="link" href="../../reference/basic_stream_socket/async_write_some.html" title="basic_stream_socket::async_write_some">basic_stream_socket::async_write_some()</a>,
                    <a class="link" href="../../reference/basic_stream_socket/send.html" title="basic_stream_socket::send">basic_stream_socket::send()</a>,
                    <a class="link" href="../../reference/basic_stream_socket/async_send.html" title="basic_stream_socket::async_send">basic_stream_socket::async_send()</a>,
                    <a class="link" href="../../reference/basic_datagram_socket/send.html" title="basic_datagram_socket::send">basic_datagram_socket::send()</a>,
                    <a class="link" href="../../reference/basic_datagram_socket/async_send.html" title="basic_datagram_socket::async_send">basic_datagram_socket::async_send()</a>
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <code class="computeroutput">sendto()</code>
                  </p>
                </td>
<td>
                  <p>
                    For UDP: <a class="link" href="../../reference/basic_datagram_socket/send_to.html" title="basic_datagram_socket::send_to">ip::udp::socket::send_to()</a>,
                    <a class="link" href="../../reference/basic_datagram_socket/async_send_to.html" title="basic_datagram_socket::async_send_to">ip::udp::socket::async_send_to()</a>
                  </p>
                  <p>
                    <a class="link" href="../../reference/basic_datagram_socket/send_to.html" title="basic_datagram_socket::send_to">basic_datagram_socket::send_to()</a>,
                    <a class="link" href="../../reference/basic_datagram_socket/async_send_to.html" title="basic_datagram_socket::async_send_to">basic_datagram_socket::async_send_to()</a>
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <code class="computeroutput">setsockopt()</code>
                  </p>
                </td>
<td>
                  <p>
                    For TCP: <a class="link" href="../../reference/basic_socket/set_option.html" title="basic_socket::set_option">ip::tcp::acceptor::set_option()</a>,
                    <a class="link" href="../../reference/basic_socket/set_option.html" title="basic_socket::set_option">ip::tcp::socket::set_option()</a>
                  </p>
                  <p>
                    For UDP: <a class="link" href="../../reference/basic_socket/set_option.html" title="basic_socket::set_option">ip::udp::socket::set_option()</a>
                  </p>
                  <p>
                    <a class="link" href="../../reference/basic_socket/set_option.html" title="basic_socket::set_option">basic_socket::set_option()</a>
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <code class="computeroutput">shutdown()</code>
                  </p>
                </td>
<td>
                  <p>
                    For TCP: <a class="link" href="../../reference/basic_socket/shutdown.html" title="basic_socket::shutdown">ip::tcp::socket::shutdown()</a>
                  </p>
                  <p>
                    For UDP: <a class="link" href="../../reference/basic_socket/shutdown.html" title="basic_socket::shutdown">ip::udp::socket::shutdown()</a>
                  </p>
                  <p>
                    <a class="link" href="../../reference/basic_socket/shutdown.html" title="basic_socket::shutdown">basic_socket::shutdown()</a>
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <code class="computeroutput">sockatmark()</code>
                  </p>
                </td>
<td>
                  <p>
                    For TCP: <a class="link" href="../../reference/basic_socket/at_mark.html" title="basic_socket::at_mark">ip::tcp::socket::at_mark()</a>
                  </p>
                  <p>
                    <a class="link" href="../../reference/basic_socket/at_mark.html" title="basic_socket::at_mark">basic_socket::at_mark()</a>
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <code class="computeroutput">socket()</code>
                  </p>
                </td>
<td>
                  <p>
                    For TCP: <a class="link" href="../../reference/basic_socket/open.html" title="basic_socket::open">ip::tcp::acceptor::open()</a>,
                    <a class="link" href="../../reference/basic_socket/open.html" title="basic_socket::open">ip::tcp::socket::open()</a>
                  </p>
                  <p>
                    For UDP: <a class="link" href="../../reference/basic_socket/open.html" title="basic_socket::open">ip::udp::socket::open()</a>
                  </p>
                  <p>
                    <a class="link" href="../../reference/basic_socket/open.html" title="basic_socket::open">basic_socket::open()</a>
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <code class="computeroutput">socketpair()</code>
                  </p>
                </td>
<td>
                  <p>
                    <a class="link" href="../../reference/local__connect_pair.html" title="local::connect_pair">local::connect_pair()</a>
                  </p>
                  <p>
                    Note: POSIX operating systems only.
                  </p>
                </td>
</tr>
</tbody>
</table></div>
</div>
<table xmlns:rev="http://www.cs.rpi.edu/~gregod/boost/tools/doc/revision" width="100%"><tr>
<td align="left"></td>
<td align="right"><div class="copyright-footer">Copyright © 2003-2021 Christopher
      M. Kohlhoff<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="iostreams.html"><img src="../../../prev.png" alt="Prev"></a><a accesskey="u" href="../networking.html"><img src="../../../up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../home.png" alt="Home"></a><a accesskey="n" href="../timers.html"><img src="../../../next.png" alt="Next"></a>
</div>
</body>
</html>
