<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Signal Handling</title>
<link rel="stylesheet" href="../../boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.75.2">
<link rel="home" href="../../index.html" title="Asio">
<link rel="up" href="../overview.html" title="Overview">
<link rel="prev" href="serial_ports.html" title="Serial Ports">
<link rel="next" href="posix.html" title="POSIX-Specific Functionality">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr><td valign="top"><img alt="asio C++ library" width="250" height="60" src="../../asio.png"></td></tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="serial_ports.html"><img src="../../prev.png" alt="Prev"></a><a accesskey="u" href="../overview.html"><img src="../../up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../home.png" alt="Home"></a><a accesskey="n" href="posix.html"><img src="../../next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="asio.overview.signals"></a><a class="link" href="signals.html" title="Signal Handling">Signal Handling</a>
</h3></div></div></div>
<p>
        Asio supports signal handling using a class called <a class="link" href="../reference/signal_set.html" title="signal_set">signal_set</a>.
        Programs may add one or more signals to the set, and then perform an <code class="computeroutput">async_wait()</code>
        operation. The specified handler will be called when one of the signals occurs.
        The same signal number may be registered with multiple <a class="link" href="../reference/signal_set.html" title="signal_set">signal_set</a>
        objects, however the signal number must be used only with Asio.
      </p>
<pre class="programlisting">void handler(
    const asio::error_code&amp; error,
    int signal_number)
{
  if (!error)
  {
    // A signal occurred.
  }
}

...

// Construct a signal set registered for process termination.
asio::signal_set signals(io_context, SIGINT, SIGTERM);

// Start an asynchronous wait for one of the signals to occur.
signals.async_wait(handler);
</pre>
<p>
        Signal handling also works on Windows, as the Microsoft Visual C++ runtime
        library maps console events like Ctrl+C to the equivalent signal.
      </p>
<h5>
<a name="asio.overview.signals.h0"></a>
        <span><a name="asio.overview.signals.see_also"></a></span><a class="link" href="signals.html#asio.overview.signals.see_also">See
        Also</a>
      </h5>
<p>
        <a class="link" href="../reference/signal_set.html" title="signal_set">signal_set</a>, <a class="link" href="../examples/cpp03_examples.html#asio.examples.cpp03_examples.http_server">HTTP
        server example (C++03)</a>, <a class="link" href="../examples/cpp11_examples.html#asio.examples.cpp11_examples.http_server">HTTP
        server example (C++11)</a>.
      </p>
</div>
<table xmlns:rev="http://www.cs.rpi.edu/~gregod/boost/tools/doc/revision" width="100%"><tr>
<td align="left"></td>
<td align="right"><div class="copyright-footer">Copyright © 2003-2021 Christopher
      M. Kohlhoff<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="serial_ports.html"><img src="../../prev.png" alt="Prev"></a><a accesskey="u" href="../overview.html"><img src="../../up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../home.png" alt="Home"></a><a accesskey="n" href="posix.html"><img src="../../next.png" alt="Next"></a>
</div>
</body>
</html>
