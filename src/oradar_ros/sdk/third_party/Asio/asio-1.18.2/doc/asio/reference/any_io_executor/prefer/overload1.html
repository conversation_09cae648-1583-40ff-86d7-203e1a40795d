<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>any_io_executor::prefer (1 of 2 overloads)</title>
<link rel="stylesheet" href="../../../../boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.75.2">
<link rel="home" href="../../../../index.html" title="Asio">
<link rel="up" href="../prefer.html" title="any_io_executor::prefer">
<link rel="prev" href="../prefer.html" title="any_io_executor::prefer">
<link rel="next" href="overload2.html" title="any_io_executor::prefer (2 of 2 overloads)">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr><td valign="top"><img alt="asio C++ library" width="250" height="60" src="../../../../asio.png"></td></tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="../prefer.html"><img src="../../../../prev.png" alt="Prev"></a><a accesskey="u" href="../prefer.html"><img src="../../../../up.png" alt="Up"></a><a accesskey="h" href="../../../../index.html"><img src="../../../../home.png" alt="Home"></a><a accesskey="n" href="overload2.html"><img src="../../../../next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h5 class="title">
<a name="asio.reference.any_io_executor.prefer.overload1"></a><a class="link" href="overload1.html" title="any_io_executor::prefer (1 of 2 overloads)">any_io_executor::prefer
          (1 of 2 overloads)</a>
</h5></div></div></div>
<p>
            Obtain a polymorphic wrapper with the specified property.
          </p>
<pre class="programlisting">template&lt;
    typename Property&gt;
any_io_executor prefer(
    const Property &amp; p,
    typename constraint&lt; traits::prefer_member&lt; const base_type &amp;, const Property &amp; &gt;::is_valid &gt;::type  = 0) const;
</pre>
<p>
            Do not call this function directly. It is intended for use with the
            <a class="link" href="../../prefer.html" title="prefer"><code class="computeroutput">prefer</code></a> customisation
            point.
          </p>
<p>
            For example:
          </p>
<pre class="programlisting">any_io_executor ex = ...;
auto ex2 = asio::prefer(ex, execution::blocking.possibly);
</pre>
</div>
<table xmlns:rev="http://www.cs.rpi.edu/~gregod/boost/tools/doc/revision" width="100%"><tr>
<td align="left"></td>
<td align="right"><div class="copyright-footer">Copyright © 2003-2021 Christopher
      M. Kohlhoff<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="../prefer.html"><img src="../../../../prev.png" alt="Prev"></a><a accesskey="u" href="../prefer.html"><img src="../../../../up.png" alt="Up"></a><a accesskey="h" href="../../../../index.html"><img src="../../../../home.png" alt="Home"></a><a accesskey="n" href="overload2.html"><img src="../../../../next.png" alt="Next"></a>
</div>
</body>
</html>
