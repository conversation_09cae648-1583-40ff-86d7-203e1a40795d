<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>async_result::return_type</title>
<link rel="stylesheet" href="../../../boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.75.2">
<link rel="home" href="../../../index.html" title="Asio">
<link rel="up" href="../async_result.html" title="async_result">
<link rel="prev" href="initiate.html" title="async_result::initiate">
<link rel="next" href="../async_result_lt__std__packaged_task_lt__Result_lp_Args_ellipsis__rp__gt__comma__Signature__gt_.html" title="async_result&lt; std::packaged_task&lt; Result(Args...)&gt;, Signature &gt;">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr><td valign="top"><img alt="asio C++ library" width="250" height="60" src="../../../asio.png"></td></tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="initiate.html"><img src="../../../prev.png" alt="Prev"></a><a accesskey="u" href="../async_result.html"><img src="../../../up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../home.png" alt="Home"></a><a accesskey="n" href="../async_result_lt__std__packaged_task_lt__Result_lp_Args_ellipsis__rp__gt__comma__Signature__gt_.html"><img src="../../../next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="asio.reference.async_result.return_type"></a><a class="link" href="return_type.html" title="async_result::return_type">async_result::return_type</a>
</h4></div></div></div>
<p>
          <a class="indexterm" name="asio.indexterm.async_result.return_type"></a> 
The return
          type of the initiating function.
        </p>
<pre class="programlisting">typedef void return_type;
</pre>
<h6>
<a name="asio.reference.async_result.return_type.h0"></a>
          <span><a name="asio.reference.async_result.return_type.requirements"></a></span><a class="link" href="return_type.html#asio.reference.async_result.return_type.requirements">Requirements</a>
        </h6>
<p>
          <span class="emphasis"><em>Header: </em></span><code class="literal">asio/async_result.hpp</code>
        </p>
<p>
          <span class="emphasis"><em>Convenience header: </em></span><code class="literal">asio.hpp</code>
        </p>
</div>
<table xmlns:rev="http://www.cs.rpi.edu/~gregod/boost/tools/doc/revision" width="100%"><tr>
<td align="left"></td>
<td align="right"><div class="copyright-footer">Copyright © 2003-2021 Christopher
      M. Kohlhoff<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="initiate.html"><img src="../../../prev.png" alt="Prev"></a><a accesskey="u" href="../async_result.html"><img src="../../../up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../home.png" alt="Home"></a><a accesskey="n" href="../async_result_lt__std__packaged_task_lt__Result_lp_Args_ellipsis__rp__gt__comma__Signature__gt_.html"><img src="../../../next.png" alt="Next"></a>
</div>
</body>
</html>
