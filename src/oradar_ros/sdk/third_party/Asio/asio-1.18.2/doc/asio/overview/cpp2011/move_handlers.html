<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Movable Handlers</title>
<link rel="stylesheet" href="../../../boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.75.2">
<link rel="home" href="../../../index.html" title="Asio">
<link rel="up" href="../cpp2011.html" title="C++ 2011 Support">
<link rel="prev" href="move_objects.html" title="Movable I/O Objects">
<link rel="next" href="variadic.html" title="Variadic Templates">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr><td valign="top"><img alt="asio C++ library" width="250" height="60" src="../../../asio.png"></td></tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="move_objects.html"><img src="../../../prev.png" alt="Prev"></a><a accesskey="u" href="../cpp2011.html"><img src="../../../up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../home.png" alt="Home"></a><a accesskey="n" href="variadic.html"><img src="../../../next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="asio.overview.cpp2011.move_handlers"></a><a class="link" href="move_handlers.html" title="Movable Handlers">Movable Handlers</a>
</h4></div></div></div>
<p>
          With C++11 and later, user-defined completion handlers are only required
          to be move constructible, and are not required to be copy constructible.
        </p>
<p>
          When move support is enabled, asynchronous that are documented as follows:
        </p>
<pre class="programlisting">template &lt;typename Handler&gt;
void async_XYZ(..., Handler handler);
</pre>
<p>
          are actually declared as:
        </p>
<pre class="programlisting">template &lt;typename Handler&gt;
void async_XYZ(..., Handler&amp;&amp; handler);
</pre>
<p>
          The handler argument is perfectly forwarded and the move construction occurs
          within the body of <code class="computeroutput">async_XYZ()</code>. This ensures that all other
          function arguments are evaluated prior to the move. This is critical when
          the other arguments to <code class="computeroutput">async_XYZ()</code> are members of the handler.
          For example:
        </p>
<pre class="programlisting">struct my_operation
{
  unique_ptr&lt;tcp::socket&gt; socket;
  unique_ptr&lt;vector&lt;char&gt;&gt; buffer;
  ...
  void operator(error_code ec, size_t length)
  {
    ...
    socket-&gt;async_read_some(asio::buffer(*buffer), std::move(*this));
    ...
  }
};
</pre>
<p>
          Move support is automatically enabled for <code class="literal">g++</code> 4.5 and
          later, when the <code class="literal">-std=c++0x</code> or <code class="literal">-std=gnu++0x</code>
          compiler options are used. It may be disabled by defining <code class="computeroutput">ASIO_DISABLE_MOVE</code>,
          or explicitly enabled for other compilers by defining <code class="computeroutput">ASIO_HAS_MOVE</code>.
          Note that these macros also affect the availability of <a class="link" href="move_objects.html" title="Movable I/O Objects">movable
          I/O objects</a>.
        </p>
</div>
<table xmlns:rev="http://www.cs.rpi.edu/~gregod/boost/tools/doc/revision" width="100%"><tr>
<td align="left"></td>
<td align="right"><div class="copyright-footer">Copyright © 2003-2021 Christopher
      M. Kohlhoff<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="move_objects.html"><img src="../../../prev.png" alt="Prev"></a><a accesskey="u" href="../cpp2011.html"><img src="../../../up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../home.png" alt="Home"></a><a accesskey="n" href="variadic.html"><img src="../../../next.png" alt="Next"></a>
</div>
</body>
</html>
