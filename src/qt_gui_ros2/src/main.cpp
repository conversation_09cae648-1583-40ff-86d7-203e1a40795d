#include <QApplication>
#include <QTimer>
#include <QStyleFactory>
#include <QPalette>
#include <memory>
#include <chrono>
#include <thread>
#include "qt_gui_ros2/mainWindow.hpp"
#include "rviz_common/ros_integration/ros_node_abstraction.hpp"

void setupDarkTheme(QApplication &app) {
    // 设置Fusion样式
    app.setStyle(QStyleFactory::create("Fusion"));

    // 设置深色调色板
    QPalette darkPalette;
    darkPalette.setColor(QPalette::Window, QColor(53, 53, 53));
    darkPalette.setColor(QPalette::WindowText, Qt::white);
    darkPalette.setColor(QPalette::Base, QColor(25, 25, 25));
    darkPalette.setColor(QPalette::AlternateBase, QColor(53, 53, 53));
    darkPalette.setColor(QPalette::ToolTipBase, Qt::white);
    darkPalette.setColor(QPalette::ToolTipText, Qt::white);
    darkPalette.setColor(QPalette::Text, Qt::white);
    darkPalette.setColor(QPalette::Button, QColor(53, 53, 53));
    darkPalette.setColor(QPalette::ButtonText, Qt::white);
    darkPalette.setColor(QPalette::BrightText, Qt::red);
    darkPalette.setColor(QPalette::Link, QColor(42, 130, 218));
    darkPalette.setColor(QPalette::Highlight, QColor(42, 130, 218));
    darkPalette.setColor(QPalette::HighlightedText, Qt::black);
    app.setPalette(darkPalette);

    // 设置样式表
    QString styleSheet = R"(
        QMainWindow {
            background-color: #353535;
            color: #ffffff;
        }

        QGroupBox {
            font-weight: bold;
            border: 2px solid #555555;
            border-radius: 5px;
            margin-top: 1ex;
            padding-top: 10px;
            color: #ffffff;
        }

        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 5px 0 5px;
        }

        QPushButton {
            background-color: #404040;
            border: 1px solid #555555;
            border-radius: 3px;
            padding: 5px;
            min-width: 80px;
            color: #ffffff;
        }

        QPushButton:hover {
            background-color: #505050;
            border-color: #2a82da;
        }

        QPushButton:pressed {
            background-color: #303030;
        }

        QPushButton:disabled {
            background-color: #2a2a2a;
            color: #666666;
        }

        QLineEdit {
            background-color: #2a2a2a;
            border: 1px solid #555555;
            border-radius: 3px;
            padding: 3px;
            color: #ffffff;
        }

        QLineEdit:focus {
            border-color: #2a82da;
        }

        QLabel {
            color: #ffffff;
        }

        QStatusBar {
            background-color: #2a2a2a;
            border-top: 1px solid #555555;
            color: #ffffff;
        }
    )";

    app.setStyleSheet(styleSheet);
}

int main(int argc, char *argv[]) {
    QApplication app(argc, argv);

    // {{ AURA-X: Add - 应用深色主题样式. Approval: 寸止(ID:1735659700). }}
    setupDarkTheme(app);

    rclcpp::init(argc, argv);

    // 使用唯一的节点名称，避免与其他RViz实例冲突
    auto ros_node_abs = std::make_shared<rviz_common::ros_integration::RosNodeAbstraction>("robotcar_qt_gui_rviz");

    auto mainWindow = std::make_shared<MainWindow>(&app, ros_node_abs);
    mainWindow->show();

    // 创建定时器来检查ROS状态，但不直接spin节点
    // 避免与RViz内部的执行器冲突
    QTimer ros_timer;
    QObject::connect(&ros_timer, &QTimer::timeout, [&]() {
        if (!rclcpp::ok()) {
            // 只在ROS关闭时退出应用
            app.quit();
        }
    });
    ros_timer.start(100); // 10Hz检查频率，降低资源占用

    // 使用Qt的事件循环而不是自定义循环
    int result = app.exec();

    // 清理ROS
    rclcpp::shutdown();
    return result;
}

