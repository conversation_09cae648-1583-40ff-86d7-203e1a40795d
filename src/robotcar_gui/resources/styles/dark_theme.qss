/* RobotCar GUI 深色主题样式表 */

/* 主窗口 */
QMainWindow {
    background-color: #353535;
    color: #ffffff;
}

/* 组框 */
QGroupBox {
    font-weight: bold;
    border: 2px solid #555555;
    border-radius: 5px;
    margin-top: 1ex;
    padding-top: 10px;
    color: #ffffff;
}

QGroupBox::title {
    subcontrol-origin: margin;
    left: 10px;
    padding: 0 5px 0 5px;
}

/* 按钮 */
QPushButton {
    background-color: #404040;
    border: 1px solid #555555;
    border-radius: 3px;
    padding: 5px;
    min-width: 80px;
    color: #ffffff;
}

QPushButton:hover {
    background-color: #505050;
    border-color: #2a82da;
}

QPushButton:pressed {
    background-color: #303030;
}

QPushButton:disabled {
    background-color: #2a2a2a;
    color: #666666;
    border-color: #333333;
}

/* 特殊按钮样式 */
QPushButton#start_button {
    background-color: #2a82da;
    border-color: #1a72ca;
}

QPushButton#start_button:hover {
    background-color: #3a92ea;
}

QPushButton#start_button:pressed {
    background-color: #1a72ca;
}

QPushButton#emergency_button {
    background-color: #cc0000;
    border-color: #aa0000;
    font-weight: bold;
}

QPushButton#emergency_button:hover {
    background-color: #dd0000;
}

QPushButton#emergency_button:pressed {
    background-color: #bb0000;
}

/* 选项卡 */
QTabWidget::pane {
    border: 1px solid #555555;
    background-color: #2a2a2a;
}

QTabBar::tab {
    background-color: #404040;
    border: 1px solid #555555;
    padding: 5px 10px;
    margin-right: 2px;
    color: #ffffff;
}

QTabBar::tab:selected {
    background-color: #2a82da;
}

QTabBar::tab:hover {
    background-color: #505050;
}

/* 进度条 */
QProgressBar {
    border: 1px solid #555555;
    border-radius: 3px;
    text-align: center;
    color: #ffffff;
}

QProgressBar::chunk {
    background-color: #2a82da;
    border-radius: 2px;
}

/* 输入框 */
QLineEdit, QTextEdit, QPlainTextEdit {
    background-color: #2a2a2a;
    border: 1px solid #555555;
    border-radius: 3px;
    padding: 3px;
    color: #ffffff;
}

QLineEdit:focus, QTextEdit:focus, QPlainTextEdit:focus {
    border-color: #2a82da;
}

/* 下拉框 */
QComboBox {
    background-color: #404040;
    border: 1px solid #555555;
    border-radius: 3px;
    padding: 3px;
    color: #ffffff;
}

QComboBox:hover {
    border-color: #2a82da;
}

QComboBox::drop-down {
    border: none;
}

QComboBox::down-arrow {
    image: url(:/icons/down_arrow.png);
    width: 12px;
    height: 12px;
}

QComboBox QAbstractItemView {
    background-color: #404040;
    border: 1px solid #555555;
    selection-background-color: #2a82da;
    color: #ffffff;
}

/* 数值输入框 */
QSpinBox, QDoubleSpinBox {
    background-color: #2a2a2a;
    border: 1px solid #555555;
    border-radius: 3px;
    padding: 3px;
    color: #ffffff;
}

QSpinBox:focus, QDoubleSpinBox:focus {
    border-color: #2a82da;
}

/* 滑块 */
QSlider::groove:horizontal {
    border: 1px solid #555555;
    height: 8px;
    background: #2a2a2a;
    border-radius: 4px;
}

QSlider::handle:horizontal {
    background: #2a82da;
    border: 1px solid #1a72ca;
    width: 18px;
    margin: -2px 0;
    border-radius: 9px;
}

QSlider::handle:horizontal:hover {
    background: #3a92ea;
}

/* 复选框 */
QCheckBox {
    color: #ffffff;
}

QCheckBox::indicator {
    width: 16px;
    height: 16px;
}

QCheckBox::indicator:unchecked {
    background-color: #2a2a2a;
    border: 1px solid #555555;
    border-radius: 3px;
}

QCheckBox::indicator:checked {
    background-color: #2a82da;
    border: 1px solid #1a72ca;
    border-radius: 3px;
}

/* 列表框 */
QListWidget {
    background-color: #2a2a2a;
    border: 1px solid #555555;
    border-radius: 3px;
    color: #ffffff;
}

QListWidget::item {
    padding: 3px;
    border-bottom: 1px solid #404040;
}

QListWidget::item:selected {
    background-color: #2a82da;
}

QListWidget::item:hover {
    background-color: #404040;
}

/* 标签 */
QLabel {
    color: #ffffff;
}

/* 状态栏 */
QStatusBar {
    background-color: #2a2a2a;
    border-top: 1px solid #555555;
    color: #ffffff;
}

/* 菜单栏 */
QMenuBar {
    background-color: #2a2a2a;
    border-bottom: 1px solid #555555;
    color: #ffffff;
}

QMenuBar::item {
    background-color: transparent;
    padding: 4px 8px;
}

QMenuBar::item:selected {
    background-color: #404040;
}

QMenu {
    background-color: #404040;
    border: 1px solid #555555;
    color: #ffffff;
}

QMenu::item {
    padding: 4px 20px;
}

QMenu::item:selected {
    background-color: #2a82da;
}

/* 工具栏 */
QToolBar {
    background-color: #2a2a2a;
    border: 1px solid #555555;
    spacing: 3px;
}

QToolButton {
    background-color: #404040;
    border: 1px solid #555555;
    border-radius: 3px;
    padding: 3px;
    color: #ffffff;
}

QToolButton:hover {
    background-color: #505050;
    border-color: #2a82da;
}

QToolButton:pressed {
    background-color: #303030;
}

/* 分割器 */
QSplitter::handle {
    background-color: #555555;
}

QSplitter::handle:horizontal {
    width: 3px;
}

QSplitter::handle:vertical {
    height: 3px;
}

/* 滚动条 */
QScrollBar:vertical {
    background-color: #2a2a2a;
    width: 12px;
    border-radius: 6px;
}

QScrollBar::handle:vertical {
    background-color: #555555;
    border-radius: 6px;
    min-height: 20px;
}

QScrollBar::handle:vertical:hover {
    background-color: #666666;
}

QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
    border: none;
    background: none;
}

QScrollBar:horizontal {
    background-color: #2a2a2a;
    height: 12px;
    border-radius: 6px;
}

QScrollBar::handle:horizontal {
    background-color: #555555;
    border-radius: 6px;
    min-width: 20px;
}

QScrollBar::handle:horizontal:hover {
    background-color: #666666;
}

QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {
    border: none;
    background: none;
}
