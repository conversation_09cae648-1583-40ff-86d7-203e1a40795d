#ifndef ROBOTCAR_GUI_NAVIGATION_WIDGET_HPP
#define ROBOTCAR_GUI_NAVIGATION_WIDGET_HPP

#include <QWidget>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <QGroupBox>
#include <QPushButton>
#include <QComboBox>
#include <QSpinBox>
#include <QDoubleSpinBox>
#include <QLineEdit>
#include <QLabel>
#include <QSlider>
#include <QCheckBox>
#include <QProgressBar>
#include <QListWidget>
#include <QFileDialog>
#include <QMessageBox>

#include <geometry_msgs/msg/pose_stamped.hpp>
#include <nav_msgs/msg/path.hpp>

namespace robotcar_gui {

/**
 * @brief 导航控制组件
 * 提供路径跟随、目标点设置、导航模式切换等功能
 */
class NavigationWidget : public QWidget
{
    Q_OBJECT

public:
    explicit NavigationWidget(QWidget *parent = nullptr);
    ~NavigationWidget();

    // 状态更新接口
    void updateNavigationStatus(bool active);
    void updatePathFollowingStatus(bool active);
    void updateCurrentGoal(const geometry_msgs::msg::PoseStamped &goal);
    void updatePath(const nav_msgs::msg::Path::SharedPtr path);

    // 控制接口
    void setNavigationMode(const QString &mode);
    void enableControls(bool enabled);

signals:
    // 路径跟随信号
    void startPathFollowingRequested(const QString &path_file, bool loop_mode, int loop_count);
    void stopPathFollowingRequested();
    
    // 导航控制信号
    void navigationGoalRequested(double x, double y, double yaw);
    void navigationCancelRequested();
    void emergencyStopRequested();
    
    // 模式切换信号
    void navigationModeChangeRequested(const QString &mode);
    
    // 文件操作信号
    void saveWaypointsRequested(const QString &filename);
    void loadWaypointsRequested(const QString &filename);

private slots:
    // 路径跟随控制
    void onSelectPathFile();
    void onStartPathFollowing();
    void onStopPathFollowing();
    void onLoopModeChanged();
    
    // 目标点控制
    void onSetGoal();
    void onCancelNavigation();
    void onEmergencyStop();
    
    // 路径点管理
    void onAddWaypoint();
    void onRemoveWaypoint();
    void onClearWaypoints();
    void onMoveWaypointUp();
    void onMoveWaypointDown();
    
    // 文件操作
    void onSaveWaypoints();
    void onLoadWaypoints();
    
    // 模式切换
    void onNavigationModeChanged();
    
    // 参数调整
    void onSpeedChanged();
    void onToleranceChanged();

private:
    void setupUI();
    void setupPathFollowingGroup();
    void setupGoalControlGroup();
    void setupWaypointManagementGroup();
    void setupNavigationParametersGroup();
    void setupConnections();
    void setupStyles();
    
    void updateButtonStates();
    void updateWaypointList();
    void addWaypointToList(double x, double y, double yaw);
    bool validateGoalInput();

    // UI组件
    QVBoxLayout *main_layout_;
    
    // 路径跟随控制组
    QGroupBox *path_following_group_;
    QLineEdit *path_file_edit_;
    QPushButton *select_path_button_;
    QPushButton *start_path_button_;
    QPushButton *stop_path_button_;
    QCheckBox *loop_mode_checkbox_;
    QSpinBox *loop_count_spin_;
    QProgressBar *path_progress_;
    QLabel *path_status_label_;

    // 目标点控制组
    QGroupBox *goal_control_group_;
    QDoubleSpinBox *goal_x_spin_;
    QDoubleSpinBox *goal_y_spin_;
    QDoubleSpinBox *goal_yaw_spin_;
    QPushButton *set_goal_button_;
    QPushButton *cancel_nav_button_;
    QPushButton *emergency_stop_button_;
    QLabel *current_goal_label_;

    // 路径点管理组
    QGroupBox *waypoint_group_;
    QListWidget *waypoint_list_;
    QPushButton *add_waypoint_button_;
    QPushButton *remove_waypoint_button_;
    QPushButton *clear_waypoints_button_;
    QPushButton *move_up_button_;
    QPushButton *move_down_button_;
    QPushButton *save_waypoints_button_;
    QPushButton *load_waypoints_button_;

    // 导航参数组
    QGroupBox *parameters_group_;
    QComboBox *navigation_mode_combo_;
    QSlider *speed_slider_;
    QLabel *speed_label_;
    QDoubleSpinBox *xy_tolerance_spin_;
    QDoubleSpinBox *yaw_tolerance_spin_;
    QCheckBox *use_ekf_checkbox_;
    QCheckBox *use_sensor_sync_checkbox_;

    // 状态变量
    QString current_path_file_;
    bool navigation_active_;
    bool path_following_active_;
    bool controls_enabled_;
    
    // 路径点数据
    std::vector<geometry_msgs::msg::PoseStamped> waypoints_;
    geometry_msgs::msg::PoseStamped current_goal_;
    
    // 参数
    double max_speed_;
    double xy_tolerance_;
    double yaw_tolerance_;
    bool use_ekf_;
    bool use_sensor_sync_;
    
    // 常量
    static constexpr double DEFAULT_MAX_SPEED = 0.5;  // 默认最大速度(m/s)
    static constexpr double DEFAULT_XY_TOLERANCE = 0.2;  // 默认位置容差(m)
    static constexpr double DEFAULT_YAW_TOLERANCE = 0.2;  // 默认角度容差(rad)
    static constexpr double MIN_SPEED = 0.1;  // 最小速度(m/s)
    static constexpr double MAX_SPEED = 1.0;  // 最大速度(m/s)
};

} // namespace robotcar_gui

#endif // ROBOTCAR_GUI_NAVIGATION_WIDGET_HPP
