#ifndef ROBOTCAR_GUI_ROBOTCAR_MAIN_WINDOW_HPP
#define ROBOTCAR_GUI_ROBOTCAR_MAIN_WINDOW_HPP

#include <QMainWindow>
#include <QTabWidget>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <QLabel>
#include <QPushButton>
#include <QComboBox>
#include <QSpinBox>
#include <QLineEdit>
#include <QTextEdit>
#include <QProgressBar>
#include <QGroupBox>
#include <QFileDialog>
#include <QMessageBox>
#include <QTimer>
#include <QSplitter>
#include <QStatusBar>
#include <QMenuBar>
#include <QToolBar>
#include <QAction>
#include <QResizeEvent>
#include <QCloseEvent>

#include "ros_interface.hpp"
#include "map_widget.hpp"
#include "status_widget.hpp"
#include "navigation_widget.hpp"
#include "navigation_control_widget.hpp"
#include "marker_manager_widget.hpp"
#include "status_monitor_widget.hpp"
#include "robot_control_widget.hpp"  // {{ AURA-X: Add - 添加机器人控制面板头文件. Approval: 用户确认. }}

namespace robotcar_gui {

/**
 * @brief RobotCar增强主操作界面
 * 集成地图可视化、导航控制、标记点管理、状态监控等完整功能模块
 * 提供直观易用的触摸屏友好界面，支持多路径点导航和标记点管理
 */
class RobotCarMainWindow : public QMainWindow
{
    Q_OBJECT

public:
    explicit RobotCarMainWindow(QWidget *parent = nullptr);
    ~RobotCarMainWindow();

    // 模块访问接口
    MapWidget* getMapWidget() const { return map_widget_; }
    NavigationControlWidget* getNavigationControl() const { return navigation_control_widget_; }
    MarkerManagerWidget* getMarkerManager() const { return marker_manager_widget_; }
    StatusMonitorWidget* getStatusMonitor() const { return status_monitor_widget_; }

protected:
    void resizeEvent(QResizeEvent *event) override;
    void closeEvent(QCloseEvent *event) override;

private slots:
    // 导航控制
    void onNavigationStarted();
    void onNavigationStopped();
    void onNavigationCompleted();
    void onNavigationFailed(const QString &error);
    void onEmergencyStop();

    // 地图交互
    void onGoalPointSelected(double x, double y, double yaw);
    void onWaypointAdded(double x, double y);
    void onPathPointsChanged(const QList<PathPoint> &points);

    // 标记点管理
    void onMarkerAdded(const MapMarker &marker);
    void onMarkerRemoved(const QString &id);
    void onMarkerUpdated(const QString &id, const MapMarker &marker);
    void onNavigateToMarker(const QString &id);

    // 系统状态
    void onSystemStatusChanged(const QString &status);
    void onErrorOccurred(const QString &error);
    void onConnectionStatusChanged(bool connected);

    // 文件操作
    void onSaveProject();
    void onLoadProject();
    void onExportData();
    void onImportData();
    void onSaveSettings();
    void onLoadSettings();

    // 界面控制
    void onTabChanged(int index);
    void onFullscreenToggle();
    void onResetLayout();
    void onShowAbout();
    void onShowSettings();

    // 定时更新
    void updateSystemStatus();
    void updateConnectionStatus();



private:
    void setupUI();
    void setupMenuBar();
    void setupToolBar();
    void setupStatusBar();
    void setupConnections();
    void setupStyles();
    void setupWindowGeometry();
    void setupTabWidget();

    // 模块初始化
    void initializeModules();
    void connectModuleSignals();

    // 布局管理
    void createMainLayout();
    void createMapTab();
    void createRobotControlTab();  // {{ AURA-X: Add - 添加机器人控制标签页创建函数. Approval: 用户确认. }}
    void createNavigationTab();
    void createMarkersTab();
    void createStatusTab();
    void createSettingsTab();

    // UI组件
    QWidget *central_widget_;
    QTabWidget *main_tabs_;
    QSplitter *main_splitter_;
    QSplitter *map_splitter_;

    // 核心功能模块
    MapWidget *map_widget_;
    NavigationControlWidget *navigation_control_widget_;
    MarkerManagerWidget *marker_manager_widget_;
    StatusMonitorWidget *status_monitor_widget_;
    RobotControlWidget *robot_control_widget_;  // {{ AURA-X: Add - 添加机器人控制面板. Approval: 用户确认. }}

    // 兼容性模块（保留原有接口）
    StatusWidget *status_widget_;
    NavigationWidget *navigation_widget_;

    // 标签页组件
    QWidget *map_tab_;
    QWidget *robot_control_tab_;  // {{ AURA-X: Add - 添加机器人控制标签页. Approval: 用户确认. }}
    QWidget *navigation_tab_;
    QWidget *markers_tab_;
    QWidget *status_tab_;
    QWidget *settings_tab_;

    // 快速控制面板
    QGroupBox *quick_control_group_;
    QPushButton *emergency_stop_button_;
    QPushButton *start_nav_button_;
    QPushButton *stop_nav_button_;
    QPushButton *home_button_;
    QLabel *connection_status_label_;
    QLabel *battery_status_label_;

    // 设置组件
    QGroupBox *display_settings_group_;
    QCheckBox *fullscreen_cb_;
    QCheckBox *auto_save_cb_;
    QComboBox *theme_combo_;
    QSpinBox *update_rate_spin_;

    // 项目管理组件
    QGroupBox *project_group_;
    QPushButton *save_project_btn_;
    QPushButton *load_project_btn_;
    QPushButton *new_project_btn_;
    QLineEdit *project_name_edit_;
    QLabel *project_status_label_;

    // 菜单和工具栏
    QMenuBar *menu_bar_;
    QToolBar *main_toolbar_;
    QStatusBar *status_bar_;

    // 菜单动作
    QAction *new_project_action_;
    QAction *open_project_action_;
    QAction *save_project_action_;
    QAction *export_data_action_;
    QAction *import_data_action_;
    QAction *exit_action_;

    QAction *emergency_stop_action_;
    QAction *start_nav_action_;
    QAction *stop_nav_action_;
    QAction *clear_path_action_;

    QAction *fullscreen_action_;
    QAction *reset_layout_action_;
    QAction *settings_action_;
    QAction *about_action_;

    // ROS接口
    RosInterface *ros_interface_;

    // 状态变量
    QString current_project_file_;
    QString current_project_name_;
    bool system_running_;
    bool navigation_active_;
    bool connection_established_;
    bool fullscreen_mode_;
    bool auto_save_enabled_;

    // 应用设置
    QString current_theme_;
    int status_update_rate_;
    QSize default_window_size_;
    QPoint default_window_pos_;

    // 定时器
    QTimer *status_update_timer_;
    QTimer *connection_check_timer_;
    QTimer *auto_save_timer_;

    // 辅助函数
    void logMessage(const QString &level, const QString &message);
    void showErrorMessage(const QString &title, const QString &message);
    void showInfoMessage(const QString &title, const QString &message);
    void showWarningMessage(const QString &title, const QString &message);
    bool confirmAction(const QString &title, const QString &message);
    void updateButtonStates();
    void updateStatusDisplay();
    void saveWindowGeometry();
    void restoreWindowGeometry();
    void applyTheme(const QString &theme_name);

    // 项目管理
    void newProject();
    bool saveProject(const QString &filename = QString());
    bool loadProject(const QString &filename);
    void setProjectModified(bool modified = true);
    bool isProjectModified() const;

    // 数据导入导出
    void exportProjectData();
    void importProjectData();

    // 常量定义
    static const QString DEFAULT_PROJECT_NAME;
    static const QStringList AVAILABLE_THEMES;
    static const int DEFAULT_UPDATE_RATE;
    static const QSize MINIMUM_WINDOW_SIZE;
};

} // namespace robotcar_gui

#endif // ROBOTCAR_GUI_ROBOTCAR_MAIN_WINDOW_HPP
