#ifndef ROBOTCAR_GUI_ROBOT_CONTROL_WIDGET_HPP
#define ROBOTCAR_GUI_ROBOT_CONTROL_WIDGET_HPP

#include <QWidget>
#include <QPushButton>
#include <QLabel>
#include <QLineEdit>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <QGroupBox>
#include <QTimer>
#include <QProgressBar>

#include <geometry_msgs/msg/twist.hpp>
#include <geometry_msgs/msg/pose_stamped.hpp>
#include <robotcar_base/msg/car_info.hpp>

namespace robotcar_gui {

class RosInterface;

/**
 * @brief 机器人控制面板Widget
 * 
 * 提供机器人的手动控制功能，包括：
 * - 8方向控制圆盘（前后左右移动 + 左右旋转 + 停止急停）
 * - 实时状态显示（速度、电量、连接状态）
 * - 路径点输入和发送功能
 */
class RobotControlWidget : public QWidget
{
    Q_OBJECT

public:
    explicit RobotControlWidget(QWidget *parent = nullptr);
    ~RobotControlWidget();

    // 设置ROS接口
    void setRosInterface(RosInterface *ros_interface);

public slots:
    // 车辆信息更新槽函数
    void updateCarInfo(const robotcar_base::msg::CarInfo::SharedPtr msg);
    
    // 连接状态更新
    void updateConnectionStatus(bool connected);

signals:
    // 控制命令信号
    void velocityCommandRequested(const geometry_msgs::msg::Twist &cmd);
    
    // 目标点发送信号
    void goalPointRequested(const geometry_msgs::msg::PoseStamped &goal);
    
    // 急停信号
    void emergencyStopRequested();

private slots:
    // 方向控制按钮槽函数
    void onMoveForward();
    void onMoveBackward();
    void onMoveLeft();
    void onMoveRight();
    void onRotateLeft();
    void onRotateRight();
    void onStop();
    void onEmergencyStop();
    
    // 按钮按下和释放事件
    void onButtonPressed();
    void onButtonReleased();
    
    // 路径点输入槽函数
    void onSendGoalPoint();
    void onClearPath();
    
    // 定时器槽函数
    void onControlTimer();

private:
    // UI初始化
    void setupUI();
    void setupControlPanel();
    void setupStatusDisplay();
    void setupGoalInput();
    void setupConnections();
    void setupStyles();
    
    // 控制逻辑
    void sendVelocityCommand(double linear_x, double linear_y, double angular_z);
    void stopRobot();
    void updateButtonStates();
    void updateStatusDisplay();
    
    // UI组件 - 控制面板
    QGroupBox *control_group_;
    QGridLayout *control_layout_;
    
    // 8个方向控制按钮
    QPushButton *forward_btn_;      // 前进 ↑
    QPushButton *backward_btn_;     // 后退 ↓
    QPushButton *left_btn_;         // 左移 ←
    QPushButton *right_btn_;        // 右移 →
    QPushButton *rotate_left_btn_;  // 左转 ↺
    QPushButton *rotate_right_btn_; // 右转 ↻
    QPushButton *stop_btn_;         // 停止 ⏹
    QPushButton *emergency_btn_;    // 急停 🛑
    
    // UI组件 - 状态显示
    QGroupBox *status_group_;
    QVBoxLayout *status_layout_;
    QLabel *speed_label_;           // 线速度显示
    QLabel *angular_speed_label_;   // 角速度显示
    QLabel *battery_label_;         // 电量显示
    QLabel *connection_label_;      // 连接状态显示
    QLabel *mode_label_;            // 当前模式显示
    QProgressBar *battery_bar_;     // 电量进度条
    
    // UI组件 - 目标点输入
    QGroupBox *goal_group_;
    QVBoxLayout *goal_layout_;
    QLineEdit *goal_x_edit_;        // X坐标输入
    QLineEdit *goal_y_edit_;        // Y坐标输入
    QPushButton *send_goal_btn_;    // 发送目标点按钮
    QPushButton *clear_path_btn_;   // 清除路径按钮
    
    // 数据成员
    RosInterface *ros_interface_;
    QTimer *control_timer_;         // 控制命令发送定时器
    
    // 当前状态
    bool is_connected_;
    bool is_manual_mode_;
    double current_linear_x_;
    double current_linear_y_;
    double current_angular_z_;
    double current_battery_;
    
    // 控制参数
    static const double DEFAULT_LINEAR_SPEED;   // 默认线速度
    static const double DEFAULT_ANGULAR_SPEED;  // 默认角速度
    static const int CONTROL_TIMER_INTERVAL;    // 控制定时器间隔(ms)
    
    // 当前按下的按钮
    QPushButton *pressed_button_;
};

} // namespace robotcar_gui

#endif // ROBOTCAR_GUI_ROBOT_CONTROL_WIDGET_HPP
