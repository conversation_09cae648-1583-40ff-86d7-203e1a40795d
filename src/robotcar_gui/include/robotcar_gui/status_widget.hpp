#ifndef ROBOTCAR_GUI_STATUS_WIDGET_HPP
#define ROBOTCAR_GUI_STATUS_WIDGET_HPP

#include <QWidget>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <QLabel>
#include <QProgressBar>
#include <QTextEdit>
#include <QGroupBox>
#include <QTimer>
#include <QPixmap>
#include <QIcon>
#include <QPushButton>
#include <QDateTime>

#include <sensor_msgs/msg/battery_state.hpp>
#include <sensor_msgs/msg/laser_scan.hpp>
#include <std_msgs/msg/bool.hpp>
#include <robotcar_base/msg/car_info.hpp>

namespace robotcar_gui {

/**
 * @brief 系统状态监控组件
 * 显示电池状态、导航系统状态、双雷达状态等信息
 */
class StatusWidget : public QWidget
{
    Q_OBJECT

public:
    explicit StatusWidget(QWidget *parent = nullptr);
    ~StatusWidget();

    // 状态更新接口
    void updateCarInfo(const robotcar_base::msg::CarInfo::SharedPtr car_info);
    void updateLaserScanState(const sensor_msgs::msg::LaserScan::SharedPtr scan);
    void updateNavigationStatus(bool active);
    void updatePathFollowingStatus(bool active);
    void updateSystemStatus(const QString &status);
    void addLogMessage(const QString &message);
    void addErrorMessage(const QString &error);

    // 状态查询接口
    bool isBatteryLow() const { return battery_low_; }
    bool isSystemHealthy() const { return system_healthy_; }
    double getBatteryPercentage() const { return battery_percentage_; }

signals:
    void batteryLowWarning();
    void systemErrorDetected(const QString &error);
    void emergencyStopRequested();

private slots:
    void updateStatusDisplay();
    void checkSystemHealth();
    void onClearLog();
    void onSaveLog();

private:
    void setupUI();
    void setupBatteryGroup();
    void setupNavigationGroup();
    void setupSensorGroup();
    void setupSystemGroup();
    void setupLogGroup();
    void setupStyles();
    void updateBatteryDisplay();
    void updateLaserStatus();
    void updateNavigationDisplay();
    void updateSystemHealthDisplay();

    // UI组件
    QVBoxLayout *main_layout_;
    
    // 电池状态组
    QGroupBox *battery_group_;
    QProgressBar *battery_progress_;
    QLabel *battery_percentage_label_;
    QLabel *battery_voltage_label_;
    QLabel *battery_current_label_;
    QLabel *battery_temperature_label_;
    QLabel *battery_status_icon_;

    // 导航系统状态组
    QGroupBox *navigation_group_;
    QLabel *navigation_status_label_;
    QLabel *navigation_icon_;
    QLabel *path_following_status_label_;
    QLabel *path_following_icon_;
    QLabel *localization_status_label_;
    QLabel *localization_icon_;

    // 传感器状态组
    QGroupBox *sensor_group_;
    QLabel *laser1_status_label_;
    QLabel *laser1_icon_;
    QLabel *laser2_status_label_;
    QLabel *laser2_icon_;
    QLabel *imu_status_label_;
    QLabel *imu_icon_;

    // 系统健康状态组
    QGroupBox *system_group_;
    QLabel *system_status_label_;
    QLabel *system_icon_;
    QLabel *cpu_usage_label_;
    QLabel *memory_usage_label_;
    QLabel *disk_usage_label_;

    // 日志显示组
    QGroupBox *log_group_;
    QTextEdit *log_text_;
    QPushButton *clear_log_button_;
    QPushButton *save_log_button_;

    // 状态数据
    robotcar_base::msg::CarInfo::SharedPtr car_info_data_;
    sensor_msgs::msg::LaserScan::SharedPtr laser_scan_data_;
    
    // 状态变量
    bool navigation_active_;
    bool path_following_active_;
    bool battery_low_;
    bool system_healthy_;
    double battery_percentage_;
    QString current_system_status_;

    // 传感器状态
    bool laser1_active_;
    bool laser2_active_;
    bool imu_active_;
    QDateTime last_laser_update_;
    QDateTime last_imu_update_;

    // 定时器
    QTimer *status_timer_;
    QTimer *health_check_timer_;

    // 图标
    QPixmap good_icon_;
    QPixmap warning_icon_;
    QPixmap error_icon_;
    QPixmap inactive_icon_;

    // 常量
    static constexpr double BATTERY_LOW_THRESHOLD = 20.0;  // 低电量阈值(%)
    static constexpr double BATTERY_CRITICAL_THRESHOLD = 10.0;  // 严重低电量阈值(%)
    static constexpr int SENSOR_TIMEOUT_MS = 5000;  // 传感器超时时间(毫秒)
    static constexpr int MAX_LOG_LINES = 1000;  // 最大日志行数
};

} // namespace robotcar_gui

#endif // ROBOTCAR_GUI_STATUS_WIDGET_HPP
