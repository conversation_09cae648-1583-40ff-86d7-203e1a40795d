#ifndef ROBOTCAR_GUI_MAP_WIDGET_HPP
#define ROBOTCAR_GUI_MAP_WIDGET_HPP

#include <QWidget>
#include <QPainter>
#include <QMouseEvent>
#include <QWheelEvent>
#include <QTimer>
#include <QPointF>
#include <QTransform>
#include <QPixmap>
#include <QMenu>
#include <QAction>
#include <QInputDialog>
#include <QColorDialog>
#include <QFileDialog>
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonArray>
#include <QStandardPaths>
#include <QDir>
#include <QLabel>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QPushButton>
#include <QCheckBox>
#include <QSlider>
#include <QSpinBox>

#include <nav_msgs/msg/occupancy_grid.hpp>
#include <nav_msgs/msg/path.hpp>
#include <geometry_msgs/msg/pose_with_covariance_stamped.hpp>
#include <geometry_msgs/msg/pose_stamped.hpp>
#include <sensor_msgs/msg/laser_scan.hpp>

namespace robotcar_gui {

class RosInterface;

// 标记点结构
struct MapMarker {
    QString id;
    QString name;
    QPointF position;  // 地图坐标
    QColor color;
    QString icon_type;
    QString description;
    bool visible;

    MapMarker() : color(Qt::red), icon_type("circle"), visible(true) {}
};

// 路径点结构
struct PathPoint {
    QPointF position;  // 地图坐标
    double orientation; // 朝向角度
    int repeat_count;   // 重复执行次数
    bool is_waypoint;   // 是否为路径点

    PathPoint() : orientation(0.0), repeat_count(1), is_waypoint(true) {}
};

/**
 * @brief 增强的地图显示组件
 * 提供完整的导航控制界面，包括地图可视化、标记点管理、路径规划等功能
 */
class MapWidget : public QWidget
{
    Q_OBJECT

public:
    explicit MapWidget(QWidget *parent = nullptr);
    ~MapWidget();

    void setRosInterface(RosInterface *ros_interface);

    // 数据更新接口
    void updateMap(const nav_msgs::msg::OccupancyGrid::SharedPtr map);
    void updateRobotPose(const geometry_msgs::msg::PoseStamped::SharedPtr pose);
    void updatePath(const nav_msgs::msg::Path::SharedPtr path);
    void updateLaserScan(const sensor_msgs::msg::LaserScan::SharedPtr scan);

    // 控制接口
    void setShowRobot(bool show) { show_robot_ = show; update(); }
    void setShowPath(bool show) { show_path_ = show; update(); }
    void setShowLaserScan(bool show) { show_laser_scan_ = show; update(); }
    void setShowGrid(bool show) { show_grid_ = show; update(); }
    void setShowMarkers(bool show) { show_markers_ = show; update(); }
    void setShowRobotFootprint(bool show) { show_robot_footprint_ = show; update(); }

    // 视图控制
    void resetView();
    void centerOnRobot();
    void fitToMap();
    void resetAutoAdjustment();  // {{ AURA-X: Add - 重置自动调整状态. Approval: 用户确认. }}

    // 标记点管理
    void addMarker(const MapMarker &marker);
    void removeMarker(const QString &id);
    void updateMarker(const QString &id, const MapMarker &marker);
    QList<MapMarker> getMarkers() const { return markers_; }

    // 路径点管理
    void addPathPoint(const PathPoint &point);
    void clearPathPoints();
    QList<PathPoint> getPathPoints() const { return path_points_; }
    void setPathExecutionCount(int count) { path_execution_count_ = count; }

    // 数据持久化
    void saveMarkersToFile(const QString &filename);
    void loadMarkersFromFile(const QString &filename);

    // 交互模式设置
    void setInteractionMode(int mode);
    int getInteractionMode() const { return static_cast<int>(interaction_mode_); }

signals:
    void goalPointSelected(double x, double y, double yaw);
    void waypointAdded(double x, double y);
    void pathPointsChanged(const QList<PathPoint> &points);
    void markerAdded(const MapMarker &marker);
    void markerRemoved(const QString &id);
    void markerUpdated(const QString &id, const MapMarker &marker);
    void navigationRequested(const QList<PathPoint> &points, int execution_count);

protected:
    void paintEvent(QPaintEvent *event) override;
    void mousePressEvent(QMouseEvent *event) override;
    void mouseMoveEvent(QMouseEvent *event) override;
    void mouseReleaseEvent(QMouseEvent *event) override;
    void wheelEvent(QWheelEvent *event) override;
    void contextMenuEvent(QContextMenuEvent *event) override;

private slots:
    void onSetGoalHere();
    void onAddWaypointHere();
    void onAddMarkerHere();
    void onCenterOnRobot();
    void onResetView();
    void onClearPath();
    void onExecutePath();
    void onSaveMarkers();
    void onLoadMarkers();
    void onEditMarker();
    void onDeleteMarker();
    void onToggleLaserScan();  // {{ AURA-X: Add - 激光雷达显示切换槽函数. Approval: 用户确认. }}

private:
    // 初始化函数
    void setupContextMenu();

    // 绘制函数
    void drawMap(QPainter &painter);
    void drawRobot(QPainter &painter);
    void drawPath(QPainter &painter);
    void drawLaserScan(QPainter &painter);
    void drawGrid(QPainter &painter);
    void drawCoordinateSystem(QPainter &painter);
    void drawMarkers(QPainter &painter);
    void drawPathPoints(QPainter &painter);
    void drawRobotFootprint(QPainter &painter, const QPointF &position, double orientation);

    // 交互处理
    void handleGoalSetting(const QPointF &map_pos);
    void handleWaypointAdding(const QPointF &map_pos);
    void handleMarkerPlacement(const QPointF &map_pos);

    // 标记点操作
    MapMarker* findMarkerAt(const QPointF &widget_pos);
    void showMarkerDialog(MapMarker &marker, bool is_new = false);

    // UI设置
    void setupUI();

    // 坐标转换
    QPointF mapToWidget(double x, double y) const;
    QPointF widgetToMap(const QPointF &point) const;
    QPointF widgetToMap(double x, double y) const;

    // 视图变换
    void updateTransform();

    // 地图数据
    nav_msgs::msg::OccupancyGrid::SharedPtr map_data_;
    QPixmap map_pixmap_;
    bool map_updated_;

    // 机器人数据
    geometry_msgs::msg::PoseStamped::SharedPtr robot_pose_;
    nav_msgs::msg::Path::SharedPtr path_data_;
    sensor_msgs::msg::LaserScan::SharedPtr laser_scan_;

    // 标记点和路径点数据
    QList<MapMarker> markers_;
    QList<PathPoint> path_points_;
    QString selected_marker_id_;
    int path_execution_count_;

    // 显示选项
    bool show_robot_;
    bool show_path_;
    bool show_laser_scan_;
    bool show_grid_;
    bool show_markers_;
    bool show_robot_footprint_;

    // 视图变换
    QTransform transform_;
    double scale_;
    QPointF offset_;
    double min_scale_;
    double max_scale_;

    // 交互状态
    enum InteractionMode {
        NONE,
        SET_GOAL,
        ADD_WAYPOINT,
        ADD_MARKER,
        PAN
    };
    InteractionMode interaction_mode_;

    bool dragging_;
    QPointF last_mouse_pos_;
    QPointF context_menu_pos_;
    QPointF drag_start_pos_;

    // 右键菜单
    QMenu *context_menu_;
    QAction *set_goal_action_;
    QAction *add_waypoint_action_;
    QAction *add_marker_action_;
    QAction *center_robot_action_;
    QAction *reset_view_action_;
    QAction *clear_path_action_;
    QAction *execute_path_action_;
    QAction *edit_marker_action_;
    QAction *delete_marker_action_;
    QAction *show_laser_action_;  // {{ AURA-X: Add - 激光雷达显示控制动作. Approval: 用户确认. }}

    // UI组件 - 移除未使用的工具栏组件，避免界面重叠问题

    // 绘制参数
    static constexpr double ROBOT_SIZE = 0.7;     // 机器人显示大小(米)
    static constexpr double ARROW_SIZE = 0.3;     // 方向箭头大小(米)
    static constexpr double PATH_WIDTH = 0.1;     // 路径线宽(米)
    static constexpr double GRID_SIZE = 1.0;      // 网格大小(米)
    static constexpr double MARKER_SIZE = 0.2;    // 标记点大小(米)
    static constexpr double WAYPOINT_SIZE = 0.15; // 路径点大小(米)

    // 机器人参数 (750mm x 600mm)
    static constexpr double ROBOT_WIDTH = 0.75;   // 机器人宽度(米)
    static constexpr double ROBOT_LENGTH = 0.6;   // 机器人长度(米)

    // ROS接口
    RosInterface *ros_interface_;
};

} // namespace robotcar_gui

#endif // ROBOTCAR_GUI_MAP_WIDGET_HPP
