#ifndef ROBOTCAR_GUI_NAVIGATION_CONTROL_WIDGET_HPP
#define ROBOTCAR_GUI_NAVIGATION_CONTROL_WIDGET_HPP

#include <QWidget>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <QPushButton>
#include <QLabel>
#include <QSpinBox>
#include <QDoubleSpinBox>
#include <QProgressBar>
#include <QTextEdit>
#include <QGroupBox>
#include <QListWidget>
#include <QTableWidget>
#include <QHeaderView>
#include <QTimer>
#include <QComboBox>
#include <QCheckBox>
#include <QSlider>

#include <geometry_msgs/msg/pose_stamped.hpp>
#include <nav_msgs/msg/path.hpp>
#include <nav2_msgs/action/navigate_to_pose.hpp>
#include <nav2_msgs/action/navigate_through_poses.hpp>

#include "map_widget.hpp"

namespace robotcar_gui {

class RosInterface;

/**
 * @brief 导航控制模块
 * 提供完整的导航控制功能，包括多路径点规划、执行控制、状态监控等
 */
class NavigationControlWidget : public QWidget
{
    Q_OBJECT

public:
    explicit NavigationControlWidget(QWidget *parent = nullptr);
    ~NavigationControlWidget();

    void setRosInterface(RosInterface *ros_interface);
    void setMapWidget(MapWidget *map_widget);

    // 导航控制接口
    void startNavigation();
    void pauseNavigation();
    void resumeNavigation();
    void stopNavigation();
    void cancelNavigation();

    // 路径管理
    void addWaypoint(double x, double y, double yaw = 0.0);
    void removeWaypoint(int index);
    void clearWaypoints();
    void moveWaypointUp(int index);
    void moveWaypointDown(int index);

    // 状态更新
    void updateNavigationStatus(const QString &status);
    void updateProgress(double progress);
    void updateCurrentWaypoint(int index);

signals:
    void navigationStarted();
    void navigationPaused();
    void navigationResumed();
    void navigationStopped();
    void navigationCancelled();
    void waypointReached(int index);
    void navigationCompleted();
    void navigationFailed(const QString &error);

private slots:
    void onStartNavigation();
    void onPauseNavigation();
    void onStopNavigation();
    void onCancelNavigation();
    void onClearWaypoints();
    void onAddCurrentPosition();
    void onRemoveSelectedWaypoint();
    void onMoveWaypointUp();
    void onMoveWaypointDown();
    void onWaypointSelectionChanged();
    void onExecutionModeChanged();
    void onRepeatCountChanged();
    void onSpeedLimitChanged();
    void onSaveRoute();
    void onLoadRoute();
    void onOptimizeRoute();

private:
    void setupUI();
    void setupWaypointTable();
    void setupControlPanel();
    void setupStatusPanel();
    void setupRoutePanel();
    
    void updateWaypointTable();
    void updateControlButtons();
    void updateStatusDisplay();
    
    // 路径优化算法
    void optimizeWaypointOrder();
    double calculateDistance(const QPointF &p1, const QPointF &p2);
    
    // 数据持久化
    void saveRouteToFile(const QString &filename);
    void loadRouteFromFile(const QString &filename);

    // 辅助函数
    void logMessage(const QString &level, const QString &message);

    // UI组件
    QVBoxLayout *main_layout_;
    
    // 路径点表格
    QGroupBox *waypoint_group_;
    QTableWidget *waypoint_table_;
    QHBoxLayout *waypoint_buttons_layout_;
    QPushButton *add_current_btn_;
    QPushButton *remove_waypoint_btn_;
    QPushButton *move_up_btn_;
    QPushButton *move_down_btn_;
    QPushButton *clear_waypoints_btn_;
    
    // 控制面板
    QGroupBox *control_group_;
    QGridLayout *control_layout_;
    QPushButton *start_nav_btn_;
    QPushButton *pause_nav_btn_;
    QPushButton *stop_nav_btn_;
    QPushButton *cancel_nav_btn_;
    
    // 执行参数
    QLabel *execution_mode_label_;
    QComboBox *execution_mode_combo_;
    QLabel *repeat_count_label_;
    QSpinBox *repeat_count_spin_;
    QLabel *speed_limit_label_;
    QSlider *speed_limit_slider_;
    QLabel *speed_value_label_;
    
    // 状态面板
    QGroupBox *status_group_;
    QVBoxLayout *status_layout_;
    QLabel *nav_status_label_;
    QProgressBar *nav_progress_bar_;
    QLabel *current_waypoint_label_;
    QLabel *remaining_distance_label_;
    QLabel *estimated_time_label_;
    
    // 路径管理
    QGroupBox *route_group_;
    QHBoxLayout *route_buttons_layout_;
    QPushButton *save_route_btn_;
    QPushButton *load_route_btn_;
    QPushButton *optimize_route_btn_;
    
    // 日志显示
    QGroupBox *log_group_;
    QTextEdit *log_text_;
    
    // 数据
    QList<PathPoint> waypoints_;
    int current_waypoint_index_;
    bool navigation_active_;
    bool navigation_paused_;
    
    // 执行参数
    enum ExecutionMode {
        SINGLE_RUN,      // 单次执行
        REPEAT_COUNT,    // 重复指定次数
        CONTINUOUS       // 连续循环
    };
    ExecutionMode execution_mode_;
    int repeat_count_;
    double speed_limit_;  // 速度限制 (0.0-1.0)
    
    // ROS接口
    MapWidget *map_widget_;
    RosInterface *ros_interface_;
    
    // 更新定时器
    QTimer *status_update_timer_;
};

} // namespace robotcar_gui

#endif // ROBOTCAR_GUI_NAVIGATION_CONTROL_WIDGET_HPP
