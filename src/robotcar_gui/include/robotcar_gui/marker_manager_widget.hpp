#ifndef ROBOTCAR_GUI_MARKER_MANAGER_WIDGET_HPP
#define ROBOTCAR_GUI_MARKER_MANAGER_WIDGET_HPP

#include <QWidget>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <QPushButton>
#include <QLabel>
#include <QLineEdit>
#include <QTextEdit>
#include <QComboBox>
#include <QColorDialog>
#include <QGroupBox>
#include <QListWidget>
#include <QTableWidget>
#include <QHeaderView>
#include <QCheckBox>
#include <QSpinBox>
#include <QDoubleSpinBox>
#include <QFileDialog>
#include <QMessageBox>
#include <QInputDialog>
#include <QMenu>
#include <QAction>
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonArray>
#include <QStandardPaths>

#include "map_widget.hpp"

namespace robotcar_gui {

class RosInterface;

/**
 * @brief 标记点管理模块
 * 提供标记点的创建、编辑、删除、分类管理和持久化存储功能
 */
class MarkerManagerWidget : public QWidget
{
    Q_OBJECT

public:
    explicit MarkerManagerWidget(QWidget *parent = nullptr);
    ~MarkerManagerWidget();

    void setRosInterface(RosInterface *ros_interface);
    void setMapWidget(MapWidget *map_widget);

    // 标记点管理接口
    void addMarker(const MapMarker &marker);
    void removeMarker(const QString &id);
    void updateMarker(const QString &id, const MapMarker &marker);
    void selectMarker(const QString &id);
    
    // 分类管理
    void addCategory(const QString &category);
    void removeCategory(const QString &category);
    QStringList getCategories() const;
    
    // 数据操作
    void clearAllMarkers();
    void exportMarkers(const QString &filename);
    void importMarkers(const QString &filename);
    
    // 搜索和过滤
    void filterByCategory(const QString &category);
    void filterByName(const QString &name);
    void showAllMarkers();

signals:
    void markerSelected(const QString &id);
    void markerAdded(const MapMarker &marker);
    void markerRemoved(const QString &id);
    void markerUpdated(const QString &id, const MapMarker &marker);
    void navigationToMarker(const QString &id);

private slots:
    void onAddMarker();
    void onEditMarker();
    void onRemoveMarker();
    void onDuplicateMarker();
    void onNavigateToMarker();
    void onMarkerSelectionChanged();
    void onCategoryChanged();
    void onFilterChanged();
    void onExportMarkers();
    void onImportMarkers();
    void onClearAllMarkers();
    void onMarkerVisibilityChanged();
    void onShowMarkerInfo();

private:
    void setupUI();
    void setupMarkerTable();
    void setupControlPanel();
    void setupFilterPanel();
    void setupInfoPanel();
    void setupContextMenu();
    
    void updateMarkerTable();
    void updateMarkerInfo();
    void updateFilterOptions();
    
    // 标记点操作
    void showMarkerEditDialog(MapMarker &marker, bool is_new = false);
    QString generateUniqueId();
    QColor getRandomColor();
    
    // 数据验证
    bool validateMarker(const MapMarker &marker);

    // 标记点辅助函数
    bool shouldShowMarker(const MapMarker &marker) const;
    QString getMarkerCategory(const MapMarker &marker) const;

    // 文件操作
    void saveMarkersToJson(const QString &filename);
    void loadMarkersFromJson(const QString &filename);
    void setupBatchOperations();

    // UI组件
    QVBoxLayout *main_layout_;
    
    // 标记点表格
    QGroupBox *marker_table_group_;
    QTableWidget *marker_table_;
    QHBoxLayout *table_buttons_layout_;
    QPushButton *add_marker_btn_;
    QPushButton *edit_marker_btn_;
    QPushButton *remove_marker_btn_;
    QPushButton *duplicate_marker_btn_;
    QPushButton *navigate_to_btn_;
    
    // 过滤面板
    QGroupBox *filter_group_;
    QGridLayout *filter_layout_;
    QLabel *category_filter_label_;
    QComboBox *category_filter_combo_;
    QLabel *name_filter_label_;
    QLineEdit *name_filter_edit_;
    QCheckBox *show_all_cb_;
    QPushButton *clear_filter_btn_;
    
    // 信息面板
    QGroupBox *info_group_;
    QVBoxLayout *info_layout_;
    QLabel *selected_marker_label_;
    QLabel *marker_id_label_;
    QLabel *marker_name_label_;
    QLabel *marker_position_label_;
    QLabel *marker_category_label_;
    QTextEdit *marker_description_text_;
    
    // 批量操作
    QGroupBox *batch_group_;
    QHBoxLayout *batch_layout_;
    QPushButton *export_btn_;
    QPushButton *import_btn_;
    QPushButton *clear_all_btn_;
    QPushButton *backup_btn_;
    QPushButton *restore_btn_;
    
    // 右键菜单
    QMenu *context_menu_;
    QAction *edit_action_;
    QAction *remove_action_;
    QAction *duplicate_action_;
    QAction *navigate_action_;
    QAction *show_info_action_;
    QAction *change_color_action_;
    QAction *toggle_visibility_action_;
    
    // 数据
    QList<MapMarker> markers_;
    QStringList categories_;
    QString selected_marker_id_;
    QString current_filter_category_;
    QString current_filter_name_;
    
    // 表格列索引
    enum TableColumns {
        COL_VISIBLE = 0,
        COL_NAME,
        COL_CATEGORY,
        COL_POSITION_X,
        COL_POSITION_Y,
        COL_COLOR,
        COL_DESCRIPTION,
        COL_COUNT
    };
    
    // 默认分类
    static const QStringList DEFAULT_CATEGORIES;
    
    // 默认图标类型
    static const QStringList ICON_TYPES;

    // 组件引用
    MapWidget *map_widget_;
    RosInterface *ros_interface_;
};

} // namespace robotcar_gui

#endif // ROBOTCAR_GUI_MARKER_MANAGER_WIDGET_HPP
