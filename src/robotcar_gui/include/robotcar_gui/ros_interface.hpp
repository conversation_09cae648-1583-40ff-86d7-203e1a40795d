#ifndef ROBOTCAR_GUI_ROS_INTERFACE_HPP
#define ROBOTCAR_GUI_ROS_INTERFACE_HPP

#include <rclcpp/rclcpp.hpp>
#include <rclcpp_action/rclcpp_action.hpp>
#include <std_msgs/msg/bool.hpp>
#include <std_msgs/msg/string.hpp>
#include <geometry_msgs/msg/pose_stamped.hpp>
#include <geometry_msgs/msg/pose_with_covariance_stamped.hpp>
#include <geometry_msgs/msg/twist.hpp>  // {{ AURA-X: Restore - 恢复Twist消息头文件. Approval: 用户确认. }}
#include <nav_msgs/msg/occupancy_grid.hpp>
#include <nav_msgs/msg/path.hpp>
#include <sensor_msgs/msg/laser_scan.hpp>
#include <sensor_msgs/msg/battery_state.hpp>
#include <sensor_msgs/msg/imu.hpp>  // 添加IMU消息
#include <nav2_msgs/action/navigate_to_pose.hpp>
#include <nav2_msgs/action/follow_waypoints.hpp>
// robotcar_base消息类型
#include <robotcar_base/msg/car_info.hpp>
// TF2 headers commented out for now to simplify build
// #include <tf2_ros/transform_listener.hpp>
// #include <tf2_ros/buffer.hpp>

#include <QObject>
#include <QTimer>
#include <QThread>
#include <QMutex>
#include <QProcess>
#include <memory>

namespace robotcar_gui {

/**
 * @brief ROS2接口类，处理与ROS2系统的所有通信
 */
class RosInterface : public QObject, public rclcpp::Node, public std::enable_shared_from_this<RosInterface>
{
    Q_OBJECT

public:
    explicit RosInterface(QObject *parent = nullptr, const QString &namespace_prefix = "chassis");
    ~RosInterface();

    // 启动/停止ROS节点
    void startRosNode();
    void stopRosNode();

    // 命名空间管理
    void setNamespace(const QString &namespace_prefix);
    QString getNamespace() const { return namespace_; }

    // 连接状态检查
    bool isConnected() const { return ros_connected_; }
    bool areTopicsAvailable() const;

    // Launch文件控制
    void startLaunchFile(const QString &launch_package, const QString &launch_file, 
                        const QStringList &arguments = QStringList());
    void stopLaunchFile();

    // 导航控制
    void sendNavigationGoal(double x, double y, double yaw);
    void sendMultipleWaypoints(const std::vector<geometry_msgs::msg::PoseStamped> &waypoints);
    void cancelNavigation();
    void emergencyStop();

    // {{ AURA-X: Restore - 恢复速度控制功能. Approval: 用户确认. }}
    void sendVelocityCommand(const geometry_msgs::msg::Twist &cmd);

    // 路径跟随控制
    void startPathFollowing(const QString &path_file, bool loop_mode = false);
    void stopPathFollowing();

    // 获取当前状态
    bool isNavigationActive() const { return navigation_active_; }
    bool isPathFollowingActive() const { return path_following_active_; }
    bool isLaunchFileRunning() const { return launch_process_ && launch_process_->state() == QProcess::Running; }

signals:
    // 数据更新信号
    void mapUpdated(const nav_msgs::msg::OccupancyGrid::SharedPtr map);
    void robotPoseUpdated(const geometry_msgs::msg::PoseStamped::SharedPtr pose);
    void laserScanUpdated(const sensor_msgs::msg::LaserScan::SharedPtr scan);
    void imuUpdated(const sensor_msgs::msg::Imu::SharedPtr imu);  // 添加IMU信号
    void carInfoUpdated(const robotcar_base::msg::CarInfo::SharedPtr car_info);
    void pathUpdated(const nav_msgs::msg::Path::SharedPtr path);
    void trackedPoseUpdated(const geometry_msgs::msg::PoseWithCovarianceStamped::SharedPtr tracked_pose);  // {{ AURA-X: Add - 添加机器人位置信号. Approval: 用户确认. }}
    
    // 状态更新信号
    void navigationStatusChanged(bool active);
    void pathFollowingStatusChanged(bool active);
    void systemStatusChanged(const QString &status);
    void errorOccurred(const QString &error);

    // 连接状态信号
    void connectionStatusChanged(bool connected);
    void topicsAvailabilityChanged(bool available);

    // 导航结果信号
    void navigationGoalReached();
    void navigationGoalFailed(const QString &reason);

private slots:
    void spinRos();
    void checkLaunchProcess();
    void checkConnectionStatus();
    void checkTopicsAvailability();

private:
    // ROS2订阅者
    rclcpp::Subscription<nav_msgs::msg::OccupancyGrid>::SharedPtr map_sub_;
    rclcpp::Subscription<geometry_msgs::msg::PoseStamped>::SharedPtr pose_sub_;
    rclcpp::Subscription<sensor_msgs::msg::LaserScan>::SharedPtr scan_sub_;
    rclcpp::Subscription<robotcar_base::msg::CarInfo>::SharedPtr car_info_sub_;
    rclcpp::Subscription<nav_msgs::msg::Path>::SharedPtr path_sub_;
    rclcpp::Subscription<std_msgs::msg::Bool>::SharedPtr path_following_status_sub_;
    rclcpp::Subscription<geometry_msgs::msg::PoseWithCovarianceStamped>::SharedPtr tracked_pose_sub_;  // {{ AURA-X: Add - 添加机器人位置订阅者. Approval: 用户确认. }}
    rclcpp::Subscription<sensor_msgs::msg::Imu>::SharedPtr imu_sub_;  // 添加IMU订阅者

    // ROS2发布者
    rclcpp::Publisher<geometry_msgs::msg::PoseStamped>::SharedPtr goal_pub_;
    rclcpp::Publisher<std_msgs::msg::Bool>::SharedPtr emergency_stop_pub_;
    rclcpp::Publisher<geometry_msgs::msg::Twist>::SharedPtr cmd_vel_pub_;  // {{ AURA-X: Restore - 恢复速度命令发布者. Approval: 用户确认. }}

    // ROS2动作客户端
    rclcpp_action::Client<nav2_msgs::action::NavigateToPose>::SharedPtr nav_action_client_;
    rclcpp_action::Client<nav2_msgs::action::FollowWaypoints>::SharedPtr waypoints_action_client_;

    // TF2 (commented out for now)
    // std::shared_ptr<tf2_ros::Buffer> tf_buffer_;
    // std::shared_ptr<tf2_ros::TransformListener> tf_listener_;

    // Qt定时器和线程
    QTimer *ros_timer_;
    QTimer *launch_check_timer_;
    QTimer *connection_check_timer_;
    QThread *ros_thread_;

    // Launch进程管理
    QProcess *launch_process_;
    QString current_launch_package_;
    QString current_launch_file_;

    // 状态变量
    bool navigation_active_;
    bool path_following_active_;
    bool ros_connected_;
    bool topics_available_;
    QString namespace_;
    mutable QMutex mutex_;

    // 辅助函数
    QString addNamespace(const QString &topic_name) const;
    void initializeSubscribers();
    void checkTopicExistence(const QString &topic_name, bool &exists);

    // 回调函数
    void mapCallback(const nav_msgs::msg::OccupancyGrid::SharedPtr msg);
    void poseCallback(const geometry_msgs::msg::PoseStamped::SharedPtr msg);
    void scanCallback(const sensor_msgs::msg::LaserScan::SharedPtr msg);
    void carInfoCallback(const robotcar_base::msg::CarInfo::SharedPtr msg);
    void pathCallback(const nav_msgs::msg::Path::SharedPtr msg);
    void pathFollowingStatusCallback(const std_msgs::msg::Bool::SharedPtr msg);
    void trackedPoseCallback(const geometry_msgs::msg::PoseWithCovarianceStamped::SharedPtr msg);  // {{ AURA-X: Add - 添加tracked_pose回调函数声明. Approval: 用户确认. }}
    void imuCallback(const sensor_msgs::msg::Imu::SharedPtr msg);  // 添加IMU回调

    // 动作回调函数 (简化版本中暂时移除)
};

} // namespace robotcar_gui

#endif // ROBOTCAR_GUI_ROS_INTERFACE_HPP
