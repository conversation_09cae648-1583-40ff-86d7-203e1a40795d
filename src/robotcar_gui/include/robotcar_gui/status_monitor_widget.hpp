#ifndef ROBOTCAR_GUI_STATUS_MONITOR_WIDGET_HPP
#define ROBOTCAR_GUI_STATUS_MONITOR_WIDGET_HPP

#include <QWidget>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <QLabel>
#include <QProgressBar>
#include <QTextEdit>
#include <QGroupBox>
#include <QTimer>
#include <QScrollArea>
#include <QFrame>
#include <QPushButton>
#include <QCheckBox>
#include <QComboBox>
#include <QSpinBox>
#include <QTabWidget>
#include <QTableWidget>
#include <QHeaderView>
#include <QDateTime>
#include <QFileDialog>
#include <QTextStream>

#include <rclcpp/rclcpp.hpp>
#include <std_msgs/msg/string.hpp>
#include <geometry_msgs/msg/twist.hpp>
#include <sensor_msgs/msg/battery_state.hpp>
#include <diagnostic_msgs/msg/diagnostic_array.hpp>
#include <nav2_msgs/action/navigate_to_pose.hpp>

namespace robotcar_gui {

class RosInterface;

/**
 * @brief 系统状态监控模块
 * 提供Nav2导航系统状态、机器人外设状态、系统日志等实时监控功能
 */
class StatusMonitorWidget : public QWidget
{
    Q_OBJECT

public:
    explicit StatusMonitorWidget(QWidget *parent = nullptr);
    ~StatusMonitorWidget();

    void setRosInterface(RosInterface *ros_interface);

    // 状态更新接口
    void updateNavigationStatus(const QString &status);
    void updateRobotPose(double x, double y, double yaw);
    void updateVelocity(double linear_x, double angular_z);
    void updateBatteryStatus(double voltage, double percentage);
    void updateSensorStatus(const QString &sensor, const QString &status);
    void updateSystemLoad(double cpu_usage, double memory_usage);
    
    // 日志管理
    void addLogMessage(const QString &level, const QString &message);
    void clearLogs();
    void exportLogs(const QString &filename);
    
    // 诊断信息
    void updateDiagnostics(const diagnostic_msgs::msg::DiagnosticArray::SharedPtr diagnostics);

signals:
    void emergencyStopRequested();
    void systemResetRequested();
    void logLevelChanged(const QString &level);

private slots:
    void onEmergencyStop();
    void onSystemReset();
    void onClearLogs();
    void onExportLogs();
    void onLogLevelChanged();
    void onAutoScrollChanged();
    void onRefreshStatus();
    void onTabChanged(int index);

private:
    void setupUI();
    void setupNavigationTab();
    void setupRobotTab();
    void setupSystemTab();
    void setupLogsTab();
    void setupDiagnosticsTab();
    
    void updateNavigationDisplay();
    void updateRobotDisplay();
    void updateSystemDisplay();
    void updateLogsDisplay();
    void updateDiagnosticsDisplay();
    
    // 状态指示器
    QLabel* createStatusIndicator(const QString &name, const QString &initial_status = "Unknown");
    void updateStatusIndicator(QLabel *indicator, const QString &status, bool is_ok = true);
    
    // 进度条
    QProgressBar* createProgressBar(const QString &name, double min_val = 0.0, double max_val = 100.0);
    void updateProgressBar(QProgressBar *bar, double value);
    
    // 日志处理
    void addLogEntry(const QString &timestamp, const QString &level, const QString &message);
    QString formatLogMessage(const QString &level, const QString &message);
    QColor getLogLevelColor(const QString &level);
    bool shouldShowLogLevel(const QString &level);

    // UI组件
    QVBoxLayout *main_layout_;
    QTabWidget *tab_widget_;
    
    // 导航状态标签页
    QWidget *nav_tab_;
    QVBoxLayout *nav_layout_;
    QGroupBox *nav_status_group_;
    QGridLayout *nav_status_layout_;
    QLabel *nav_state_indicator_;
    QLabel *planner_status_indicator_;
    QLabel *controller_status_indicator_;
    QLabel *recovery_status_indicator_;
    QProgressBar *nav_progress_bar_;
    QLabel *current_goal_label_;
    QLabel *path_length_label_;
    QLabel *eta_label_;
    
    // 机器人状态标签页
    QWidget *robot_tab_;
    QVBoxLayout *robot_layout_;
    
    // 位置信息组
    QGroupBox *pose_group_;
    QGridLayout *pose_layout_;
    QLabel *position_x_label_;
    QLabel *position_y_label_;
    QLabel *orientation_label_;
    
    // 速度信息组
    QGroupBox *velocity_group_;
    QGridLayout *velocity_layout_;
    QLabel *linear_vel_label_;
    QLabel *angular_vel_label_;
    QProgressBar *speed_bar_;
    
    // 传感器状态组
    QGroupBox *sensors_group_;
    QGridLayout *sensors_layout_;
    QLabel *lidar_status_indicator_;
    QLabel *imu_status_indicator_;
    QLabel *camera_status_indicator_;
    QLabel *encoder_status_indicator_;
    
    // 电源状态组
    QGroupBox *power_group_;
    QGridLayout *power_layout_;
    QLabel *battery_voltage_label_;
    QProgressBar *battery_bar_;
    QLabel *charging_status_label_;
    
    // 系统状态标签页
    QWidget *system_tab_;
    QVBoxLayout *system_layout_;
    
    // 系统资源组
    QGroupBox *resources_group_;
    QGridLayout *resources_layout_;
    QProgressBar *cpu_bar_;
    QProgressBar *memory_bar_;
    QProgressBar *disk_bar_;
    QLabel *uptime_label_;
    
    // 网络状态组
    QGroupBox *network_group_;
    QGridLayout *network_layout_;
    QLabel *ros_master_indicator_;
    QLabel *wifi_status_indicator_;
    QLabel *ethernet_status_indicator_;
    
    // 控制按钮组
    QGroupBox *control_group_;
    QHBoxLayout *control_layout_;
    QPushButton *emergency_stop_btn_;
    QPushButton *system_reset_btn_;
    QPushButton *refresh_btn_;
    
    // 日志标签页
    QWidget *logs_tab_;
    QVBoxLayout *logs_layout_;
    QHBoxLayout *logs_control_layout_;
    QComboBox *log_level_combo_;
    QCheckBox *auto_scroll_cb_;
    QPushButton *clear_logs_btn_;
    QPushButton *export_logs_btn_;
    QTextEdit *logs_text_;
    
    // 诊断标签页
    QWidget *diagnostics_tab_;
    QVBoxLayout *diagnostics_layout_;
    QTableWidget *diagnostics_table_;
    
    // 数据存储
    struct NavigationStatus {
        QString state;
        QString planner_status;
        QString controller_status;
        QString recovery_status;
        double progress;
        QString current_goal;
        double path_length;
        QString eta;
    } nav_status_;
    
    struct RobotStatus {
        double pos_x, pos_y, orientation;
        double linear_vel, angular_vel;
        double battery_voltage, battery_percentage;
        bool charging;
        QMap<QString, QString> sensor_status;
    } robot_status_;
    
    struct SystemStatus {
        double cpu_usage, memory_usage, disk_usage;
        QString uptime;
        bool ros_master_ok;
        QString wifi_status, ethernet_status;
    } system_status_;
    
    // 日志数据
    struct LogEntry {
        QString timestamp;
        QString level;
        QString message;
    };
    QList<LogEntry> log_entries_;
    QString current_log_level_;
    bool auto_scroll_logs_;
    
    // 诊断数据
    QMap<QString, QString> diagnostic_status_;
    
    // 更新定时器
    QTimer *status_update_timer_;
    
    // ROS接口
    RosInterface *ros_interface_;
    
    // 常量
    static const QStringList LOG_LEVELS;
    static const QMap<QString, QColor> LOG_LEVEL_COLORS;
};

} // namespace robotcar_gui

#endif // ROBOTCAR_GUI_STATUS_MONITOR_WIDGET_HPP
