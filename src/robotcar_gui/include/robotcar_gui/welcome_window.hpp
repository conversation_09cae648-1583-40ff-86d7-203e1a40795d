#ifndef ROBOTCAR_GUI_WELCOME_WINDOW_HPP
#define ROBOTCAR_GUI_WELCOME_WINDOW_HPP

#include <QMainWindow>
#include <QLabel>
#include <QPushButton>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QTimer>
#include <QPropertyAnimation>
#include <QGraphicsOpacityEffect>
#include <QMovie>

namespace robotcar_gui {

/**
 * @brief 拟人化欢迎界面窗口
 * 提供友好的用户交互入口，适合机器人载体显示
 */
class WelcomeWindow : public QMainWindow
{
    Q_OBJECT

public:
    explicit WelcomeWindow(QWidget *parent = nullptr);
    ~WelcomeWindow();

signals:
    void startMainApplication();

private slots:
    void onStartButtonClicked();
    void updateWelcomeAnimation();
    void showSystemStatus();

protected:
    void paintEvent(QPaintEvent *event) override;
    void mousePressEvent(QMouseEvent *event) override;
    void keyPressEvent(QKeyEvent *event) override;

private:
    void setupUI();
    void setupAnimations();
    void setupStyles();
    void startWelcomeSequence();

    // UI组件
    QWidget *central_widget_;
    QVBoxLayout *main_layout_;
    QHBoxLayout *content_layout_;
    
    // 欢迎界面组件
    QLabel *robot_avatar_;          // 机器人头像
    QLabel *welcome_title_;         // 欢迎标题
    QLabel *welcome_subtitle_;      // 欢迎副标题
    QLabel *system_status_;         // 系统状态
    QPushButton *start_button_;     // 开始按钮
    QPushButton *settings_button_;  // 设置按钮
    QPushButton *exit_button_;      // 退出按钮

    // 动画组件
    QMovie *robot_animation_;       // 机器人动画
    QTimer *animation_timer_;       // 动画定时器
    QPropertyAnimation *fade_animation_;  // 淡入淡出动画
    QGraphicsOpacityEffect *opacity_effect_;

    // 状态变量
    bool animation_running_;
    int animation_step_;
};

} // namespace robotcar_gui

#endif // ROBOTCAR_GUI_WELCOME_WINDOW_HPP
