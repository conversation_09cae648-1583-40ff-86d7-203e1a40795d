# RobotCar GUI配置文件
# 配置GUI界面的各种参数和设置

# 界面设置
gui:
  # 窗口设置
  window:
    title: "RobotCar 双雷达导航控制系统"
    width: 1800  # 增加默认宽度
    height: 1200  # 增加默认高度
    min_width: 1400  # 增加最小宽度
    min_height: 900  # 增加最小高度
    fullscreen: false
    
  # 主题设置
  theme:
    style: "Fusion"  # Qt样式
    dark_mode: true
    primary_color: "#2a82da"
    background_color: "#353535"
    text_color: "#ffffff"
    
  # 欢迎界面设置
  welcome:
    show_welcome: true
    animation_enabled: true
    auto_start_delay: 4000  # 毫秒
    
  # 地图显示设置
  map:
    show_robot: true
    show_path: true
    show_laser_scan: true  # {{ AURA-X: Fix - 默认显示激光雷达数据. Approval: 用户确认. }}
    show_grid: true
    grid_size: 1.0  # 米
    robot_size: 0.7  # 米
    arrow_size: 0.3  # 米
    path_width: 0.1  # 米
    min_scale: 0.05  # 允许更小的缩放，看到更大范围
    max_scale: 50.0  # 允许更大的缩放，看到更多细节
    
  # 状态监控设置
  status:
    update_interval: 1000  # 毫秒
    battery_low_threshold: 20.0  # 百分比
    battery_critical_threshold: 10.0  # 百分比
    sensor_timeout: 5000  # 毫秒
    max_log_lines: 1000

# ROS2设置
ros:
  # 节点设置
  node:
    name: "robotcar_gui_node"
    namespace: "chassis"  # 默认命名空间
    
  # 话题设置（将自动添加命名空间前缀）
  topics:
    map: "/map"                                    # 地图数据，来自Cartographer
    robot_pose: "/tracked_pose"                    # 机器人位置，来自Cartographer（不使用AMCL）
    laser_scan: "/merged"                         # 融合后的激光扫描数据（修正为/merged）
    car_info: "/car_info"                         # 车辆信息（包含电池状态），来自robotcar_base
    planned_path: "/plan"                         # 规划路径，来自Nav2 planner
    path_following_status: "/path_following_status" # 路径跟随状态
    goal_pose: "/goal_pose"                       # 导航目标点
    emergency_stop: "/emergency_stop"             # 紧急停止信号
    # 里程计相关话题
    odom_raw: "/diff_drive_controller/odom"       # 原始里程计数据
    odom_filtered: "/odometry/filtered"           # EKF融合后的里程计数据
    # 控制话题
    cmd_vel: "/diff_drive_controller/cmd_vel_unstamped" # 速度控制命令

  # 命名空间设置
  namespace:
    default: "chassis"                            # 默认命名空间
    isolation: true                               # 启用命名空间隔离
    
  # 服务设置
  services:
    save_map: "/map_saver/save_map"
    load_map: "/map_server/load_map"
    
  # 动作设置
  actions:
    navigate_to_pose: "navigate_to_pose"
    follow_waypoints: "follow_waypoints"

# 导航设置
navigation:
  # 默认参数
  default_speed: 0.5  # m/s
  min_speed: 0.1  # m/s
  max_speed: 1.0  # m/s
  xy_tolerance: 0.2  # m
  yaw_tolerance: 0.2  # rad
  
  # 路径跟随设置
  path_following:
    default_loop_count: 1
    max_loop_count: 100
    
  # 模式设置
  modes:
    - "建图模式"
    - "定位模式"
    - "导航模式"
    - "路径跟随模式"

# Launch文件映射
launch_files:
  mapping: "robotcar_laser_fusion/fusion_02_mapping.launch.py"
  localization: "robotcar_laser_fusion/fusion_03_localization.launch.py"
  navigation: "robotcar_laser_fusion/fusion_04_navigation.launch.py"
  multi_point_navigation: "robotcar_laser_fusion/fusion_05_multi_point_navigation.launch.py"
  path_following: "robotcar_laser_fusion/fusion_07_path_following.launch.py"

# 文件路径设置
paths:
  # 默认文件路径
  maps_dir: "~/robotcar_maps"
  waypoints_dir: "~/robotcar_waypoints"
  logs_dir: "~/robotcar_logs"
  
  # 文件扩展名
  map_extension: ".yaml"
  waypoint_extension: ".yaml"
  log_extension: ".txt"

# 安全设置
safety:
  # 紧急停止设置
  emergency_stop:
    enabled: true
    hotkey: "Ctrl+E"
    
  # 电池安全设置
  battery:
    auto_warning: true
    auto_return_home: false
    critical_action: "stop"  # stop, return_home, continue
    
  # 导航安全设置
  navigation:
    max_goal_distance: 50.0  # 米
    collision_avoidance: true
    obstacle_detection: true

# 日志设置
logging:
  # 日志级别
  level: "INFO"  # DEBUG, INFO, WARN, ERROR
  
  # 日志输出
  console: true
  file: true
  
  # 日志文件设置
  file_settings:
    max_size: "10MB"
    max_files: 5
    rotation: true
