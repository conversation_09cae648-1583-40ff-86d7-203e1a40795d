#include <QApplication>
#include <QStyleFactory>
#include <QDir>
#include <QStandardPaths>
#include <QSplashScreen>
#include <QPixmap>
#include <QTimer>
#include <QCommandLineParser>
#include <QCommandLineOption>
#include <QPainter>
#include <QFont>

#include <rclcpp/rclcpp.hpp>

#include "robotcar_gui/welcome_window.hpp"
#include "robotcar_gui/robotcar_main_window.hpp"

/**
 * @brief 设置应用程序样式
 */
void setupApplicationStyle(QApplication &app)
{
    // 设置应用程序样式
    app.setStyle(QStyleFactory::create("Fusion"));
    
    // 设置深色主题
    QPalette darkPalette;
    darkPalette.setColor(QPalette::Window, QColor(53, 53, 53));
    darkPalette.setColor(QPalette::WindowText, Qt::white);
    darkPalette.setColor(QPalette::Base, QColor(25, 25, 25));
    darkPalette.setColor(QPalette::AlternateBase, QColor(53, 53, 53));
    darkPalette.setColor(QPalette::ToolTipBase, Qt::white);
    darkPalette.setColor(QPalette::ToolTipText, Qt::white);
    darkPalette.setColor(QPalette::Text, Qt::white);
    darkPalette.setColor(QPalette::Button, QColor(53, 53, 53));
    darkPalette.setColor(QPalette::ButtonText, Qt::white);
    darkPalette.setColor(QPalette::BrightText, Qt::red);
    darkPalette.setColor(QPalette::Link, QColor(42, 130, 218));
    darkPalette.setColor(QPalette::Highlight, QColor(42, 130, 218));
    darkPalette.setColor(QPalette::HighlightedText, Qt::black);
    
    app.setPalette(darkPalette);
    
    // 设置样式表
    QString styleSheet = R"(
        QMainWindow {
            background-color: #353535;
        }
        
        QGroupBox {
            font-weight: bold;
            border: 2px solid #555555;
            border-radius: 5px;
            margin-top: 1ex;
            padding-top: 10px;
        }
        
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 5px 0 5px;
        }
        
        QPushButton {
            background-color: #404040;
            border: 1px solid #555555;
            border-radius: 3px;
            padding: 5px;
            min-width: 80px;
        }
        
        QPushButton:hover {
            background-color: #505050;
        }
        
        QPushButton:pressed {
            background-color: #303030;
        }
        
        QPushButton:disabled {
            background-color: #2a2a2a;
            color: #666666;
        }
        
        QTabWidget::pane {
            border: 1px solid #555555;
        }
        
        QTabBar::tab {
            background-color: #404040;
            border: 1px solid #555555;
            padding: 5px 10px;
            margin-right: 2px;
        }
        
        QTabBar::tab:selected {
            background-color: #2a82da;
        }
        
        QProgressBar {
            border: 1px solid #555555;
            border-radius: 3px;
            text-align: center;
        }
        
        QProgressBar::chunk {
            background-color: #2a82da;
            border-radius: 2px;
        }
    )";
    
    app.setStyleSheet(styleSheet);
}

/**
 * @brief 显示启动画面
 */
QSplashScreen* showSplashScreen()
{
    QPixmap pixmap(400, 300);
    pixmap.fill(QColor(53, 53, 53));
    
    QPainter painter(&pixmap);
    painter.setPen(Qt::white);
    painter.setFont(QFont("Arial", 16, QFont::Bold));
    painter.drawText(pixmap.rect(), Qt::AlignCenter, 
                    "RobotCar GUI\n\n正在初始化系统...");
    
    QSplashScreen *splash = new QSplashScreen(pixmap);
    splash->show();
    splash->showMessage("正在启动机器人操作系统...", Qt::AlignBottom | Qt::AlignCenter, Qt::white);
    
    return splash;
}

int main(int argc, char *argv[])
{
    // 分离ROS2和Qt参数
    std::vector<char*> qt_args;
    std::vector<char*> ros_args;

    // 第一个参数总是程序名
    qt_args.push_back(argv[0]);
    ros_args.push_back(argv[0]);

    // 分离参数
    bool skip_welcome = false;
    bool fullscreen = false;

    for (int i = 1; i < argc; ++i) {
        std::string arg = argv[i];

        // Qt GUI特定参数
        if (arg == "--skip-welcome" || arg == "-s") {
            skip_welcome = true;
        } else if (arg == "--fullscreen" || arg == "-f") {
            fullscreen = true;
        } else if (arg.find("--ros-args") == 0 || arg.find("-r") == 0 ||
                   arg.find("--params-file") == 0 || arg.find("--param") == 0) {
            // ROS2参数
            ros_args.push_back(argv[i]);
        } else {
            // 其他参数同时传递给Qt和ROS2
            qt_args.push_back(argv[i]);
            ros_args.push_back(argv[i]);
        }
    }

    // 初始化ROS2（使用分离后的参数）
    int ros_argc = ros_args.size();
    char** ros_argv = ros_args.data();
    rclcpp::init(ros_argc, ros_argv);

    // 创建Qt应用程序（使用分离后的参数）
    int qt_argc = qt_args.size();
    char** qt_argv = qt_args.data();
    QApplication app(qt_argc, qt_argv);
    app.setApplicationName("RobotCar GUI");
    app.setApplicationVersion("1.0.0");
    app.setOrganizationName("RobotCar Team");
    app.setOrganizationDomain("robotcar.com");
    
    // 设置应用程序样式
    setupApplicationStyle(app);
    
    // 显示启动画面
    QSplashScreen *splash = showSplashScreen();
    app.processEvents();
    
    // 模拟初始化过程
    QTimer::singleShot(1000, [splash]() {
        splash->showMessage("正在加载配置文件...", Qt::AlignBottom | Qt::AlignCenter, Qt::white);
    });
    
    QTimer::singleShot(2000, [splash]() {
        splash->showMessage("正在连接机器人状态节点...", Qt::AlignBottom | Qt::AlignCenter, Qt::white);
    });
    
    QTimer::singleShot(3000, [splash]() {
        splash->showMessage("初始化完成", Qt::AlignBottom | Qt::AlignCenter, Qt::white);
    });
    
    // 从ROS参数中获取命名空间
    QString namespace_prefix = "";  // 默认值为空

    // 创建临时节点来读取参数
    auto temp_node = rclcpp::Node::make_shared("temp_param_reader");
    temp_node->declare_parameter("namespace", "chassis");
    namespace_prefix = QString::fromStdString(temp_node->get_parameter("namespace").as_string());

    // 创建主窗口
    robotcar_gui::RobotCarMainWindow *mainWindow = nullptr;
    robotcar_gui::WelcomeWindow *welcomeWindow = nullptr;

    // 根据命令行参数决定启动方式
    if (skip_welcome) {
        // 直接启动主界面
        QTimer::singleShot(3500, [&, namespace_prefix]() {
            splash->finish(nullptr);
            delete splash;

            mainWindow = new robotcar_gui::RobotCarMainWindow(nullptr);

            if (fullscreen) {
                mainWindow->showFullScreen();
            } else {
                mainWindow->show();
            }
        });
    } else {
        // 先显示欢迎界面
        QTimer::singleShot(3500, [&, namespace_prefix]() {
            splash->finish(nullptr);
            delete splash;

            welcomeWindow = new robotcar_gui::WelcomeWindow();

            // 连接欢迎界面到主界面的信号
            QObject::connect(welcomeWindow, &robotcar_gui::WelcomeWindow::startMainApplication,
                           [&, namespace_prefix]() {
                               welcomeWindow->hide();

                               mainWindow = new robotcar_gui::RobotCarMainWindow(nullptr);

                               if (fullscreen) {
                                   mainWindow->showFullScreen();
                               } else {
                                   mainWindow->show();
                               }

                               // 延迟删除欢迎窗口
                               QTimer::singleShot(1000, [welcomeWindow]() {
                                   delete welcomeWindow;
                               });
                           });

            if (fullscreen) {
                welcomeWindow->showFullScreen();
            } else {
                welcomeWindow->show();
            }
        });
    }
    
    // 运行应用程序
    int result = app.exec();
    
    // 清理资源
    if (mainWindow) {
        delete mainWindow;
    }
    if (welcomeWindow) {
        delete welcomeWindow;
    }
    
    rclcpp::shutdown();
    
    return result;
}
