from launch import LaunchDescription
from launch_ros.actions import Node

def generate_launch_description():
    return LaunchDescription([
        Node(
            package='robot_arm',  # 替换为你的实际包名
            executable='arm_action_server',  # 可执行文件名（CMakeLists.txt install 后的目标）
            name='arm_action_server',
            output='screen',
            parameters=[{
                'server_ip': '*************',
                'server_port': 502
            }]
        )
    ])
