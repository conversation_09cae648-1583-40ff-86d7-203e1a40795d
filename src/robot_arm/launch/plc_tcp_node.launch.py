from launch import LaunchDescription
from launch_ros.actions import Node

def generate_launch_description():
    return LaunchDescription([
        Node(
            package='robot_arm', 
            executable='plc_tcp',  
            name='plc_tcp',
            output='screen',
            parameters=[{
                'server_ip': '127.0.0.1',
                'server_port': 2000
            }]
        )
    ])
