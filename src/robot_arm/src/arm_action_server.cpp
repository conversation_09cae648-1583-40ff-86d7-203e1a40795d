#include <rclcpp/rclcpp.hpp>
#include <rclcpp_action/rclcpp_action.hpp>
#include <robot_arm/action/arm_command.hpp>
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <unistd.h>
#include <cstring>
#include <mutex>
#include <memory>
#include <thread>
#include <atomic>
#include <cmath>
#include <condition_variable>
#include <csignal>
#include "geometry_msgs/msg/pose.hpp"
#include "tf2_geometry_msgs/tf2_geometry_msgs.hpp"

#pragma pack (1)
typedef struct _CMD_HAND_
{
    short flag_1;
    short flag_2;
    short length;
    char  addr;
    char  func;
    short reg_addr;
    short reg_num;
    char  reg_length;
    uint32_t pose_x;
    uint32_t pose_y;
    uint32_t pose_z;
    uint32_t pose_A;
    uint32_t pose_B;
}_CMD_HAND_;

#pragma pack ()
// 网络字节序处理宏
#define SWAP_32(x)  ((x & 0x000000FF) << 8 | (x & 0x0000FF00) >> 8  | (x & 0x00FF0000) << 8  | (x & 0xFF000000) >> 8 )

static uint8_t s_buffer[sizeof(_CMD_HAND_)];

using ArmCommand = robot_arm::action::ArmCommand;
using GoalHandle = rclcpp_action::ServerGoalHandle<ArmCommand>;

class ArmActionServer : public rclcpp::Node {
public:
    explicit ArmActionServer(const rclcpp::NodeOptions & options = rclcpp::NodeOptions())
        : Node("arm_action_server", options)
    {
        this->declare_parameter<std::string>("server_ip", "*************");
        this->declare_parameter<int>("server_port", 502);

        server_ip_ = this->get_parameter("server_ip").as_string();
        server_port_ = this->get_parameter("server_port").as_int();

        pose_publisher_ = this->create_publisher<geometry_msgs::msg::Pose>("arm_current_pose", 10);

        action_server_ = rclcpp_action::create_server<ArmCommand>(
            this,
            "arm_command",
            std::bind(&ArmActionServer::handle_goal, this, std::placeholders::_1, std::placeholders::_2),
            std::bind(&ArmActionServer::handle_cancel, this, std::placeholders::_1),
            std::bind(&ArmActionServer::handle_accepted, this, std::placeholders::_1));

        // 先尝试连接
        if(!connectToArm()) {
            RCLCPP_ERROR(this->get_logger(), "Failed to connect to arm controller on startup");
        }

        running_ = true;

        // 启动后台位姿查询线程
        pose_monitor_thread_ = std::thread([this]() {
            rclcpp::Rate rate(5.0); 
            while (rclcpp::ok() && running_) {
                float pose[4];
                if (getCurrentPose(pose)) {
                    std::lock_guard<std::mutex> lock(pose_mutex_);
                    std::copy(pose, pose + 4, current_pose_);
                }
                // 发布话题
                geometry_msgs::msg::Pose msg;
                msg.position.x = pose[0]/1000;
                msg.position.y = pose[1]/1000;
                msg.position.z = pose[2]/1000;

                tf2::Quaternion q;
                q.setRPY(0, 0, pose[3]*3.1415926/180); // yaw 转四元数
                msg.orientation = tf2::toMsg(q);

                pose_publisher_->publish(msg);

                rate.sleep();
            }
        });

        // 连接维护线程：断线时每隔5秒重连
        connection_maintainer_thread_ = std::thread([this]() {
            rclcpp::Rate rate(0.2); // 5秒一次
            while (rclcpp::ok() && running_) {
                {
                    std::lock_guard<std::mutex> lock(connection_mutex_);
                    if (sock_ < 0) {
                        RCLCPP_WARN(this->get_logger(), "Socket disconnected, trying to reconnect...");
                        if (connectToArm()) {
                            RCLCPP_INFO(this->get_logger(), "Reconnected to arm controller");
                        }
                    }
                }
                rate.sleep();
            }
        });

        RCLCPP_INFO(this->get_logger(), "Arm Action Server started");
    }

    ~ArmActionServer() {
        running_ = false;

        if (pose_monitor_thread_.joinable()) {
            pose_monitor_thread_.join();
        }

        if (connection_maintainer_thread_.joinable()) {
            connection_maintainer_thread_.join();
        }

        std::lock_guard<std::mutex> lock(connection_mutex_);
        if(sock_ != -1) {
            close(sock_);
            sock_ = -1;
        }
    }

private:
    rclcpp_action::Server<ArmCommand>::SharedPtr action_server_;
    int sock_ = -1;
    std::string server_ip_;
    int server_port_;
    std::mutex connection_mutex_;
    rclcpp::Publisher<geometry_msgs::msg::Pose>::SharedPtr pose_publisher_;

    std::thread pose_monitor_thread_;
    std::thread connection_maintainer_thread_;
    std::atomic<bool> running_{false};
    float current_pose_[4] = {0};
    std::mutex pose_mutex_;

    bool connectToArm() {
        if(sock_ != -1) {
            close(sock_);
            sock_ = -1;
        }

        sock_ = socket(AF_INET, SOCK_STREAM, 0);
        if(sock_ < 0) {
            RCLCPP_ERROR(this->get_logger(), "Socket creation error");
            return false;
        }

        struct sockaddr_in serv_addr;
        memset(&serv_addr, 0, sizeof(serv_addr));
        serv_addr.sin_family = AF_INET;
        serv_addr.sin_port = htons(server_port_);

        if(inet_pton(AF_INET, server_ip_.c_str(), &serv_addr.sin_addr) <= 0) {
            RCLCPP_ERROR(this->get_logger(), "Invalid address/Address not supported");
            close(sock_);
            sock_ = -1;
            return false;
        }

        if(connect(sock_, (struct sockaddr *)&serv_addr, sizeof(serv_addr)) < 0) {
            RCLCPP_ERROR(this->get_logger(), "Connection failed");
            close(sock_);
            sock_ = -1;
            return false;
        }

        RCLCPP_INFO(this->get_logger(), "Connected to arm controller at %s:%d",
                   server_ip_.c_str(), server_port_);
        return true;
    }

    rclcpp_action::GoalResponse handle_goal(
        const rclcpp_action::GoalUUID & uuid,
        std::shared_ptr<const ArmCommand::Goal> goal) {
        (void)uuid;
        RCLCPP_INFO(this->get_logger(), "Received new goal to move to (%.1f, %.1f, %.1f, %.1f)",
                   goal->target_x, goal->target_y, goal->target_z, goal->target_a);
        return rclcpp_action::GoalResponse::ACCEPT_AND_EXECUTE;
    }

    rclcpp_action::CancelResponse handle_cancel(const std::shared_ptr<GoalHandle> goal_handle) {
        RCLCPP_INFO(this->get_logger(), "Received request to cancel goal");
        (void)goal_handle;
        return rclcpp_action::CancelResponse::ACCEPT;
    }

    void handle_accepted(const std::shared_ptr<GoalHandle> goal_handle) {
        std::thread{std::bind(&ArmActionServer::execute, this, std::placeholders::_1), goal_handle}.detach();
    }

    void execute(const std::shared_ptr<GoalHandle> goal_handle) {
        const auto goal = goal_handle->get_goal();
        auto result = std::make_shared<ArmCommand::Result>();
        auto feedback = std::make_shared<ArmCommand::Feedback>();

        if (!checkConnection()) {
            result->success = false;
            result->message = "Connection to arm controller failed";
            goal_handle->abort(result);
            return;
        }

        try {
            feedback->status = "Sending move command";
            goal_handle->publish_feedback(feedback);

            if (!sendMoveCommand(goal->target_x, goal->target_y,
                                 goal->target_z, goal->target_a)) {
                throw std::runtime_error("Failed to send move command");
            }

            feedback->status = "Moving...";
            goal_handle->publish_feedback(feedback);

            rclcpp::Rate rate(5.0);
            const float tolerance = 5.0;

            while (rclcpp::ok()) {
                if (goal_handle->is_canceling()) {
                    result->success = false;
                    result->message = "Goal canceled";
                    goal_handle->canceled(result);
                    return;
                }

                float cur[4];
                {
                    std::lock_guard<std::mutex> lock(pose_mutex_);
                    std::copy(current_pose_, current_pose_ + 4, cur);
                }

                feedback->current_x = cur[0];
                feedback->current_y = cur[1];
                feedback->current_z = cur[2];
                feedback->current_a = cur[3];
                goal_handle->publish_feedback(feedback);

                float dx = std::abs(goal->target_x - cur[0]);
                float dy = std::abs(goal->target_y - cur[1]);
                float dz = std::abs(goal->target_z - cur[2]);
                float da = std::abs(goal->target_a - cur[3]);

                if (dx < tolerance && dy < tolerance && dz < tolerance && da < tolerance) {
                    result->success = true;
                    result->message = "Target position reached";
                    goal_handle->succeed(result);
                    return;
                }

                rate.sleep();
            }

            result->success = false;
            result->message = "Execution interrupted";
            goal_handle->abort(result);

        } catch (const std::exception& e) {
            result->success = false;
            result->message = std::string("Exception: ") + e.what();
            goal_handle->abort(result);
            RCLCPP_ERROR(this->get_logger(), "Execution failed: %s", e.what());
        }
    }

    bool checkConnection() {
        std::lock_guard<std::mutex> lock(connection_mutex_);
        if(sock_ < 0) {
            return connectToArm();
        }
        return true;
    }

    bool sendMoveCommand(float x, float y, float z, float a) {
        std::lock_guard<std::mutex> lock(connection_mutex_);
        if (sock_ < 0) return false;

        _CMD_HAND_* cmd_hand = new _CMD_HAND_;
        cmd_hand->flag_1      = htons(0x48DA);
        cmd_hand->flag_2      = htons(0x0000);
        cmd_hand->length      = htons(0x001B);
        cmd_hand->addr        = 0x01;
        cmd_hand->func        = 0x10;
        cmd_hand->reg_addr    = htons(0x62D4);
        cmd_hand->reg_num     = htons(0x000A);
        cmd_hand->reg_length  = 0x14;

        memcpy(&cmd_hand->pose_x, &x, sizeof(float));
        cmd_hand->pose_x = SWAP_32(cmd_hand->pose_x);

        memcpy(&cmd_hand->pose_y, &y, sizeof(float));
        cmd_hand->pose_y = SWAP_32(cmd_hand->pose_y);

        memcpy(&cmd_hand->pose_z, &z, sizeof(float));
        cmd_hand->pose_z = SWAP_32(cmd_hand->pose_z);

        memcpy(&cmd_hand->pose_A, &a, sizeof(float));
        cmd_hand->pose_A = SWAP_32(cmd_hand->pose_A);

        cmd_hand->pose_B = 0;

        memcpy(s_buffer, cmd_hand, sizeof(_CMD_HAND_));

        std::cout << "Sending data (" << sizeof(_CMD_HAND_) << " bytes): ";
        for (size_t i = 0; i < sizeof(_CMD_HAND_); ++i) {
            printf("%02X ", s_buffer[i]);
        }
        std::cout << std::endl;

        int send_result = send(sock_, s_buffer, sizeof(_CMD_HAND_), 0);
        delete cmd_hand;

        if (send_result < 0) {
            RCLCPP_ERROR(this->get_logger(), "Send failed");
            close(sock_);
            sock_ = -1;
            return false;
        }

        uint8_t reply[32];
        int len = recv(sock_, reply, sizeof(reply), 0);
        if (len <= 0) {
            RCLCPP_ERROR(this->get_logger(), "No response from arm");
            close(sock_);
            sock_ = -1;
            return false;
        }

        return true;
    }

    bool getCurrentPose(float* pose_out, int size = 4) {
        std::lock_guard<std::mutex> lock(connection_mutex_);
        if (sock_ < 0 || pose_out == nullptr || size < 4) return false;

        uint8_t buf[12] = {
            0x03, 0xB1, 0x00, 0x00, 0x00, 0x06,
            0x01, 0x03, 0x09, 0x8E, 0x00, 0x08
        };

        if (send(sock_, buf, 12, 0) < 0) {
            RCLCPP_ERROR(this->get_logger(), "Failed to send pose request, errno=%d: %s", errno, strerror(errno));
            close(sock_);
            sock_ = -1;
            return false;
        }

        uint8_t recbuf[128] = {0};
        int recv_len = recv(sock_, recbuf, sizeof(recbuf), 0);
        if (recv_len <= 0) {
            RCLCPP_ERROR(this->get_logger(), "Failed to receive pose response");
            close(sock_);
            sock_ = -1;
            return false;
        }

        if (recbuf[0] == 0x03 && recbuf[1] == 0xB1 &&
            recbuf[5] == 0x13 && recbuf[6] == 0x01) {

            uint32_t temp[4] = {0};
            memcpy(temp, &recbuf[9], 4 * sizeof(uint32_t));
            for (int i = 0; i < 4; ++i) {
                temp[i] = SWAP_32(temp[i]);//交换全字节
                memcpy(&pose_out[i], &temp[i], sizeof(float));
                // RCLCPP_INFO(this->get_logger(), "pose_out[%d]= %f",i,pose_out[i]);
            }

            return true;
        }

        return false;
    }

    bool getLatestPose(float* pose_out) {
        std::lock_guard<std::mutex> lock(pose_mutex_);
        std::copy(current_pose_, current_pose_ + 4, pose_out);
        return true;
    }
};

int main(int argc, char **argv) {
    // 忽略 SIGPIPE 信号，防止 broken pipe 崩溃
    signal(SIGPIPE, SIG_IGN);

    rclcpp::init(argc, argv);
    auto node = std::make_shared<ArmActionServer>();
    rclcpp::spin(node);
    rclcpp::shutdown();
    return 0;
}
