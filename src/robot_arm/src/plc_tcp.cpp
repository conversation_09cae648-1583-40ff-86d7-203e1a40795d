#include <rclcpp/rclcpp.hpp>
#include <std_msgs/msg/int8.hpp>
#include <std_msgs/msg/int8_multi_array.hpp>

#include <thread>
#include <atomic>
#include <cstring>
#include <iostream>
#include <sys/socket.h>
#include <arpa/inet.h>
#include <unistd.h>
#include <chrono>

class PLCTcpNode : public rclcpp::Node {
public:
    PLCTcpNode() : Node("plc_tcp_node"), plc_flag_(0), thread_tick_(10), socket_fd_(-1), running_(true)
    {
        this->declare_parameter<std::string>("server_ip", "127.0.0.1");
        this->declare_parameter<int>("server_port", 2000);
        this->get_parameter("server_ip", server_ip_);
        this->get_parameter("server_port", server_port_);

        pub_ = this->create_publisher<std_msgs::msg::Int8>("PLC_DATA", 10);
        sub_ = this->create_subscription<std_msgs::msg::Int8MultiArray>(
            "Hand_STATU", 10, 
            std::bind(&PLCTcpNode::handCallback, this, std::placeholders::_1)
        );

        // 启动接收线程（含自动重连逻辑）
        recv_thread_ = std::thread(&PLCTcpNode::recvThreadWithReconnect, this);

        // 发送+发布定时器
        timer_ = this->create_wall_timer(
            std::chrono::seconds(1),
            std::bind(&PLCTcpNode::timerCallback, this)
        );
    }

    ~PLCTcpNode() {
        running_ = false;
        if (recv_thread_.joinable()) {
            recv_thread_.join();
        }
        if (socket_fd_ >= 0) {
            close(socket_fd_);
        }
    }

private:
    rclcpp::Publisher<std_msgs::msg::Int8>::SharedPtr pub_;
    rclcpp::Subscription<std_msgs::msg::Int8MultiArray>::SharedPtr sub_;
    rclcpp::TimerBase::SharedPtr timer_;
    std::string server_ip_;
    int server_port_;

    std::atomic<uint8_t> plc_flag_;
    std::atomic<int> thread_tick_;
    int socket_fd_;

    std::thread recv_thread_;
    std::atomic<bool> running_;
    
    uint8_t send_buf_[2] = {0};
    uint8_t recv_buf_[5] = {0};

    bool connectToServer() {
        if (socket_fd_ >= 0) {
            close(socket_fd_);
            socket_fd_ = -1;
        }
        socket_fd_ = socket(AF_INET, SOCK_STREAM, 0);
        if (socket_fd_ < 0) {
            RCLCPP_ERROR(this->get_logger(), "socket 创建失败");
            return false;
        }

        sockaddr_in server_addr{};
        server_addr.sin_family = AF_INET;
        server_addr.sin_port = htons(server_port_);
        if (inet_pton(AF_INET, server_ip_.c_str(), &server_addr.sin_addr) <= 0) {
            RCLCPP_ERROR(this->get_logger(), "无效的 IP 地址");
            close(socket_fd_);
            socket_fd_ = -1;
            return false;
        }

        if (connect(socket_fd_, (sockaddr *)&server_addr, sizeof(server_addr)) < 0) {
            RCLCPP_ERROR(this->get_logger(), "无法连接到 PLC");
            close(socket_fd_);
            socket_fd_ = -1;
            return false;
        }

        RCLCPP_INFO(this->get_logger(), "成功连接到 PLC 服务器");
        return true;
    }

    void recvThreadWithReconnect() {
        while (running_) {
            if (!connectToServer()) {
                RCLCPP_WARN(this->get_logger(), "连接失败,5秒后重试...");
                std::this_thread::sleep_for(std::chrono::seconds(5));
                continue;
            }

            // 连接成功，开始接收数据
            while (running_) {
                ssize_t len = recv(socket_fd_, recv_buf_, sizeof(recv_buf_), 0);
                if (len <= 0) {
                    // RCLCPP_WARN(this->get_logger(), "连接断开或接收失败，准备重连...");
                    close(socket_fd_);
                    socket_fd_ = -1;
                    break;  // 跳出接收循环，重新连接
                }

                // std::stringstream ss;
                // for (int i = 0; i < len; ++i) {
                //     ss << std::hex << static_cast<int>(recv_buf_[i]) << " ";
                // }
                // RCLCPP_INFO(this->get_logger(), "接收: %s", ss.str().c_str());

                plc_flag_ = recv_buf_[0];
            }
        }
    }

    void handCallback(const std_msgs::msg::Int8MultiArray::SharedPtr msg) {
        if (!msg->data.empty()) {
            send_buf_[0] = msg->data[0];
            RCLCPP_INFO(this->get_logger(), "设置 send_buf[0]: %d", send_buf_[0]);
        }
    }

    void timerCallback() {
        std_msgs::msg::Int8 msg;
        msg.data = plc_flag_;
        pub_->publish(msg);

        send_buf_[1] = 0x0A;
        if (socket_fd_ >= 0) {
            ssize_t sent = send(socket_fd_, send_buf_, 2, 0);
            if (sent < 0) {
                // RCLCPP_ERROR(this->get_logger(), "发送失败，关闭连接准备重连");
                close(socket_fd_);
                socket_fd_ = -1;
            }
        } else {
            // RCLCPP_WARN(this->get_logger(), "socket 未连接，跳过发送");
        }
    }
};

int main(int argc, char **argv) {
    signal(SIGPIPE, SIG_IGN);
    rclcpp::init(argc, argv);
    auto node = std::make_shared<PLCTcpNode>();
    rclcpp::spin(node);
    rclcpp::shutdown();
    return 0;
}
