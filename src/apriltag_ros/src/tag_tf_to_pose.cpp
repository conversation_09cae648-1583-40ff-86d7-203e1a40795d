#include <rclcpp/rclcpp.hpp>
#include <geometry_msgs/msg/pose_stamped.hpp>
#include <tf2_ros/transform_listener.h>
#include <tf2_ros/buffer.h>
#include <tf2_geometry_msgs/tf2_geometry_msgs.hpp>

class TagTfToPoseNode : public rclcpp::Node
{
public:
  TagTfToPoseNode() : Node("tag_tf_to_pose_node"), tf_buffer_(this->get_clock()), tf_listener_(tf_buffer_)
  {
    pose_pub_ = this->create_publisher<geometry_msgs::msg::PoseStamped>("/detected_dock_pose", 10);

    // 声明参数（tag 的 frame id，camera 的 frame id）
    this->declare_parameter<std::string>("target_tag_frame", "Tag0");
    this->declare_parameter<std::string>("camera_frame", "camera");

    target_tag_frame_ = this->get_parameter("target_tag_frame").as_string();
    camera_frame_ = this->get_parameter("camera_frame").as_string();

    timer_ = this->create_wall_timer(
      std::chrono::milliseconds(200),
      std::bind(&TagTfToPoseNode::publishPose, this));
  }

private:
  rclcpp::Publisher<geometry_msgs::msg::PoseStamped>::SharedPtr pose_pub_;
  tf2_ros::Buffer tf_buffer_;
  tf2_ros::TransformListener tf_listener_;
  rclcpp::TimerBase::SharedPtr timer_;

  std::string target_tag_frame_;
  std::string camera_frame_;

  void publishPose()
  {
    geometry_msgs::msg::TransformStamped transformStamped;

    try
    {
      // 查找 tag 相对 camera 的坐标
      transformStamped = tf_buffer_.lookupTransform(
        camera_frame_, target_tag_frame_,
        tf2::TimePointZero);

      geometry_msgs::msg::PoseStamped pose_msg;
      pose_msg.header.stamp = this->now();
      pose_msg.header.frame_id = camera_frame_;

      pose_msg.pose.position.x = transformStamped.transform.translation.x;
      pose_msg.pose.position.y = transformStamped.transform.translation.y;
      pose_msg.pose.position.z = transformStamped.transform.translation.z;

      pose_msg.pose.orientation = transformStamped.transform.rotation;

      pose_pub_->publish(pose_msg);

    //   RCLCPP_INFO_THROTTLE(this->get_logger(), *this->get_clock(), 2000,
    //                        "Published dock pose from TF %s -> %s",
    //                        camera_frame_.c_str(), target_tag_frame_.c_str());
    }
    catch (tf2::TransformException &ex)
    {
      RCLCPP_WARN_THROTTLE(this->get_logger(), *this->get_clock(), 5000,
                           "Could not transform %s to %s: %s",
                           target_tag_frame_.c_str(), camera_frame_.c_str(), ex.what());
    }
  }
};

int main(int argc, char **argv)
{
  rclcpp::init(argc, argv);
  auto node = std::make_shared<TagTfToPoseNode>();
  rclcpp::spin(node);
  rclcpp::shutdown();
  return 0;
}
