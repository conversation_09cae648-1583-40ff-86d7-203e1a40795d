# 地图旋转节点参数配置文件
# {{ AURA-X: Add - 分辨率自适应参数配置. Approval: 寸止(ID:map_rotation_params). }}

map_rotation_node:
  ros__parameters:
    # 最小检测线段长度（米）
    # 建议值：2.0-5.0米，根据建图环境的典型墙壁长度调整
    min_line_length_meters: 3.0
    
    # 角度更新阈值（度）
    # 建议值：0.5-2.0度，过小会频繁更新，过大会错过小幅调整
    angle_update_threshold_deg: 1.0

# 不同分辨率的推荐配置示例：

# === 高精度建图 (0.01-0.02 m/pixel) ===
# 适用场景：精密室内建图、小型机器人
# map_rotation_node:
#   ros__parameters:
#     min_line_length_meters: 2.0    # 较短的最小线段
#     angle_update_threshold_deg: 0.5 # 更敏感的角度检测

# === 标准建图 (0.03-0.05 m/pixel) ===
# 适用场景：一般室内建图、中型机器人（当前默认）
# map_rotation_node:
#   ros__parameters:
#     min_line_length_meters: 3.0    # 标准线段长度
#     angle_update_threshold_deg: 1.0 # 标准角度阈值

# === 快速建图 (0.05-0.1 m/pixel) ===
# 适用场景：大范围建图、户外环境
# map_rotation_node:
#   ros__parameters:
#     min_line_length_meters: 5.0    # 较长的最小线段
#     angle_update_threshold_deg: 2.0 # 较宽松的角度阈值

# === 粗糙建图 (0.1+ m/pixel) ===
# 适用场景：大型环境快速扫描
# map_rotation_node:
#   ros__parameters:
#     min_line_length_meters: 8.0    # 长线段检测
#     angle_update_threshold_deg: 3.0 # 宽松角度阈值
