# 路网导航路点配置文件示例
# 定义机器人可以导航到的预设路点和它们之间的连接关系

waypoints:
  # 入口区域
  - name: "entrance"
    description: "入口区域"
    position:
      x: 0.0
      y: 0.0
      z: 0.0
    orientation:
      x: 0.0
      y: 0.0
      z: 0.0
      w: 1.0
    connections: ["corridor_1", "reception"]
    
  # 接待区
  - name: "reception"
    description: "接待区"
    position:
      x: 2.0
      y: 1.0
      z: 0.0
    orientation:
      x: 0.0
      y: 0.0
      z: 0.707
      w: 0.707
    connections: ["entrance", "corridor_1", "office_1"]
    
  # 走廊1
  - name: "corridor_1"
    description: "主走廊1"
    position:
      x: 5.0
      y: 0.0
      z: 0.0
    orientation:
      x: 0.0
      y: 0.0
      z: 0.0
      w: 1.0
    connections: ["entrance", "reception", "corridor_2", "office_1", "office_2"]
    
  # 走廊2
  - name: "corridor_2"
    description: "主走廊2"
    position:
      x: 10.0
      y: 0.0
      z: 0.0
    orientation:
      x: 0.0
      y: 0.0
      z: 0.0
      w: 1.0
    connections: ["corridor_1", "office_3", "office_4", "meeting_room"]
    
  # 办公室1
  - name: "office_1"
    description: "办公室1"
    position:
      x: 3.0
      y: 3.0
      z: 0.0
    orientation:
      x: 0.0
      y: 0.0
      z: 1.0
      w: 0.0
    connections: ["reception", "corridor_1"]
    
  # 办公室2
  - name: "office_2"
    description: "办公室2"
    position:
      x: 7.0
      y: 3.0
      z: 0.0
    orientation:
      x: 0.0
      y: 0.0
      z: 1.0
      w: 0.0
    connections: ["corridor_1"]
    
  # 办公室3
  - name: "office_3"
    description: "办公室3"
    position:
      x: 8.0
      y: -3.0
      z: 0.0
    orientation:
      x: 0.0
      y: 0.0
      z: 0.0
      w: 1.0
    connections: ["corridor_2"]
    
  # 办公室4
  - name: "office_4"
    description: "办公室4"
    position:
      x: 12.0
      y: -3.0
      z: 0.0
    orientation:
      x: 0.0
      y: 0.0
      z: 0.0
      w: 1.0
    connections: ["corridor_2"]
    
  # 会议室
  - name: "meeting_room"
    description: "会议室"
    position:
      x: 13.0
      y: 2.0
      z: 0.0
    orientation:
      x: 0.0
      y: 0.0
      z: 0.707
      w: 0.707
    connections: ["corridor_2"]
    
  # 充电站
  - name: "charging_station"
    description: "充电站"
    position:
      x: 1.0
      y: -2.0
      z: 0.0
    orientation:
      x: 0.0
      y: 0.0
      z: -0.707
      w: 0.707
    connections: ["entrance"]

# 路网配置
network:
  # 默认导航参数
  default_tolerance: 0.5  # 到达路点的容差 (米)
  max_speed: 0.5         # 最大速度 (m/s)
  
  # 路径规划参数
  path_planning:
    algorithm: "dijkstra"  # 路径规划算法: dijkstra, a_star
    avoid_obstacles: true  # 是否避障
    
  # 安全参数
  safety:
    min_distance_to_obstacle: 0.3  # 与障碍物的最小距离 (米)
    emergency_stop_distance: 0.2   # 紧急停车距离 (米)
