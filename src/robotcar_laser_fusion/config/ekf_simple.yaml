### EKF (Extended Kalman Filter) 配置文件 ###
# 用于机器人状态估计和传感器融合的扩展卡尔曼滤波器配置
# 该配置融合里程计和IMU数据，提供更准确的机器人位姿估计

ekf_filter_node:
    ros__parameters:
        # ========== 基本参数设置 ==========
        frequency: 50.0  # EKF滤波器运行频率(Hz)，匹配里程计频率
                        # 建议设置为传感器数据中最高频率的1-2倍
                        # 降低到10Hz以提高性能，对于大多数应用已足够

        two_d_mode: true  # 启用2D模式，忽略Z轴位置、roll和pitch角度
                         # 对于地面移动机器人，通常设置为true

        publish_acceleration: false  # 关闭加速度发布以提高性能
                                    # 如果下游节点需要加速度数据则设置为true

        publish_tf: true  # 启用EKF的TF发布，统一管理odom->base_footprint变换
                         # EKF融合多传感器数据后发布高质量的TF变换

        predict_to_current_time: true  # 启用预测到当前时间，提高实时性

        print_diagnostics: false  # 关闭诊断输出以提高性能，调试时可设为true

        # ========== 传感器超时和队列优化 ==========
        sensor_timeout: 0.1      # 传感器数据超时时间(秒)
        transform_timeout: 0.1   # TF变换超时时间(秒)
        transform_time_offset: 0.0  # TF时间偏移

        # ========== 坐标系定义 ==========
        # {{ AURA-X: Fix - 改为标准坐标系名称，由PushRosNamespace自动添加命名空间. Approval: 寸止(ID:1735659700). }}
        map_frame: map                          # 地图坐标系名称，全局固定参考系
        odom_frame: odom                       # 里程计坐标系名称，标准名称
        base_link_frame: base_footprint        # 机器人本体坐标系名称，标准名称
        world_frame: odom                      # 世界坐标系，EKF输出的参考系

        # ========== 里程计传感器配置 ==========
        odom0: diff_drive_controller/odom  # 第一个里程计数据源话题（相对路径，支持命名空间）
                                          # 来自差分驱动控制器的原始里程计数据

        # 里程计数据使用配置 (15个布尔值对应15个状态变量)
        # 顺序: [x, y, z, roll, pitch, yaw, vx, vy, vz, vroll, vpitch, vyaw, ax, ay, az]
        odom0_config: [true,  true,  false,   # 位置: 使用x,y，忽略z
                       false, false, true,    # 姿态: 忽略roll,pitch，使用yaw
                       true,  false, false,   # 线速度: 使用vx，忽略vy,vz
                       false, false, true,    # 角速度: 忽略vroll,vpitch，使用vyaw
                       false, false, false]   # 加速度: 全部忽略(里程计通常不提供)

        odom0_differential: false  # 是否将里程计数据作为差分数据处理
                                  # false表示使用绝对位置数据

        odom0_queue_size: 10      # 增加队列大小适应低频数据
        odom0_nodelay: false      # 启用数据缓冲

        # ========== IMU传感器配置 ==========
        imu0: imu  # IMU数据源话题，来自HWT901B-TTL IMU设备

        # IMU数据使用配置 (15个布尔值对应15个状态变量)
        # 顺序: [x, y, z, roll, pitch, yaw, vx, vy, vz, vroll, vpitch, vyaw, ax, ay, az]
        #
        # 推荐配置选项:
        # 选项1: 仅角速度 (保守配置)
        # imu0_config: [false, false, false, false, false, false, false, false, false, false, false, true, false, false, false]
        #
        # 选项2: 角速度 + yaw姿态 (推荐用于2D导航)
        imu0_config: [false, false, false,  # 位置: IMU不提供位置信息
                      false, false, true,   # 姿态: 使用yaw角，忽略roll/pitch(2D模式)
                      false, false, false,  # 线速度: IMU不直接提供线速度
                      false, false, true,   # 角速度: 仅使用yaw角速度(2D导航关键)
                      false, false, false]  # 加速度: 暂不使用(可选择启用)
        #
        # 选项3: 角速度 + 加速度 (用于更精确的运动估计)
        # imu0_config: [false, false, false, false, false, false, false, false, false, false, false, true, true, true, false]
        #
        # 当前选择: 选项2 - yaw姿态 + yaw角速度
        # 原因: 2D导航中yaw角是最重要的，IMU的yaw测量通常比里程计更准确

        imu0_differential: false  # IMU数据不作为差分数据处理

        imu0_remove_gravitational_acceleration: true  # 移除重力加速度影响
                                                     # 对于移动机器人通常设置为true

        imu0_queue_size: 10       # 增加队列大小
        imu0_nodelay: false       # 启用数据缓冲

        # ========== 过程噪声协方差矩阵 ==========
        # 定义系统模型预测的不确定性，15x15对角矩阵
        # 对角线元素对应: [x, y, z, roll, pitch, yaw, vx, vy, vz, vroll, vpitch, vyaw, ax, ay, az]
        # 较大的值表示对该状态变量的预测不确定性更高
        # 调优指南:
        # - 如果机器人运动平滑但EKF输出抖动，减小相应值
        # - 如果EKF响应传感器数据太慢，增大相应值
        # - 位置相关(x,y,yaw)值通常设置为0.01-0.1
        # - 速度相关值通常比位置值小一个数量级
        process_noise_covariance: [
            0.01, 0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,   # x
            0.0,  0.01, 0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,   # y
            0.0,  0.0,  0.02, 0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,   # z
            0.0,  0.0,  0.0,  0.01, 0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,   # roll
            0.0,  0.0,  0.0,  0.0,  0.01, 0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,   # pitch
            0.0,  0.0,  0.0,  0.0,  0.0,  0.02, 0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,   # yaw
            0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.01, 0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,   # vx
            0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.01, 0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,   # vy
            0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.02, 0.0,  0.0,  0.0,  0.0,  0.0,  0.0,   # vz
            0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.005, 0.0,  0.0,  0.0,  0.0,  0.0,  # vroll
            0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.005, 0.0,  0.0,  0.0,  0.0,  # vpitch
            0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.01, 0.0,  0.0,  0.0,  # vyaw
            0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.005, 0.0,  0.0,  # ax
            0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.005, 0.0,  # ay
            0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.01   # az
        ]

        # ========== 初始估计误差协方差矩阵 ==========
        # 定义系统启动时对初始状态估计的不确定性，15x15对角矩阵
        # 对角线元素对应: [x, y, z, roll, pitch, yaw, vx, vy, vz, vroll, vpitch, vyaw, ax, ay, az]
        # 较小的值(如1e-9)表示对初始状态非常确信
        # 较大的值表示对初始状态不确定，EKF需要更多时间收敛
        # 调优指南:
        # - 如果机器人总是从已知位置启动，使用小值(1e-9)
        # - 如果启动位置不确定，增大位置相关的值(如0.1-1.0)
        # - 速度和加速度通常可以假设为0，使用小值
        initial_estimate_covariance: [
            1e-9, 0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,
            0.0,  1e-9, 0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,
            0.0,  0.0,  1e-9, 0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,
            0.0,  0.0,  0.0,  1e-9, 0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,
            0.0,  0.0,  0.0,  0.0,  1e-9, 0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,
            0.0,  0.0,  0.0,  0.0,  0.0,  1e-9, 0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,
            0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  1e-9, 0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,
            0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  1e-9, 0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,
            0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  1e-9, 0.0,  0.0,  0.0,  0.0,  0.0,  0.0,
            0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  1e-9, 0.0,  0.0,  0.0,  0.0,  0.0,
            0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  1e-9, 0.0,  0.0,  0.0,  0.0,
            0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  1e-9, 0.0,  0.0,  0.0,
            0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  1e-9, 0.0,  0.0,
            0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  1e-9, 0.0,
            0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  1e-9
        ]

# ========== EKF配置总结 ==========
# 当前配置适用于:
# 1. 差分驱动地面机器人(2D运动)
# 2. 融合里程计(位置、速度)和IMU(角速度、yaw角)数据
# 3. 输出频率10Hz，平衡性能和精度
# 4. 支持命名空间配置，兼容多机器人系统
#
# TF变换链: map -> odom -> base_footprint (命名空间由PushRosNamespace自动添加)
#           ↑       ↑            ↑
#      Cartographer EKF  diff_drive_controller
#
# 调优建议:
# 1. 如果输出抖动: 减小process_noise_covariance对应值
# 2. 如果响应慢: 增大process_noise_covariance对应值
# 3. 如果启动收敛慢: 增大initial_estimate_covariance对应值
# 4. 根据实际传感器性能调整各传感器的config配置
# 5. 频率调整: 根据系统性能需求调整frequency参数
#
# 常用调优参数:
# - frequency: 5-50Hz (推荐10Hz)
# - process_noise_covariance中的yaw值: 0.01-0.1
# - IMU配置: 根据IMU质量选择使用yaw角度或仅角速度
#
# 故障排除:
# - 如果EKF不收敛: 检查传感器话题是否正确发布
# - 如果TF错误: 检查坐标系名称是否与实际系统匹配
# - 如果性能问题: 降低frequency或关闭不必要的功能
