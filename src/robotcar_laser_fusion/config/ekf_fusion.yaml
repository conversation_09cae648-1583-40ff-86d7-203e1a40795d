### EKF (Extended Kalman Filter) 配置文件 ###
# 用于机器人状态估计和传感器融合的扩展卡尔曼滤波器配置
# 该配置融合里程计和IMU数据，提供更准确的机器人位姿估计

ekf_filter_node:
    ros__parameters:
        # ========== 基本参数设置 ==========
        frequency: 20.0  # EKF滤波器运行频率(Hz)
                        # 建议设置为传感器数据中最高频率的1-2倍

        two_d_mode: true  # 启用2D模式，忽略Z轴位置、roll和pitch角度
                         # 对于地面移动机器人，通常设置为true

        publish_acceleration: false  # 发布加速度信息到/odometry/filtered话题
                                   # 如果下游节点需要加速度数据则设置为true

        publish_tf: true 

        # ========== 坐标系定义 ==========
        map_frame: map              # 地图坐标系名称，全局固定参考系
        odom_frame: odom           # 里程计坐标系名称，局部连续参考系
        base_link_frame: base_footprint  # 机器人本体坐标系名称
        world_frame: odom          # 世界坐标系，EKF输出的参考系
                                  # 设置为odom意味着输出odom->base_link变换

        # ========== 里程计传感器配置 ==========
        odom0: /diff_drive_controller/odom 
                        # 来自差分驱动控制器的原始里程计数据

        # 里程计数据使用配置 (15个布尔值对应15个状态变量)
        # 顺序: [x, y, z, roll, pitch, yaw, vx, vy, vz, vroll, vpitch, vyaw, ax, ay, az]
        # 方案B: 位置+姿态，禁用所有角速度
        odom0_config: [false,  false ,  false,   # 位置: 使用x,y，忽略z
                       false, false, false,    # 姿态: 忽略roll,pitch，使用yaw
                       true, true, false,    # 线速度: 使用vx,vy,忽略vz
                       false, false, true,   # 角速度: 使用yaw角速度
                       false, false, false]   # 加速度: 全部忽略
        odom0_relative: true
        odom0_queue_size: 10
        odom0_differential: true  # 是否将里程计数据作为差分数据处理
                                  # false表示使用绝对位置数据

        # ========== IMU传感器配置 ==========
        imu0: /imu_sensor_broadcaster/imu  # IMU数据源话题，来自HWT901B-TTL IMU设备

        # IMU数据使用配置 (15个布尔值对应15个状态变量)
        # 顺序: [x, y, z, roll, pitch, yaw, vx, vy, vz, vroll, vpitch, vyaw, ax, ay, az]
        # 方案B: 仅使用IMU的yaw姿态，禁用所有角速度
        imu0_config: [false, false, false,  # 位置: IMU不提供位置信息
                      false, false, false,   # 姿态: 仅使用yaw角度，提供方向参考
                      false, false, false,  # 线速度: IMU不直接提供线速度
                      false, false, true,  # 角速度: 完全禁用所有角速度
                      false, false, false]  # 加速度: 完全禁用

        imu0_differential: false  # IMU数据不作为差分数据处理
        imu0_relative: false      # IMU数据不作为相对数据处理
        imu0_queue_size: 5        # IMU数据队列大小
        imu0_remove_gravitational_acceleration: true  # 移除重力加速度影响
                                                     # 对于移动机器人通常设置为true

        # ========== 过程噪声协方差矩阵 ==========
        # 定义系统模型预测的不确定性，15x15对角矩阵
        # 对角线元素对应: [x, y, z, roll, pitch, yaw, vx, vy, vz, vroll, vpitch, vyaw, ax, ay, az]
        # 较大的值表示对该状态变量的预测不确定性更高
        # 调优指南:
        # - 如果机器人运动平滑但EKF输出抖动，减小相应值
        # - 如果EKF响应传感器数据太慢，增大相应值
        # - 位置相关(x,y,yaw)值通常设置为0.01-0.1
        # - 速度相关值通常比位置值小一个数量级
        process_noise_covariance: [0.01, 0.0,  0.0,  0.0,  0.0,  0.0,  0.0,   0.0,   0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,   # x位置噪声
                                   0.0,  0.01, 0.0,  0.0,  0.0,  0.0,  0.0,   0.0,   0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,   # y位置噪声
                                   0.0,  0.0,  0.02, 0.0,  0.0,  0.0,  0.0,   0.0,   0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,   # z位置噪声(2D模式下忽略)
                                   0.0,  0.0,  0.0,  0.01, 0.0,  0.0,  0.0,   0.0,   0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,   # roll角噪声(2D模式下忽略)
                                   0.0,  0.0,  0.0,  0.0,  0.01, 0.0,  0.0,   0.0,   0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,   # pitch角噪声(2D模式下忽略)
                                   0.0,  0.0,  0.0,  0.0,  0.0,  0.05, 0.0,   0.0,   0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,   # yaw角噪声
                                   0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.01, 0.0,   0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,   # x方向线速度噪声
                                   0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,   0.01, 0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,   # y方向线速度噪声
                                   0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,   0.0,   0.02, 0.0,  0.0,  0.0,  0.0,  0.0,  0.0,   # z方向线速度噪声(2D模式下忽略)
                                   0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,   0.0,   0.0,  0.005, 0.0,  0.0,  0.0,  0.0,  0.0,  # roll角速度噪声
                                   0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,   0.0,   0.0,  0.0,  0.005, 0.0,  0.0,  0.0,  0.0,  # pitch角速度噪声
                                   0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,   0.0,   0.0,  0.0,  0.0,  0.01, 0.0,  0.0,  0.0, # yaw角速度噪声(减小，减少预测)
                                   0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,   0.0,   0.0,  0.0,  0.0,  0.0,  0.005, 0.0,  0.0,  # x方向加速度噪声
                                   0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,   0.0,   0.0,  0.0,  0.0,  0.0,  0.0,  0.005, 0.0,  # y方向加速度噪声
                                   0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,   0.0,   0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.01] # z方向加速度噪声

        # ========== 初始估计误差协方差矩阵 ==========
        # 定义系统启动时对初始状态估计的不确定性，15x15对角矩阵
        # 对角线元素对应: [x, y, z, roll, pitch, yaw, vx, vy, vz, vroll, vpitch, vyaw, ax, ay, az]
        # 较小的值(如1e-9)表示对初始状态非常确信
        # 较大的值表示对初始状态不确定，EKF需要更多时间收敛
        # 调优指南:
        # - 如果机器人总是从已知位置启动，使用小值(1e-9)
        # - 如果启动位置不确定，增大位置相关的值(如0.1-1.0)
        # - 速度和加速度通常可以假设为0，使用小值
        initial_estimate_covariance: [1e-9, 0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,   0.0,   0.0,   0.0,  0.0,  0.0,   # x位置初始不确定性
                                      0.0,  1e-9, 0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,   0.0,   0.0,   0.0,  0.0,  0.0,   # y位置初始不确定性
                                      0.0,  0.0,  1e-9, 0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,   0.0,   0.0,   0.0,  0.0,  0.0,   # z位置初始不确定性
                                      0.0,  0.0,  0.0,  1e-9, 0.0,  0.0,  0.0,  0.0,  0.0,  0.0,   0.0,   0.0,   0.0,  0.0,  0.0,   # roll角初始不确定性
                                      0.0,  0.0,  0.0,  0.0,  1e-9, 0.0,  0.0,  0.0,  0.0,  0.0,   0.0,   0.0,   0.0,  0.0,  0.0,   # pitch角初始不确定性
                                      0.0,  0.0,  0.0,  0.0,  0.0,  1e-7, 0.0,  0.0,  0.0,  0.0,   0.0,   0.0,   0.0,  0.0,  0.0,   # yaw角初始不确定性
                                      0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  1e-9, 0.0,  0.0,  0.0,   0.0,   0.0,   0.0,  0.0,  0.0,   # x线速度初始不确定性
                                      0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  1e-9, 0.0,  0.0,   0.0,   0.0,   0.0,  0.0,  0.0,   # y线速度初始不确定性
                                      0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  1e-9, 0.0,   0.0,   0.0,   0.0,  0.0,  0.0,   # z线速度初始不确定性
                                      0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  1e-9,  0.0,   0.0,   0.0,  0.0,  0.0,   # roll角速度初始不确定性
                                      0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,   1e-9,  0.0,   0.0,  0.0,  0.0,   # pitch角速度初始不确定性
                                      0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,   0.0,   1e-9,  0.0,  0.0,  0.0,   # yaw角速度初始不确定性
                                      0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,   0.0,   0.0,   1e-9, 0.0,  0.0,   # x加速度初始不确定性
                                      0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,   0.0,   0.0,   0.0,  1e-9, 0.0,   # y加速度初始不确定性
                                      0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,   0.0,   0.0,   0.0,  0.0,  1e-9]  # z加速度初始不确定性

# ========== EKF配置总结 ==========
# 当前配置适用于:
# 1. 差分驱动地面机器人(2D运动)
# 2. 融合里程计(位置、速度)和IMU(角速度)数据
# 3. 输出频率50Hz，匹配里程计频率
# 4. 当前TF架构: diff_drive_controller发布odom->base_footprint
#    EKF接收原始里程计数据，输出融合后的/odometry/filtered话题
#    Cartographer使用融合后的里程计数据，发布map->odom变换
#
# TF变换链: map -> odom -> base_footprint
#           ↑       ↑         ↑
#      Cartographer EKF   diff_drive
#
# 调优建议:
# 1. 如果输出抖动: 减小process_noise_covariance对应值
# 2. 如果响应慢: 增大process_noise_covariance对应值
# 3. 如果启动收敛慢: 增大initial_estimate_covariance对应值
# 4. 根据实际传感器性能调整各传感器的config配置