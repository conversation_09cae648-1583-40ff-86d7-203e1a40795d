#!/usr/bin/env python3

"""
RobotCar路径跟随控制节点
功能：基于纯追踪算法的固定路径跟随控制
作者：RobotCar团队
"""

import rclpy
from rclpy.node import Node
from rclpy.action import ActionClient
from geometry_msgs.msg import PoseStamped, PoseWithCovarianceStamped
from nav_msgs.msg import Path
from nav2_msgs.action import NavigateToPose, FollowWaypoints
from std_msgs.msg import Bool
import yaml
import os
import math
import time
from typing import List, Tuple


class PathFollowingController(Node):
    """路径跟随控制器节点"""
    
    def __init__(self):
        super().__init__('path_following_controller')
        
        # 声明参数
        self.declare_parameter('path_file', '')
        self.declare_parameter('loop_mode', True)
        self.declare_parameter('follow_speed', 0.4)
        self.declare_parameter('goal_tolerance', 0.15)
        self.declare_parameter('path_resolution', 0.1)
        self.declare_parameter('use_sim_time', False)
        
        # 获取参数
        self.path_file = self.get_parameter('path_file').get_parameter_value().string_value
        self.loop_mode = self.get_parameter('loop_mode').get_parameter_value().bool_value
        self.follow_speed = self.get_parameter('follow_speed').get_parameter_value().double_value
        self.goal_tolerance = self.get_parameter('goal_tolerance').get_parameter_value().double_value
        self.path_resolution = self.get_parameter('path_resolution').get_parameter_value().double_value
        
        # 状态变量
        self.current_path: List[PoseStamped] = []
        self.current_goal_index = 0
        self.is_following = False
        self.robot_pose = None
        
        # 创建Action客户端
        self.nav_to_pose_client = ActionClient(self, NavigateToPose, 'navigate_to_pose')
        self.follow_waypoints_client = ActionClient(self, FollowWaypoints, 'follow_waypoints')
        
        # 创建发布者
        self.path_pub = self.create_publisher(Path, 'planned_path', 10)
        self.status_pub = self.create_publisher(Bool, 'path_following_status', 10)
        
        # 创建订阅者
        self.pose_sub = self.create_subscription(
            PoseWithCovarianceStamped,
            'amcl_pose',
            self.pose_callback,
            10
        )
        
        # 创建定时器
        self.control_timer = self.create_timer(0.1, self.control_loop)  # 10Hz控制循环
        
        self.get_logger().info('路径跟随控制器已启动')
        
        # 如果提供了路径文件，加载并开始跟随
        if self.path_file:
            self.load_and_start_following()
    
    def pose_callback(self, msg: PoseWithCovarianceStamped):
        """机器人位姿回调"""
        self.robot_pose = msg.pose.pose
    
    def load_path_from_file(self, file_path: str) -> List[PoseStamped]:
        """从文件加载路径"""
        try:
            if not os.path.exists(file_path):
                self.get_logger().error(f'路径文件不存在: {file_path}')
                return []
            
            with open(file_path, 'r') as f:
                path_data = yaml.safe_load(f)
            
            path = []
            if 'waypoints' in path_data:
                for wp in path_data['waypoints']:
                    pose = PoseStamped()
                    pose.header.frame_id = 'map'
                    pose.header.stamp = self.get_clock().now().to_msg()
                    pose.pose.position.x = float(wp['x'])
                    pose.pose.position.y = float(wp['y'])
                    pose.pose.position.z = 0.0
                    
                    # 处理方向
                    if 'yaw' in wp:
                        yaw = float(wp['yaw'])
                        pose.pose.orientation.z = math.sin(yaw / 2.0)
                        pose.pose.orientation.w = math.cos(yaw / 2.0)
                    else:
                        pose.pose.orientation.w = 1.0
                    
                    path.append(pose)
            
            self.get_logger().info(f'成功加载路径，包含 {len(path)} 个路径点')
            return path
            
        except Exception as e:
            self.get_logger().error(f'加载路径文件失败: {str(e)}')
            return []
    
    def interpolate_path(self, path: List[PoseStamped]) -> List[PoseStamped]:
        """对路径进行插值，增加路径点密度"""
        if len(path) < 2:
            return path
        
        interpolated_path = []
        
        for i in range(len(path) - 1):
            current = path[i]
            next_point = path[i + 1]
            
            # 添加当前点
            interpolated_path.append(current)
            
            # 计算两点间距离
            dx = next_point.pose.position.x - current.pose.position.x
            dy = next_point.pose.position.y - current.pose.position.y
            distance = math.sqrt(dx*dx + dy*dy)
            
            # 如果距离大于分辨率，进行插值
            if distance > self.path_resolution:
                num_points = int(distance / self.path_resolution)
                for j in range(1, num_points):
                    ratio = j / num_points
                    
                    interpolated_pose = PoseStamped()
                    interpolated_pose.header = current.header
                    interpolated_pose.pose.position.x = current.pose.position.x + dx * ratio
                    interpolated_pose.pose.position.y = current.pose.position.y + dy * ratio
                    interpolated_pose.pose.position.z = 0.0
                    
                    # 插值方向
                    interpolated_pose.pose.orientation = current.pose.orientation
                    
                    interpolated_path.append(interpolated_pose)
        
        # 添加最后一个点
        interpolated_path.append(path[-1])
        
        return interpolated_path
    
    def publish_path(self, path: List[PoseStamped]):
        """发布路径用于可视化"""
        path_msg = Path()
        path_msg.header.frame_id = 'map'
        path_msg.header.stamp = self.get_clock().now().to_msg()
        path_msg.poses = path
        self.path_pub.publish(path_msg)
    
    def load_and_start_following(self):
        """加载路径并开始跟随"""
        if not self.path_file:
            self.get_logger().warn('未指定路径文件')
            return
        
        # 加载路径
        raw_path = self.load_path_from_file(self.path_file)
        if not raw_path:
            return
        
        # 插值路径
        self.current_path = self.interpolate_path(raw_path)
        
        # 发布路径用于可视化
        self.publish_path(self.current_path)
        
        # 开始跟随
        self.start_path_following()
    
    def start_path_following(self):
        """开始路径跟随"""
        if not self.current_path:
            self.get_logger().warn('没有可跟随的路径')
            return
        
        self.get_logger().info('开始路径跟随')
        self.is_following = True
        self.current_goal_index = 0
        
        # 发布状态
        status_msg = Bool()
        status_msg.data = True
        self.status_pub.publish(status_msg)
        
        # 使用FollowWaypoints动作进行路径跟随
        self.send_waypoints_goal()
    
    def send_waypoints_goal(self):
        """发送路径点目标"""
        if not self.follow_waypoints_client.wait_for_server(timeout_sec=5.0):
            self.get_logger().error('FollowWaypoints动作服务器不可用')
            return
        
        goal_msg = FollowWaypoints.Goal()
        goal_msg.poses = self.current_path
        
        self.get_logger().info(f'发送 {len(self.current_path)} 个路径点进行跟随')
        
        # 发送目标
        future = self.follow_waypoints_client.send_goal_async(
            goal_msg,
            feedback_callback=self.waypoints_feedback_callback
        )
        future.add_done_callback(self.waypoints_goal_response_callback)
    
    def waypoints_goal_response_callback(self, future):
        """路径点目标响应回调"""
        goal_handle = future.result()
        if not goal_handle.accepted:
            self.get_logger().error('路径跟随目标被拒绝')
            return
        
        self.get_logger().info('路径跟随目标已接受')
        result_future = goal_handle.get_result_async()
        result_future.add_done_callback(self.waypoints_result_callback)
    
    def waypoints_feedback_callback(self, feedback_msg):
        """路径点跟随反馈回调"""
        feedback = feedback_msg.feedback
        self.get_logger().info(f'当前跟随路径点: {feedback.current_waypoint + 1}/{len(self.current_path)}')
    
    def waypoints_result_callback(self, future):
        """路径点跟随结果回调"""
        result = future.result().result
        
        if result.missed_waypoints:
            self.get_logger().warn(f'错过了 {len(result.missed_waypoints)} 个路径点')
        
        self.get_logger().info('路径跟随完成')
        
        # 如果启用循环模式，重新开始跟随
        if self.loop_mode:
            self.get_logger().info('循环模式已启用，重新开始路径跟随')
            time.sleep(1.0)  # 短暂延迟
            self.send_waypoints_goal()
        else:
            self.is_following = False
            status_msg = Bool()
            status_msg.data = False
            self.status_pub.publish(status_msg)
    
    def control_loop(self):
        """控制循环"""
        # 定期发布路径用于可视化
        if self.current_path:
            self.publish_path(self.current_path)
    
    def stop_following(self):
        """停止路径跟随"""
        if self.is_following:
            self.get_logger().info('停止路径跟随')
            self.follow_waypoints_client.cancel_all_goals()
            self.is_following = False
            
            status_msg = Bool()
            status_msg.data = False
            self.status_pub.publish(status_msg)


def main(args=None):
    rclpy.init(args=args)
    
    try:
        node = PathFollowingController()
        rclpy.spin(node)
    except KeyboardInterrupt:
        pass
    finally:
        if 'node' in locals():
            node.stop_following()
            node.destroy_node()
        rclpy.shutdown()


if __name__ == '__main__':
    main()
