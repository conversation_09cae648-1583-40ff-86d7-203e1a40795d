#!/usr/bin/env python3
"""
路网导航Web界面
提供简单的网页界面用于选择路点和监控导航状态
"""

import rclpy
from rclpy.node import Node
from std_msgs.msg import String
from robotcar_laser_fusion.srv import NavigateToWaypoint
import yaml
import json
from http.server import HTTPServer, BaseHTTPRequestHandler
import threading
import urllib.parse
import os

class WaypointWebInterface(Node):
    def __init__(self):
        super().__init__('waypoint_web_interface')
        
        # 声明参数
        self.declare_parameter('waypoints_file', '')
        self.declare_parameter('web_port', 8080)
        self.declare_parameter('namespace', 'chassis')
        
        # 获取参数
        self.waypoints_file = self.get_parameter('waypoints_file').as_string()
        self.web_port = self.get_parameter('web_port').as_int()
        self.namespace = self.get_parameter('namespace').as_string()
        
        # 加载路点配置
        self.waypoints = {}
        if self.waypoints_file:
            self.load_waypoints()
        
        # 创建服务客户端
        self.nav_client = self.create_client(
            NavigateToWaypoint, 
            f'/{self.namespace}/navigate_to_waypoint'
        )
        
        # 创建发布者
        self.waypoint_pub = self.create_publisher(
            String, 
            f'/{self.namespace}/selected_waypoint', 
            10
        )
        
        # 订阅导航状态
        self.status_sub = self.create_subscription(
            String,
            f'/{self.namespace}/navigation_status',
            self.status_callback,
            10
        )
        
        self.current_status = "就绪"
        
        # 启动Web服务器
        self.start_web_server()
        
        self.get_logger().info(f'路网导航Web界面已启动，端口: {self.web_port}')
    
    def load_waypoints(self):
        """加载路点配置"""
        try:
            with open(self.waypoints_file, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
                for wp in config.get('waypoints', []):
                    self.waypoints[wp['name']] = {
                        'description': wp.get('description', wp['name']),
                        'position': wp['position'],
                        'connections': wp.get('connections', [])
                    }
            self.get_logger().info(f'已加载 {len(self.waypoints)} 个路点')
        except Exception as e:
            self.get_logger().error(f'加载路点配置失败: {e}')
    
    def status_callback(self, msg):
        """导航状态回调"""
        self.current_status = msg.data
    
    def navigate_to_waypoint(self, waypoint_name):
        """导航到指定路点"""
        if waypoint_name not in self.waypoints:
            return False, "路点不存在"
        
        # 发布路点选择
        msg = String()
        msg.data = waypoint_name
        self.waypoint_pub.publish(msg)
        
        return True, "导航任务已发送"
    
    def start_web_server(self):
        """启动Web服务器"""
        server_address = ('', self.web_port)
        
        # 创建请求处理器类
        class RequestHandler(BaseHTTPRequestHandler):
            def __init__(self, waypoint_interface, *args, **kwargs):
                self.waypoint_interface = waypoint_interface
                super().__init__(*args, **kwargs)
        
        # 绑定waypoint_interface到处理器
        handler = lambda *args, **kwargs: WaypointRequestHandler(self, *args, **kwargs)
        
        self.httpd = HTTPServer(server_address, handler)
        
        # 在单独线程中运行服务器
        self.server_thread = threading.Thread(target=self.httpd.serve_forever)
        self.server_thread.daemon = True
        self.server_thread.start()

class WaypointRequestHandler(BaseHTTPRequestHandler):
    def __init__(self, waypoint_interface, *args, **kwargs):
        self.waypoint_interface = waypoint_interface
        super().__init__(*args, **kwargs)
    
    def do_GET(self):
        """处理GET请求"""
        if self.path == '/':
            self.serve_main_page()
        elif self.path == '/api/waypoints':
            self.serve_waypoints_api()
        elif self.path == '/api/status':
            self.serve_status_api()
        else:
            self.send_error(404)
    
    def do_POST(self):
        """处理POST请求"""
        if self.path == '/api/navigate':
            self.handle_navigate_request()
        else:
            self.send_error(404)
    
    def serve_main_page(self):
        """提供主页面"""
        html = """
        <!DOCTYPE html>
        <html>
        <head>
            <title>机器人路网导航</title>
            <meta charset="utf-8">
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                .waypoint-grid { display: grid; grid-template-columns: repeat(auto-fill, minmax(200px, 1fr)); gap: 10px; margin: 20px 0; }
                .waypoint-btn { padding: 15px; border: 1px solid #ccc; border-radius: 5px; cursor: pointer; text-align: center; background: #f9f9f9; }
                .waypoint-btn:hover { background: #e9e9e9; }
                .status { padding: 10px; margin: 10px 0; border-radius: 5px; background: #e7f3ff; }
                .title { color: #333; }
            </style>
        </head>
        <body>
            <h1 class="title">🤖 机器人路网导航控制面板</h1>
            
            <div class="status">
                <strong>当前状态:</strong> <span id="status">加载中...</span>
            </div>
            
            <h2>选择目标路点:</h2>
            <div class="waypoint-grid" id="waypoints">
                加载中...
            </div>
            
            <script>
                function loadWaypoints() {
                    fetch('/api/waypoints')
                        .then(response => response.json())
                        .then(data => {
                            const container = document.getElementById('waypoints');
                            container.innerHTML = '';
                            
                            for (const [name, info] of Object.entries(data)) {
                                const btn = document.createElement('div');
                                btn.className = 'waypoint-btn';
                                btn.innerHTML = `<strong>${name}</strong><br><small>${info.description}</small>`;
                                btn.onclick = () => navigateToWaypoint(name);
                                container.appendChild(btn);
                            }
                        });
                }
                
                function navigateToWaypoint(name) {
                    fetch('/api/navigate', {
                        method: 'POST',
                        headers: {'Content-Type': 'application/json'},
                        body: JSON.stringify({waypoint: name})
                    })
                    .then(response => response.json())
                    .then(data => {
                        alert(data.message);
                        updateStatus();
                    });
                }
                
                function updateStatus() {
                    fetch('/api/status')
                        .then(response => response.json())
                        .then(data => {
                            document.getElementById('status').textContent = data.status;
                        });
                }
                
                // 初始化
                loadWaypoints();
                updateStatus();
                
                // 定期更新状态
                setInterval(updateStatus, 2000);
            </script>
        </body>
        </html>
        """
        
        self.send_response(200)
        self.send_header('Content-type', 'text/html; charset=utf-8')
        self.end_headers()
        self.wfile.write(html.encode('utf-8'))
    
    def serve_waypoints_api(self):
        """提供路点API"""
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.end_headers()
        
        response = json.dumps(self.waypoint_interface.waypoints, ensure_ascii=False)
        self.wfile.write(response.encode('utf-8'))
    
    def serve_status_api(self):
        """提供状态API"""
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.end_headers()
        
        response = json.dumps({
            'status': self.waypoint_interface.current_status
        }, ensure_ascii=False)
        self.wfile.write(response.encode('utf-8'))
    
    def handle_navigate_request(self):
        """处理导航请求"""
        content_length = int(self.headers['Content-Length'])
        post_data = self.rfile.read(content_length)
        
        try:
            data = json.loads(post_data.decode('utf-8'))
            waypoint_name = data['waypoint']
            
            success, message = self.waypoint_interface.navigate_to_waypoint(waypoint_name)
            
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            
            response = json.dumps({
                'success': success,
                'message': message
            }, ensure_ascii=False)
            self.wfile.write(response.encode('utf-8'))
            
        except Exception as e:
            self.send_error(400, str(e))

def main():
    rclpy.init()
    
    try:
        node = WaypointWebInterface()
        rclpy.spin(node)
    except KeyboardInterrupt:
        pass
    finally:
        rclpy.shutdown()

if __name__ == '__main__':
    main()
