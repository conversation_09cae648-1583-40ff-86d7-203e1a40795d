#!/bin/bash
# 路网导航系统快速启动脚本

echo "🤖 启动路网导航系统..."

# 检查参数
if [ $# -lt 2 ]; then
    echo "用法: $0 <地图文件> <路点配置文件> [可选参数]"
    echo "示例: $0 /path/to/map.pbstream /path/to/waypoints.yaml"
    echo ""
    echo "可选参数:"
    echo "  --rviz          启动RViz可视化"
    echo "  --no-web        不启动Web界面"
    echo "  --no-ekf        不启用EKF"
    echo "  --namespace=X   设置命名空间"
    exit 1
fi

MAP_FILE="$1"
WAYPOINTS_FILE="$2"
shift 2

# 默认参数
START_RVIZ="false"
START_WEB="true"
USE_EKF="true"
NAMESPACE="chassis"

# 解析可选参数
while [[ $# -gt 0 ]]; do
    case $1 in
        --rviz)
            START_RVIZ="true"
            shift
            ;;
        --no-web)
            START_WEB="false"
            shift
            ;;
        --no-ekf)
            USE_EKF="false"
            shift
            ;;
        --namespace=*)
            NAMESPACE="${1#*=}"
            shift
            ;;
        *)
            echo "未知参数: $1"
            exit 1
            ;;
    esac
done

# 检查文件是否存在
if [ ! -f "$MAP_FILE" ]; then
    echo "❌ 地图文件不存在: $MAP_FILE"
    exit 1
fi

if [ ! -f "$WAYPOINTS_FILE" ]; then
    echo "❌ 路点配置文件不存在: $WAYPOINTS_FILE"
    exit 1
fi

# 设置环境
source install/setup.bash

echo "📋 启动配置:"
echo "  地图文件: $MAP_FILE"
echo "  路点配置: $WAYPOINTS_FILE"
echo "  RViz可视化: $START_RVIZ"
echo "  Web界面: $START_WEB"
echo "  EKF融合: $USE_EKF"
echo "  命名空间: $NAMESPACE"
echo ""

# 启动系统
echo "🚀 启动路网导航系统..."
ros2 launch robotcar_laser_fusion fusion_07_waypoint_navigation.launch.py \
    map_file:="$MAP_FILE" \
    waypoints_file:="$WAYPOINTS_FILE" \
    start_rviz:="$START_RVIZ" \
    start_web_interface:="$START_WEB" \
    use_ekf:="$USE_EKF" \
    namespace:="$NAMESPACE"
