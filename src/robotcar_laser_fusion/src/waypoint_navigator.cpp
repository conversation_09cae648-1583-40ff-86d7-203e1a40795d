#include <rclcpp/rclcpp.hpp>
#include <geometry_msgs/msg/pose_stamped.hpp>
#include <nav2_msgs/action/navigate_to_pose.hpp>
#include <rclcpp_action/rclcpp_action.hpp>
#include <yaml-cpp/yaml.h>
#include <std_msgs/msg/string.hpp>
#include <std_srvs/srv/trigger.hpp>
#include "robotcar_laser_fusion/srv/navigate_to_waypoint.hpp"

class WaypointNavigator : public rclcpp::Node
{
public:
    using NavigateToPose = nav2_msgs::action::NavigateToPose;
    using GoalHandleNavigateToPose = rclcpp_action::ClientGoalHandle<NavigateToPose>;

    WaypointNavigator() : Node("waypoint_navigator")
    {
        // 声明参数
        this->declare_parameter("waypoints_file", "");
        this->declare_parameter("namespace", "chassis");
        
        // 获取参数
        waypoints_file_ = this->get_parameter("waypoints_file").as_string();
        namespace_ = this->get_parameter("namespace").as_string();
        
        // 加载路点配置
        if (!waypoints_file_.empty()) {
            loadWaypoints(waypoints_file_);
        }
        
        // 创建Nav2动作客户端
        nav_client_ = rclcpp_action::create_client<NavigateToPose>(
            this, "/" + namespace_ + "/navigate_to_pose");
        
        // 创建服务
        navigate_service_ = this->create_service<robotcar_laser_fusion::srv::NavigateToWaypoint>(
            "/" + namespace_ + "/navigate_to_waypoint",
            std::bind(&WaypointNavigator::navigateToWaypointCallback, this,
                     std::placeholders::_1, std::placeholders::_2));
        
        // 创建发布者
        status_pub_ = this->create_publisher<std_msgs::msg::String>(
            "/" + namespace_ + "/navigation_status", 10);
        
        // 创建订阅者 - 监听路点选择
        waypoint_sub_ = this->create_subscription<std_msgs::msg::String>(
            "/" + namespace_ + "/selected_waypoint", 10,
            std::bind(&WaypointNavigator::waypointCallback, this, std::placeholders::_1));
        
        RCLCPP_INFO(this->get_logger(), "路网导航管理器已启动");
        if (!waypoints_file_.empty()) {
            RCLCPP_INFO(this->get_logger(), "已加载 %zu 个路点", waypoints_.size());
        }
    }

private:
    struct Waypoint {
        std::string name;
        double x, y, z;
        double qx, qy, qz, qw;
        std::vector<std::string> connected_waypoints;
    };
    
    std::string waypoints_file_;
    std::string namespace_;
    std::map<std::string, Waypoint> waypoints_;
    
    rclcpp_action::Client<NavigateToPose>::SharedPtr nav_client_;
    rclcpp::Service<robotcar_laser_fusion::srv::NavigateToWaypoint>::SharedPtr navigate_service_;
    rclcpp::Publisher<std_msgs::msg::String>::SharedPtr status_pub_;
    rclcpp::Subscription<std_msgs::msg::String>::SharedPtr waypoint_sub_;
    
    void loadWaypoints(const std::string& filename)
    {
        try {
            YAML::Node config = YAML::LoadFile(filename);
            
            for (const auto& wp_node : config["waypoints"]) {
                Waypoint wp;
                wp.name = wp_node["name"].as<std::string>();
                wp.x = wp_node["position"]["x"].as<double>();
                wp.y = wp_node["position"]["y"].as<double>();
                wp.z = wp_node["position"]["z"].as<double>();
                wp.qx = wp_node["orientation"]["x"].as<double>();
                wp.qy = wp_node["orientation"]["y"].as<double>();
                wp.qz = wp_node["orientation"]["z"].as<double>();
                wp.qw = wp_node["orientation"]["w"].as<double>();
                
                if (wp_node["connections"]) {
                    for (const auto& conn : wp_node["connections"]) {
                        wp.connected_waypoints.push_back(conn.as<std::string>());
                    }
                }
                
                waypoints_[wp.name] = wp;
            }
            
            RCLCPP_INFO(this->get_logger(), "成功加载路点配置文件: %s", filename.c_str());
        } catch (const std::exception& e) {
            RCLCPP_ERROR(this->get_logger(), "加载路点配置文件失败: %s", e.what());
        }
    }
    
    void waypointCallback(const std_msgs::msg::String::SharedPtr msg)
    {
        std::string waypoint_name = msg->data;
        RCLCPP_INFO(this->get_logger(), "收到路点选择: %s", waypoint_name.c_str());
        
        if (waypoints_.find(waypoint_name) != waypoints_.end()) {
            navigateToWaypoint(waypoint_name);
        } else {
            RCLCPP_WARN(this->get_logger(), "未找到路点: %s", waypoint_name.c_str());
        }
    }
    
    void navigateToWaypointCallback(
        const std::shared_ptr<robotcar_laser_fusion::srv::NavigateToWaypoint::Request> request,
        std::shared_ptr<robotcar_laser_fusion::srv::NavigateToWaypoint::Response> response)
    {
        response->success = navigateToWaypoint(request->waypoint_name);
        if (response->success) {
            response->message = "导航任务已启动";
        } else {
            response->message = "导航任务启动失败";
        }
    }
    
    bool navigateToWaypoint(const std::string& waypoint_name)
    {
        auto it = waypoints_.find(waypoint_name);
        if (it == waypoints_.end()) {
            RCLCPP_ERROR(this->get_logger(), "路点不存在: %s", waypoint_name.c_str());
            return false;
        }
        
        const Waypoint& wp = it->second;
        
        // 等待Nav2服务可用
        if (!nav_client_->wait_for_action_server(std::chrono::seconds(5))) {
            RCLCPP_ERROR(this->get_logger(), "Nav2导航服务不可用");
            return false;
        }
        
        // 创建导航目标
        auto goal_msg = NavigateToPose::Goal();
        goal_msg.pose.header.frame_id = "map";
        goal_msg.pose.header.stamp = this->now();
        goal_msg.pose.pose.position.x = wp.x;
        goal_msg.pose.pose.position.y = wp.y;
        goal_msg.pose.pose.position.z = wp.z;
        goal_msg.pose.pose.orientation.x = wp.qx;
        goal_msg.pose.pose.orientation.y = wp.qy;
        goal_msg.pose.pose.orientation.z = wp.qz;
        goal_msg.pose.pose.orientation.w = wp.qw;
        
        // 发送导航目标
        auto send_goal_options = rclcpp_action::Client<NavigateToPose>::SendGoalOptions();
        send_goal_options.goal_response_callback =
            std::bind(&WaypointNavigator::goalResponseCallback, this, std::placeholders::_1);
        send_goal_options.feedback_callback =
            std::bind(&WaypointNavigator::feedbackCallback, this, std::placeholders::_1, std::placeholders::_2);
        send_goal_options.result_callback =
            std::bind(&WaypointNavigator::resultCallback, this, std::placeholders::_1);
        
        nav_client_->async_send_goal(goal_msg, send_goal_options);
        
        RCLCPP_INFO(this->get_logger(), "开始导航到路点: %s (%.2f, %.2f)", 
                   waypoint_name.c_str(), wp.x, wp.y);
        
        // 发布状态
        auto status_msg = std_msgs::msg::String();
        status_msg.data = "导航中: " + waypoint_name;
        status_pub_->publish(status_msg);
        
        return true;
    }
    
    void goalResponseCallback(const GoalHandleNavigateToPose::SharedPtr& goal_handle)
    {
        if (!goal_handle) {
            RCLCPP_ERROR(this->get_logger(), "导航目标被拒绝");
        } else {
            RCLCPP_INFO(this->get_logger(), "导航目标已接受");
        }
    }
    
    void feedbackCallback(
        GoalHandleNavigateToPose::SharedPtr,
        const std::shared_ptr<const NavigateToPose::Feedback> feedback)
    {
        // 可以在这里处理导航反馈
        (void)feedback; // 避免未使用变量警告
    }
    
    void resultCallback(const GoalHandleNavigateToPose::WrappedResult& result)
    {
        auto status_msg = std_msgs::msg::String();
        
        switch (result.code) {
            case rclcpp_action::ResultCode::SUCCEEDED:
                RCLCPP_INFO(this->get_logger(), "导航成功完成");
                status_msg.data = "导航完成";
                break;
            case rclcpp_action::ResultCode::ABORTED:
                RCLCPP_ERROR(this->get_logger(), "导航被中止");
                status_msg.data = "导航中止";
                break;
            case rclcpp_action::ResultCode::CANCELED:
                RCLCPP_WARN(this->get_logger(), "导航被取消");
                status_msg.data = "导航取消";
                break;
            default:
                RCLCPP_ERROR(this->get_logger(), "导航失败，未知错误");
                status_msg.data = "导航失败";
                break;
        }
        
        status_pub_->publish(status_msg);
    }
};

int main(int argc, char** argv)
{
    rclcpp::init(argc, argv);
    auto node = std::make_shared<WaypointNavigator>();
    rclcpp::spin(node);
    rclcpp::shutdown();
    return 0;
}
