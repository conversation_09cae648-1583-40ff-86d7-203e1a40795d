# 路网导航系统使用指南

## 概述

`fusion_07_waypoint_navigation.launch.py` 提供了基于预定义路径网络的固定路径导航功能。用户可以通过Web界面或ROS服务选择目标路点，机器人将自动沿着预设的路径网络进行导航。

## 系统特性

- 🗺️ **路网导航**: 基于预定义路点的网络导航
- 🌐 **Web界面**: 简单易用的网页控制面板
- 🎯 **点选导航**: 选择目标点即可开始导航
- 🔄 **状态监控**: 实时显示导航状态
- 🛡️ **安全保障**: 集成Nav2避障和路径规划

## 启动命令

### 基本启动
```bash
ros2 launch robotcar_laser_fusion fusion_07_waypoint_navigation.launch.py \
    map_file:=/path/to/your/map.pbstream \
    waypoints_file:=/path/to/waypoints.yaml
```

### 完整配置启动
```bash
ros2 launch robotcar_laser_fusion fusion_07_waypoint_navigation.launch.py \
    map_file:=/path/to/your/map.pbstream \
    waypoints_file:=/path/to/waypoints.yaml \
    start_rviz:=true \
    start_web_interface:=true \
    use_ekf:=true \
    namespace:=chassis
```

## 启动参数

| 参数 | 默认值 | 说明 |
|------|--------|------|
| `map_file` | '' | 地图文件路径 (.pbstream格式) |
| `waypoints_file` | '' | 路点配置文件路径 (.yaml格式) |
| `start_rviz` | false | 是否启动RViz可视化 |
| `start_web_interface` | true | 是否启动Web控制界面 |
| `use_ekf` | true | 是否启用EKF传感器融合 |
| `use_sim_time` | false | 是否使用仿真时间 |
| `namespace` | chassis | ROS2命名空间 |

## 路点配置文件

### 配置文件格式
路点配置文件使用YAML格式，定义机器人可以导航到的预设位置：

```yaml
waypoints:
  - name: "entrance"
    description: "入口区域"
    position:
      x: 0.0
      y: 0.0
      z: 0.0
    orientation:
      x: 0.0
      y: 0.0
      z: 0.0
      w: 1.0
    connections: ["corridor_1", "reception"]
    
  - name: "office_1"
    description: "办公室1"
    position:
      x: 3.0
      y: 3.0
      z: 0.0
    orientation:
      x: 0.0
      y: 0.0
      z: 1.0
      w: 0.0
    connections: ["corridor_1"]
```

### 创建路点配置
1. **复制示例文件**:
   ```bash
   cp src/robotcar_laser_fusion/config/waypoints_example.yaml my_waypoints.yaml
   ```

2. **编辑路点位置**: 根据您的环境修改路点坐标
3. **设置连接关系**: 定义路点之间的可达性

## Web界面使用

### 访问Web界面
启动系统后，在浏览器中访问：
```
http://机器人IP:8080
```

### 界面功能
- **路点选择**: 点击路点按钮开始导航
- **状态显示**: 实时显示当前导航状态
- **响应式设计**: 支持手机和平板访问

## ROS接口

### 话题
- `/chassis/selected_waypoint` (std_msgs/String): 发布选中的路点名称
- `/chassis/navigation_status` (std_msgs/String): 导航状态信息

### 服务
- `/chassis/navigate_to_waypoint` (NavigateToWaypoint): 导航到指定路点

### 服务调用示例
```bash
# 导航到指定路点
ros2 service call /chassis/navigate_to_waypoint \
    robotcar_laser_fusion/srv/NavigateToWaypoint \
    "{waypoint_name: 'office_1'}"
```

## 系统架构

### 启动顺序
1. **0s**: 基础硬件和双雷达融合
2. **0s**: EKF传感器融合 (可选)
3. **5s**: Cartographer定位
4. **6s**: 占用栅格节点
5. **7s**: Nav2导航系统
6. **8s**: 路网导航管理器
7. **9s**: Web界面 (可选)
8. **10s**: RViz可视化 (可选)

### 核心组件
- **waypoint_navigator**: 路网导航管理器
- **waypoint_web_interface**: Web控制界面
- **Nav2**: 路径规划和避障
- **Cartographer**: 定位和地图

## 使用场景

### 1. 仓库巡检
```yaml
waypoints:
  - name: "warehouse_entrance"
  - name: "aisle_1_start"
  - name: "aisle_1_end"
  - name: "aisle_2_start"
  - name: "aisle_2_end"
  - name: "loading_dock"
```

### 2. 办公室服务
```yaml
waypoints:
  - name: "reception"
  - name: "meeting_room_a"
  - name: "meeting_room_b"
  - name: "office_area"
  - name: "kitchen"
  - name: "charging_station"
```

### 3. 医院配送
```yaml
waypoints:
  - name: "pharmacy"
  - name: "ward_1"
  - name: "ward_2"
  - name: "nurse_station"
  - name: "elevator"
```

## 故障排除

### 1. Web界面无法访问
```bash
# 检查Web服务是否启动
ros2 node list | grep waypoint_web_interface

# 检查端口是否被占用
netstat -tulpn | grep 8080
```

### 2. 导航失败
```bash
# 检查路点配置
ros2 service call /chassis/navigate_to_waypoint \
    robotcar_laser_fusion/srv/NavigateToWaypoint \
    "{waypoint_name: 'test_point'}"

# 检查Nav2状态
ros2 topic echo /chassis/navigation_status
```

### 3. 路点加载失败
```bash
# 验证YAML文件格式
python3 -c "import yaml; yaml.safe_load(open('waypoints.yaml'))"

# 检查文件路径
ls -la /path/to/waypoints.yaml
```

## 高级配置

### 自定义Web端口
```bash
ros2 launch robotcar_laser_fusion fusion_07_waypoint_navigation.launch.py \
    waypoints_file:=waypoints.yaml \
    web_port:=9090
```

### 多机器人支持
```bash
# 机器人1
ros2 launch robotcar_laser_fusion fusion_07_waypoint_navigation.launch.py \
    namespace:=robot1 \
    waypoints_file:=robot1_waypoints.yaml

# 机器人2  
ros2 launch robotcar_laser_fusion fusion_07_waypoint_navigation.launch.py \
    namespace:=robot2 \
    waypoints_file:=robot2_waypoints.yaml
```

## 注意事项

1. **地图要求**: 必须使用Cartographer生成的.pbstream格式地图
2. **路点精度**: 路点坐标应在地图坐标系中准确定位
3. **网络连接**: Web界面需要网络连接
4. **安全距离**: 路点位置应避开障碍物
5. **系统资源**: 确保系统有足够的计算资源运行所有组件
