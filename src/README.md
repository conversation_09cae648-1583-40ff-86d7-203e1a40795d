# RobotCar 智能导航机器人系统

![License](https://img.shields.io/badge/license-Apache%202.0-blue)
![ROS2](https://img.shields.io/badge/ROS2-Humble-green)
![Platform](https://img.shields.io/badge/platform-Ubuntu%2022.04-orange)
![Build Status](https://img.shields.io/badge/build-passing-brightgreen)

一个基于ROS2的智能移动机器人系统，集成双雷达融合SLAM、自抗扰控制器(ADRC)、Qt图形界面和多模式导航功能。

## 📋 目录

- [系统架构](#系统架构)
- [功能模块](#功能模块)
- [系统要求](#系统要求)
- [快速启动](#快速启动)
- [Docker部署](#docker部署)
- [详细使用说明](#详细使用说明)
- [开发指南](#开发指南)
- [故障排除](#故障排除)
- [贡献指南](#贡献指南)
- [许可证](#许可证)
- [引用](#引用)

## 🏗️ 系统架构

```mermaid
graph TB
    subgraph "硬件层"
        A[双ORadar MS500激光雷达]
        B[IMU传感器]
        C[差分驱动底盘]
        D[机械臂]
    end

    subgraph "驱动层"
        E[robotcar_base<br/>硬件接口]
        F[oradar_ros<br/>雷达驱动]
        G[robot_ctrl<br/>底盘控制]
        H[robot_arm<br/>机械臂控制]
    end

    subgraph "感知层"
        I[dual_laser_merger<br/>雷达融合]
        J[cartographer<br/>SLAM建图]
        K[EKF传感器融合]
    end

    subgraph "决策层"
        L[Nav2导航栈]
        M[adrc_controller<br/>自抗扰控制]
        N[pb_omni_pid_pursuit_controller<br/>PB控制器]
        O[docking_server<br/>对接服务]
    end

    subgraph "应用层"
        P[qt_gui_ros2<br/>图形界面]
        Q[robotcar_gui<br/>机器人界面]
        R[apriltag_ros<br/>视觉标签]
    end

    A --> F
    B --> E
    C --> G
    D --> H
    F --> I
    E --> K
    I --> J
    J --> L
    K --> L
    L --> M
    L --> N
    M --> G
    N --> G
    L --> O
    P --> L
    Q --> L
    R --> L
```

## 🔧 功能模块

### 核心功能模块

| 模块 | 功能描述 | 主要特性 |
|------|----------|----------|
| **robotcar_laser_fusion** | 双雷达融合导航系统 | • 360°激光扫描融合<br/>• Cartographer SLAM<br/>• 多模式导航 |
| **adrc_controller** | 自抗扰控制器 | • 高精度轨迹跟踪<br/>• 抗干扰能力强<br/>• 实时控制 |
| **qt_gui_ros2** | Qt图形用户界面 | • 集成RViz可视化<br/>• 实时控制面板<br/>• 状态监控 |
| **robotcar_base** | 机器人基础系统 | • 硬件抽象层<br/>• ROS2 Control集成<br/>• URDF模型 |

### 支持模块

| 模块 | 功能描述 |
|------|----------|
| **dual_laser_merger** | 双雷达数据融合 |
| **oradar_ros** | ORadar激光雷达驱动 |
| **cartographer_code** | Google Cartographer SLAM |
| **pb_omni_pid_pursuit_controller** | PB纯追踪控制器 |
| **docking_server** | 自动对接服务 |
| **robot_arm** | 机械臂控制 |
| **robot_ctrl** | 底盘运动控制 |
| **robotcar_gui** | 机器人专用界面 |
| **robotcar_nav** | 导航功能包 |
| **apriltag_ros** | AprilTag视觉标签检测 |

## 💻 系统要求

### Requirements

#### 操作系统
- **Ubuntu 22.04 LTS** (推荐)
- **Ubuntu 20.04 LTS** (兼容)

#### ROS2版本
- **ROS2 Humble** (主要支持)
- **ROS2 Galactic** (兼容)

#### 硬件要求
- **CPU**: Intel i5-8400 或 AMD Ryzen 5 3600 以上
- **内存**: 8GB RAM (推荐16GB)
- **存储**: 50GB 可用空间
- **GPU**: 支持OpenGL 3.3+ (可选，用于3D可视化)

#### 依赖库
```bash
# 核心依赖
ros-humble-desktop-full
ros-humble-navigation2
ros-humble-nav2-bringup
ros-humble-cartographer
ros-humble-cartographer-ros

# 控制相关
ros-humble-ros2-control
ros-humble-ros2-controllers
ros-humble-diff-drive-controller
ros-humble-joint-state-broadcaster

# 传感器相关
ros-humble-sensor-msgs
ros-humble-laser-geometry
ros-humble-pcl-ros

# Qt界面
qt5-default
qtbase5-dev
ros-humble-rviz2
ros-humble-rviz-common

# 其他工具
python3-colcon-common-extensions
python3-rosdep
python3-vcstool
```

## 🚀 快速启动

### 1. 环境准备

```bash
# 安装ROS2 Humble
sudo apt update
sudo apt install software-properties-common
sudo add-apt-repository universe
sudo apt update && sudo apt install curl -y
sudo curl -sSL https://raw.githubusercontent.com/ros/rosdistro/master/ros.asc | sudo apt-key add -
sudo sh -c 'echo "deb [arch=amd64,arm64] http://packages.ros.org/ros2/ubuntu $(lsb_release -cs) main" > /etc/apt/sources.list.d/ros2-latest.list'
sudo apt update
sudo apt install ros-humble-desktop-full

# 安装依赖
sudo apt install python3-colcon-common-extensions python3-rosdep python3-vcstool
sudo rosdep init
rosdep update
```

### 2. 克隆和构建

```bash
# 克隆仓库
git clone https://github.com/Sspacegray/test_ws.git
cd test_ws

# 安装依赖
rosdep install --from-paths src --ignore-src -r -y

# 构建工作空间
colcon build --symlink-install

# 加载环境
source install/setup.bash
```

### 3. 基础系统启动

```bash
# 启动机器人基础系统
ros2 launch robotcar_base robotcar.launch.py

# 新终端：启动双雷达融合系统
ros2 launch robotcar_laser_fusion fusion_01_bringup.launch.py
```

### 4. 完整导航系统

```bash
# 一键启动完整导航系统
ros2 launch robotcar_laser_fusion fusion_05_navigation.launch.py

# 启动Qt图形界面
ros2 launch qt_gui_ros2 robotcar_qt_gui.launch.py
```

### 5. SLAM建图模式

```bash
# 启动SLAM建图
ros2 launch robotcar_laser_fusion fusion_03_mapping.launch.py

# 保存地图
ros2 run nav2_map_server map_saver_cli -f ~/my_map
```

## 🐳 Docker部署

### Docker镜像构建

```dockerfile
# Dockerfile
FROM ros:humble-desktop-full

# 安装依赖
RUN apt-get update && apt-get install -y \
    python3-colcon-common-extensions \
    python3-rosdep \
    python3-vcstool \
    qt5-default \
    qtbase5-dev \
    ros-humble-navigation2 \
    ros-humble-cartographer \
    ros-humble-cartographer-ros \
    ros-humble-ros2-control \
    ros-humble-ros2-controllers \
    && rm -rf /var/lib/apt/lists/*

# 创建工作空间
WORKDIR /opt/robotcar_ws

# 复制源码
COPY src/ src/

# 安装ROS依赖
RUN rosdep update && \
    rosdep install --from-paths src --ignore-src -r -y

# 构建工作空间
RUN . /opt/ros/humble/setup.sh && \
    colcon build --symlink-install

# 设置入口点
COPY docker/entrypoint.sh /entrypoint.sh
RUN chmod +x /entrypoint.sh

ENTRYPOINT ["/entrypoint.sh"]
CMD ["bash"]
```

### 构建和运行

```bash
# 构建Docker镜像
docker build -t robotcar:latest .

# 运行容器（带GUI支持）
docker run -it --rm \
    --name robotcar \
    --network host \
    --privileged \
    -e DISPLAY=$DISPLAY \
    -v /tmp/.X11-unix:/tmp/.X11-unix:rw \
    -v /dev:/dev \
    robotcar:latest

# 在容器内启动系统
ros2 launch robotcar_laser_fusion fusion_05_navigation.launch.py
```

### Docker Compose

```yaml
# docker-compose.yml
version: '3.8'

services:
  robotcar:
    build: .
    image: robotcar:latest
    container_name: robotcar
    network_mode: host
    privileged: true
    environment:
      - DISPLAY=${DISPLAY}
      - ROS_DOMAIN_ID=0
    volumes:
      - /tmp/.X11-unix:/tmp/.X11-unix:rw
      - /dev:/dev
      - ./maps:/opt/robotcar_ws/maps
      - ./logs:/opt/robotcar_ws/logs
    command: >
      bash -c "
        source /opt/robotcar_ws/install/setup.bash &&
        ros2 launch robotcar_laser_fusion fusion_05_navigation.launch.py
      "

  gui:
    image: robotcar:latest
    container_name: robotcar-gui
    network_mode: host
    environment:
      - DISPLAY=${DISPLAY}
    volumes:
      - /tmp/.X11-unix:/tmp/.X11-unix:rw
    depends_on:
      - robotcar
    command: >
      bash -c "
        source /opt/robotcar_ws/install/setup.bash &&
        sleep 10 &&
        ros2 launch qt_gui_ros2 robotcar_qt_gui.launch.py
      "
```

```bash
# 使用Docker Compose启动
docker-compose up
```

## 📖 详细使用说明

### 启动模式说明

| 启动文件 | 功能描述 | 使用场景 |
|----------|----------|----------|
| `fusion_01_bringup.launch.py` | 基础双雷达融合 | 硬件测试、传感器验证 |
| `fusion_02_ekf.launch.py` | EKF传感器融合 | 多传感器数据融合 |
| `fusion_03_mapping.launch.py` | SLAM建图模式 | 环境地图构建 |
| `fusion_04_localization.launch.py` | 定位模式 | 已知地图定位 |
| `fusion_05_navigation.launch.py` | 完整导航系统 | 自主导航任务 |
| `fusion_06_multi_point_navigation.launch.py` | 多点导航 | 巡逻、配送任务 |
| `fusion_07_waypoint_navigation.launch.py` | 航点导航 | 精确路径跟踪 |
| `fusion_08_path_following.launch.py` | 路径跟随 | 预定义路径执行 |

### 控制器对比

| 控制器 | 优势 | 适用场景 | 性能特点 |
|--------|------|----------|----------|
| **ADRC控制器** | • 抗干扰能力强<br/>• 无需精确模型<br/>• 自适应性好 | • 复杂环境导航<br/>• 高精度跟踪<br/>• 动态障碍物 | • 响应快速<br/>• 超调小<br/>• 稳态精度高 |
| **PB控制器** | • 算法简单<br/>• 计算量小<br/>• 易于调参 | • 简单环境<br/>• 低速导航<br/>• 教学演示 | • 实现简单<br/>• 资源占用少 |

### 参数配置指南

#### ADRC控制器参数

```yaml
# adrc_controller配置
adrc_controller:
  # 控制参数
  max_linear_vel: 0.5      # 最大线速度 (m/s)
  max_angular_vel: 1.0     # 最大角速度 (rad/s)
  goal_tolerance: 0.1      # 目标容差 (m)

  # ADRC参数
  b0: 1.0                  # 控制增益
  kp: 100.0               # 比例增益
  kd: 20.0                # 微分增益
  beta01: 100.0           # ESO参数1
  beta02: 300.0           # ESO参数2
  beta03: 1000.0          # ESO参数3
```

#### 雷达融合参数

```yaml
# 双雷达融合配置
laser_merger:
  laser_1_topic: "/lidar1/scan"
  laser_2_topic: "/lidar2/scan"
  merged_topic: "/scan"
  target_frame: "base_link"

  # 融合参数
  tolerance: 0.02          # 时间同步容差 (s)
  range_min: 0.05         # 最小测距 (m)
  range_max: 20.0         # 最大测距 (m)
  angle_increment: 0.001   # 角度分辨率 (rad)
```

### 常用操作命令

```bash
# 系统状态检查
ros2 node list                          # 查看运行节点
ros2 topic list                         # 查看话题列表
ros2 service list                       # 查看服务列表

# 传感器数据检查
ros2 topic echo /scan --once            # 查看融合激光数据
ros2 topic echo /tracked_pose --once    # 查看机器人位置
ros2 topic echo /odom --once            # 查看里程计数据

# 控制命令
ros2 topic pub /cmd_vel geometry_msgs/msg/Twist \
  '{linear: {x: 0.2}, angular: {z: 0.0}}'  # 手动控制

# 导航命令
ros2 action send_goal /navigate_to_pose nav2_msgs/action/NavigateToPose \
  '{pose: {header: {frame_id: "map"}, pose: {position: {x: 2.0, y: 1.0}}}}'

# 地图保存
ros2 run nav2_map_server map_saver_cli -f my_map

# 参数调整
ros2 param set /adrc_controller max_linear_vel 0.3
ros2 param get /adrc_controller max_linear_vel
```

## 🛠️ 开发指南

### 代码结构

```
src/
├── robotcar_laser_fusion/     # 主要功能包
│   ├── launch/               # 启动文件
│   ├── config/               # 配置文件
│   ├── src/                  # 源代码
│   └── scripts/              # Python脚本
├── adrc_controller/          # ADRC控制器
├── qt_gui_ros2/             # Qt图形界面
├── robotcar_base/           # 硬件基础包
└── ...                      # 其他功能包
```

### 添加新功能

1. **创建新包**
```bash
cd src/
ros2 pkg create --build-type ament_cmake my_new_package
```

2. **添加依赖**
```xml
<!-- package.xml -->
<depend>rclcpp</depend>
<depend>sensor_msgs</depend>
```

3. **编写节点**
```cpp
// src/my_node.cpp
#include <rclcpp/rclcpp.hpp>

class MyNode : public rclcpp::Node {
public:
    MyNode() : Node("my_node") {
        // 节点初始化
    }
};

int main(int argc, char** argv) {
    rclcpp::init(argc, argv);
    rclcpp::spin(std::make_shared<MyNode>());
    rclcpp::shutdown();
    return 0;
}
```

4. **配置CMakeLists.txt**
```cmake
find_package(rclcpp REQUIRED)
find_package(sensor_msgs REQUIRED)

add_executable(my_node src/my_node.cpp)
ament_target_dependencies(my_node rclcpp sensor_msgs)

install(TARGETS my_node DESTINATION lib/${PROJECT_NAME})
```

### 调试技巧

```bash
# 使用GDB调试
ros2 run --prefix 'gdb -ex run --args' my_package my_node

# 性能分析
ros2 run --prefix 'valgrind --tool=callgrind' my_package my_node

# 日志级别设置
ros2 run my_package my_node --ros-args --log-level DEBUG

# RViz配置保存
rviz2 -d config/my_config.rviz
```

## 🔧 故障排除

### 常见问题及解决方案

#### 1. 雷达连接问题

**症状**: 无法接收到激光雷达数据

**解决方案**:
```bash
# 检查网络连接
ping *************  # 雷达1 IP
ping *************  # 雷达2 IP

# 检查话题
ros2 topic list | grep scan
ros2 topic hz /lidar1/scan
ros2 topic hz /lidar2/scan

# 重启雷达驱动
ros2 lifecycle set /oradar_node configure
ros2 lifecycle set /oradar_node activate
```

#### 2. TF变换错误

**症状**: `tf2.LookupException` 或坐标变换失败

**解决方案**:
```bash
# 检查TF树
ros2 run tf2_tools view_frames
evince frames.pdf

# 检查特定变换
ros2 run tf2_ros tf2_echo map base_link

# 发布静态变换
ros2 run tf2_ros static_transform_publisher 0 0 0 0 0 0 map odom
```

#### 3. 导航失败

**症状**: 机器人无法到达目标点

**解决方案**:
```bash
# 检查定位状态
ros2 topic echo /tracked_pose

# 检查路径规划
ros2 topic echo /plan

# 重置代价地图
ros2 service call /global_costmap/clear_entirely_global_costmap nav2_msgs/srv/ClearEntireCostmap
ros2 service call /local_costmap/clear_entirely_local_costmap nav2_msgs/srv/ClearEntireCostmap

# 调整导航参数
ros2 param set /controller_server max_vel_x 0.3
```

#### 4. Qt界面无法启动

**症状**: GUI程序崩溃或无法显示

**解决方案**:
```bash
# 检查Qt依赖
sudo apt install qt5-default qtbase5-dev

# 检查显示环境
echo $DISPLAY
xhost +local:

# 重新编译Qt包
colcon build --packages-select qt_gui_ros2 --cmake-clean-cache
```

#### 5. ADRC控制器振荡

**症状**: 机器人运动不稳定，出现振荡

**解决方案**:
```bash
# 降低控制增益
ros2 param set /adrc_controller kp 50.0
ros2 param set /adrc_controller kd 10.0

# 调整ESO参数
ros2 param set /adrc_controller beta01 50.0
ros2 param set /adrc_controller beta02 150.0

# 限制速度
ros2 param set /adrc_controller max_linear_vel 0.3
ros2 param set /adrc_controller max_angular_vel 0.5
```

### 日志分析

```bash
# 查看系统日志
ros2 log list
ros2 log get /my_node

# 设置日志级别
export RCUTILS_LOGGING_SEVERITY=DEBUG
export RCUTILS_LOGGING_BUFFERED_STREAM=1

# 保存日志到文件
ros2 launch my_package my_launch.py 2>&1 | tee system.log
```

## 🤝 贡献指南

### 开发流程

1. **Fork仓库**
```bash
git clone https://github.com/your-username/test_ws.git
cd test_ws
git remote add upstream https://github.com/Sspacegray/test_ws.git
```

2. **创建功能分支**
```bash
git checkout -b feature/new-feature
```

3. **开发和测试**
```bash
# 编写代码
# 运行测试
colcon test --packages-select your_package
```

4. **提交更改**
```bash
git add .
git commit -m "feat: add new feature description"
git push origin feature/new-feature
```

5. **创建Pull Request**

### 代码规范

#### C++代码规范
- 遵循ROS2 C++风格指南
- 使用`clang-format`格式化代码
- 添加适当的注释和文档

#### Python代码规范
- 遵循PEP 8规范
- 使用`black`格式化代码
- 添加类型提示

#### 提交信息规范
```
feat: 新功能
fix: 修复bug
docs: 文档更新
style: 代码格式化
refactor: 代码重构
test: 测试相关
chore: 构建过程或辅助工具的变动
```

### 测试指南

```bash
# 运行单元测试
colcon test --packages-select robotcar_laser_fusion

# 运行集成测试
ros2 launch robotcar_laser_fusion test_integration.launch.py

# 性能测试
ros2 run robotcar_laser_fusion benchmark_node

# 代码覆盖率
colcon test --packages-select robotcar_laser_fusion --event-handlers console_cohesion+
```

## 📄 许可证

本项目采用 [Apache License 2.0](LICENSE) 许可证。

```
Copyright 2024 RobotCar Team

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
```

## 📚 引用

如果您在研究中使用了本项目，请引用以下文献：

### BibTeX

```bibtex
@software{robotcar_navigation_system,
  title={RobotCar: An Intelligent Navigation Robot System with Dual-LiDAR Fusion and ADRC Control},
  author={RobotCar Team},
  year={2024},
  url={https://github.com/Sspacegray/test_ws},
  version={1.0.0}
}

@article{adrc_controller_2024,
  title={Enhanced Active Disturbance Rejection Control for Mobile Robot Navigation},
  author={RobotCar Team},
  journal={Robotics and Autonomous Systems},
  year={2024},
  note={Implementation available at: https://github.com/Sspacegray/test_ws}
}
```

### APA格式

```
RobotCar Team. (2024). RobotCar: An Intelligent Navigation Robot System with Dual-LiDAR Fusion and ADRC Control (Version 1.0.0) [Computer software]. https://github.com/Sspacegray/test_ws
```

## 🔮 未来改进和完善点

### 短期改进计划 (1-3个月)

#### 🎯 性能优化
- [ ] **多线程优化**: 实现传感器数据处理的并行化，提升实时性能
- [ ] **内存管理**: 优化点云数据处理，减少内存占用和GC压力
- [ ] **算法优化**: 改进ADRC控制器的计算效率，降低CPU使用率
- [ ] **网络优化**: 实现DDS QoS配置优化，提升通信可靠性

#### 🛡️ 稳定性增强
- [ ] **异常处理**: 完善传感器断连、网络中断等异常情况的处理机制
- [ ] **故障恢复**: 实现自动故障检测和恢复功能
- [ ] **参数验证**: 添加配置参数的有效性检查和自动修正
- [ ] **日志系统**: 完善结构化日志记录，便于问题诊断

#### 🔧 易用性提升
- [ ] **一键部署**: 开发自动化部署脚本，简化系统安装过程
- [ ] **参数调优工具**: 开发图形化参数调优界面
- [ ] **诊断工具**: 实现系统健康检查和性能监控工具
- [ ] **文档完善**: 添加更多使用示例和故障排除指南

### 中期发展计划 (3-6个月)

#### 🤖 智能化升级
- [ ] **机器学习集成**:
  - 基于强化学习的路径规划优化
  - 环境自适应的控制参数调整
  - 障碍物行为预测模型
- [ ] **语义SLAM**: 集成语义分割，实现场景理解和语义导航
- [ ] **多机器人协作**: 支持多机器人编队和协同导航
- [ ] **云端集成**: 实现云端地图共享和远程监控

#### 🌐 功能扩展
- [ ] **视觉导航**:
  - 集成RGB-D相机进行视觉SLAM
  - 实现基于视觉的精确定位
  - 支持动态环境下的视觉导航
- [ ] **语音交互**: 添加语音命令识别和反馈系统
- [ ] **移动操作**: 集成机械臂实现移动操作任务
- [ ] **自动充电**: 实现自主返回充电桩功能

#### 🔒 安全性强化
- [ ] **安全认证**: 实现ROS2安全通信和节点认证
- [ ] **数据加密**: 添加敏感数据传输加密
- [ ] **访问控制**: 实现基于角色的访问控制系统
- [ ] **安全审计**: 添加操作日志和安全事件记录

### 长期愿景 (6-12个月)

#### 🏭 产业化应用
- [ ] **行业定制**:
  - 仓储物流版本 (AGV功能)
  - 服务机器人版本 (导览、配送)
  - 巡检机器人版本 (安防、设备检查)
  - 清洁机器人版本 (自动清扫)
- [ ] **标准化接口**: 实现与主流机器人平台的标准化接口
- [ ] **云原生架构**: 支持Kubernetes部署和微服务架构
- [ ] **边缘计算**: 优化边缘设备部署，支持离线运行

#### 🧠 AI能力增强
- [ ] **深度学习框架集成**:
  - PyTorch/TensorFlow模型部署
  - 实时推理优化
  - 模型热更新机制
- [ ] **自主学习**: 实现环境自适应学习和经验积累
- [ ] **决策规划**: 基于AI的高级任务规划和决策系统
- [ ] **人机交互**: 自然语言理解和多模态交互

#### 🌍 生态系统建设
- [ ] **插件系统**: 开发可扩展的插件架构
- [ ] **开发者工具**: 提供完整的SDK和开发工具链
- [ ] **社区平台**: 建立开发者社区和知识分享平台
- [ ] **商业化支持**: 提供技术支持和定制开发服务

### 技术债务清理

#### 🔄 代码重构
- [ ] **架构优化**: 重构核心模块，提升代码可维护性
- [ ] **接口标准化**: 统一各模块间的接口规范
- [ ] **测试覆盖**: 提升单元测试和集成测试覆盖率至90%+
- [ ] **文档同步**: 确保代码文档与实现保持同步

#### 📊 性能基准
- [ ] **基准测试**: 建立完整的性能基准测试套件
- [ ] **持续集成**: 实现自动化测试和性能回归检测
- [ ] **质量门禁**: 设置代码质量和性能标准
- [ ] **监控告警**: 实现生产环境性能监控和告警

### 贡献机会

我们欢迎社区贡献者参与以下领域的开发：

#### 🎯 初级贡献者
- 文档翻译和完善
- 示例代码和教程编写
- Bug修复和小功能改进
- 测试用例编写

#### 🚀 中级贡献者
- 新功能模块开发
- 性能优化和算法改进
- 跨平台适配
- 工具和脚本开发

#### 🏆 高级贡献者
- 架构设计和重构
- 核心算法研发
- 系统集成和优化
- 技术方案设计

### 联系方式

- **项目主页**: https://github.com/Sspacegray/test_ws
- **问题反馈**: https://github.com/Sspacegray/test_ws/issues
- **讨论区**: https://github.com/Sspacegray/test_ws/discussions
- **邮箱**: <EMAIL>

---

## 🙏 致谢

感谢以下开源项目和社区的支持：

- [ROS2](https://ros.org/) - 机器人操作系统
- [Google Cartographer](https://github.com/cartographer-project/cartographer) - SLAM算法
- [Navigation2](https://navigation.ros.org/) - 导航框架
- [Qt](https://www.qt.io/) - 图形界面框架
- [PCL](https://pointclouds.org/) - 点云处理库

特别感谢所有贡献者和用户的反馈，让这个项目不断改进和完善。

---

<div align="center">

**⭐ 如果这个项目对您有帮助，请给我们一个Star！⭐**

[![GitHub stars](https://img.shields.io/github/stars/Sspacegray/test_ws.svg?style=social&label=Star)](https://github.com/Sspacegray/test_ws)
[![GitHub forks](https://img.shields.io/github/forks/Sspacegray/test_ws.svg?style=social&label=Fork)](https://github.com/Sspacegray/test_ws/fork)
[![GitHub watchers](https://img.shields.io/github/watchers/Sspacegray/test_ws.svg?style=social&label=Watch)](https://github.com/Sspacegray/test_ws)

</div>