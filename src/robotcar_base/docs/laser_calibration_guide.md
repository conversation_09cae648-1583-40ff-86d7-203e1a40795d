# 激光雷达动态标定指南

## 概述

本系统支持通过rqt动态调参来实时调整激光雷达的位置和姿态，无需重新编译URDF文件。

## 功能特性

- ✅ **实时参数调整**：通过rqt_reconfigure实时修改激光雷达位置和姿态
- ✅ **6DOF完整支持**：支持X/Y/Z位置和Roll/Pitch/Yaw姿态调整
- ✅ **参数范围限制**：防止设置不合理的参数值
- ✅ **角度单位转换**：自动显示弧度和角度值
- ✅ **实时TF更新**：参数变化立即反映到TF树中

## 使用方法

### 1. 启动激光雷达标定系统

```bash
# 启动标定系统
ros2 launch robotcar_base laser_calibration.launch.py

# 或者使用自定义初始参数
ros2 launch robotcar_base laser_calibration.launch.py \
    front_right_laser_x:=0.2 \
    front_right_laser_yaw:=-0.7
```

### 2. 启动rqt动态调参工具

```bash
# 启动rqt_reconfigure
ros2 run rqt_reconfigure rqt_reconfigure

# 或者启动完整的rqt工具
rqt
```

### 3. 在rqt中调整参数

1. 在rqt_reconfigure中找到`laser_calibration_node`
2. 展开激光雷达参数组：
   - `front_right_laser` - 前右激光雷达
   - `rear_left_laser` - 后左激光雷达
3. 调整位置参数（position）：
   - `x`: 前后位置 (-0.5 ~ 0.5m)
   - `y`: 左右位置 (-0.5 ~ 0.5m)  
   - `z`: 上下位置 (0.0 ~ 0.5m)
4. 调整姿态参数（orientation）：
   - `roll`: 绕X轴旋转 (-90° ~ 90°)
   - `pitch`: 绕Y轴旋转 (-90° ~ 90°)
   - `yaw`: 绕Z轴旋转 (-180° ~ 180°)

### 4. 实时查看TF变化

```bash
# 查看TF树
ros2 run tf2_tools view_frames

# 实时监控特定TF变换
ros2 run tf2_ros tf2_echo base_link front_right_laser
ros2 run tf2_ros tf2_echo base_link rear_left_laser
```

### 5. 在RViz中可视化

```bash
# 启动RViz
rviz2

# 添加以下显示项：
# - RobotModel: 显示机器人模型
# - TF: 显示坐标系
# - LaserScan: 显示激光雷达数据（如果有的话）
```

## 参数说明

### 前右激光雷达 (front_right_laser)

| 参数 | 默认值 | 范围 | 说明 |
|------|--------|------|------|
| position.x | 0.1875 | -0.5~0.5 | 前后位置(m) |
| position.y | -0.15 | -0.5~0.5 | 左右位置(m) |
| position.z | 0.15 | 0.0~0.5 | 上下位置(m) |
| orientation.roll | 0.0 | -90°~90° | 绕X轴旋转 |
| orientation.pitch | 0.0 | -90°~90° | 绕Y轴旋转 |
| orientation.yaw | -38.7° | -180°~180° | 绕Z轴旋转 |

### 后左激光雷达 (rear_left_laser)

| 参数 | 默认值 | 范围 | 说明 |
|------|--------|------|------|
| position.x | -0.1875 | -0.5~0.5 | 前后位置(m) |
| position.y | 0.15 | -0.5~0.5 | 左右位置(m) |
| position.z | 0.15 | 0.0~0.5 | 上下位置(m) |
| orientation.roll | 0.0 | -90°~90° | 绕X轴旋转 |
| orientation.pitch | 0.0 | -90°~90° | 绕Y轴旋转 |
| orientation.yaw | 141.3° | -180°~180° | 绕Z轴旋转 |

## 标定流程建议

1. **粗调位置**：先调整X/Y/Z位置，使激光雷达大致在正确位置
2. **精调姿态**：调整Yaw角度，使激光雷达扫描方向正确
3. **微调倾斜**：如需要，调整Roll/Pitch角度
4. **验证效果**：在RViz中查看激光雷达数据是否正确对齐

## 保存标定结果

标定完成后，可以将参数保存到配置文件或启动文件中：

```bash
# 获取当前参数值
ros2 param get /laser_calibration_node front_right_laser.position.x
ros2 param get /laser_calibration_node front_right_laser.orientation.yaw
# ... 其他参数
```

## 故障排除

### 问题1：rqt_reconfigure中看不到参数
- 确保`laser_calibration_node`正在运行
- 检查节点是否正确启动：`ros2 node list`

### 问题2：参数调整后TF没有更新
- 检查robot_state_publisher是否正在运行
- 确认URDF参数是否正确传递

### 问题3：参数范围受限
- 参数范围在代码中定义，可以修改`laser_calibration_node.py`中的范围限制
