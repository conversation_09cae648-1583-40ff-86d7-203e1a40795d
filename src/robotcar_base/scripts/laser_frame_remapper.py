#!/usr/bin/env python3

import rclpy
from rclpy.node import Node
from sensor_msgs.msg import LaserScan
import copy

class LaserFrameRemapper(Node):
    """
    激光雷达坐标系重映射节点
    将激光雷达数据从原始坐标系重映射到动态坐标系
    这样在RViz中就能看到实时的激光雷达位置变化
    """
    
    def __init__(self):
        super().__init__('laser_frame_remapper')
        
        # 创建订阅者 - 订阅原始激光雷达数据
        self.front_right_sub = self.create_subscription(
            LaserScan,
            '/MS500_front_right/scan',
            self.front_right_callback,
            10
        )
        
        self.rear_left_sub = self.create_subscription(
            LaserScan,
            '/MS500_rear_left/scan', 
            self.rear_left_callback,
            10
        )
        
        # 创建发布者 - 发布重映射后的激光雷达数据
        self.front_right_pub = self.create_publisher(
            LaserScan,
            '/front_right_laser_dynamic/scan',
            10
        )
        
        self.rear_left_pub = self.create_publisher(
            LaserScan,
            '/rear_left_laser_dynamic/scan',
            10
        )
        
        self.get_logger().info('Laser Frame Remapper started.')
        self.get_logger().info('Remapping laser scans to dynamic frames:')
        self.get_logger().info('  /MS500_front_right/scan -> /front_right_laser_dynamic/scan')
        self.get_logger().info('  /MS500_rear_left/scan -> /rear_left_laser_dynamic/scan')
    
    def front_right_callback(self, msg):
        """前右激光雷达数据回调"""
        # 复制消息并更改坐标系
        remapped_msg = copy.deepcopy(msg)
        remapped_msg.header.frame_id = 'front_right_laser_dynamic'
        
        # 发布重映射后的数据
        self.front_right_pub.publish(remapped_msg)
    
    def rear_left_callback(self, msg):
        """后左激光雷达数据回调"""
        # 复制消息并更改坐标系
        remapped_msg = copy.deepcopy(msg)
        remapped_msg.header.frame_id = 'rear_left_laser_dynamic'
        
        # 发布重映射后的数据
        self.rear_left_pub.publish(remapped_msg)

def main(args=None):
    rclpy.init(args=args)
    node = LaserFrameRemapper()
    
    try:
        rclpy.spin(node)
    except KeyboardInterrupt:
        pass
    finally:
        node.destroy_node()
        rclpy.shutdown()

if __name__ == '__main__':
    main()
