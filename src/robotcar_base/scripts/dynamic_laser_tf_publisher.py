#!/usr/bin/env python3

import rclpy
from rclpy.node import Node
from tf2_ros import StaticTransformBroadcaster
from geometry_msgs.msg import TransformStamped
from rcl_interfaces.msg import ParameterDescriptor, ParameterType, SetParametersResult
import tf_transformations
import math

class DynamicLaserTFPublisher(Node):
    """
    动态激光雷达TF发布器
    监听参数变化，实时更新激光雷达的TF变换
    这样可以在RViz中看到激光雷达位置的实时变化
    """
    
    def __init__(self):
        super().__init__('dynamic_laser_tf_publisher')
        
        # 创建静态TF发布器
        self.tf_broadcaster = StaticTransformBroadcaster(self)
        
        # 声明激光雷达参数（与laser_calibration_node相同）
        self.declare_laser_parameters()
        
        # 添加参数变化回调
        self.add_on_set_parameters_callback(self.parameter_callback)
        
        # 初始发布TF
        self.publish_laser_transforms()
        
        self.get_logger().info('Dynamic Laser TF Publisher started.')
        self.get_logger().info('Laser positions will update in real-time in RViz!')
    
    def declare_laser_parameters(self):
        """声明激光雷达参数"""
        # 前右激光雷达参数
        self.declare_parameter('front_right_laser.position.x', 0.1875)
        self.declare_parameter('front_right_laser.position.y', -0.15)
        self.declare_parameter('front_right_laser.position.z', 0.15)
        self.declare_parameter('front_right_laser.orientation.roll', 0.0)
        self.declare_parameter('front_right_laser.orientation.pitch', 0.0)
        self.declare_parameter('front_right_laser.orientation.yaw', -0.6747)
        
        # 后左激光雷达参数
        self.declare_parameter('rear_left_laser.position.x', -0.1875)
        self.declare_parameter('rear_left_laser.position.y', 0.15)
        self.declare_parameter('rear_left_laser.position.z', 0.15)
        self.declare_parameter('rear_left_laser.orientation.roll', 0.0)
        self.declare_parameter('rear_left_laser.orientation.pitch', 0.0)
        self.declare_parameter('rear_left_laser.orientation.yaw', 2.4669)
    
    def parameter_callback(self, params):
        """参数变化回调函数"""
        laser_param_changed = False
        
        for param in params:
            if 'laser' in param.name:
                laser_param_changed = True
                self.get_logger().info(f'TF Parameter updated: {param.name} = {param.value}')
                
                # 如果是角度参数，显示度数
                if 'orientation' in param.name:
                    degrees = math.degrees(param.value)
                    self.get_logger().info(f'  -> {degrees:.2f} degrees')
        
        # 如果激光雷达参数发生变化，重新发布TF
        if laser_param_changed:
            self.publish_laser_transforms()
            self.get_logger().info('✅ Laser TF transforms updated in RViz!')
        
        return SetParametersResult(successful=True)
    
    def create_transform(self, parent_frame, child_frame, x, y, z, roll, pitch, yaw):
        """创建TF变换"""
        transform = TransformStamped()
        
        # 设置时间戳和坐标系
        transform.header.stamp = self.get_clock().now().to_msg()
        transform.header.frame_id = parent_frame
        transform.child_frame_id = child_frame
        
        # 设置位置
        transform.transform.translation.x = x
        transform.transform.translation.y = y
        transform.transform.translation.z = z
        
        # 设置旋转（从RPY转换为四元数）
        quaternion = tf_transformations.quaternion_from_euler(roll, pitch, yaw)
        transform.transform.rotation.x = quaternion[0]
        transform.transform.rotation.y = quaternion[1]
        transform.transform.rotation.z = quaternion[2]
        transform.transform.rotation.w = quaternion[3]
        
        return transform
    
    def publish_laser_transforms(self):
        """发布激光雷达TF变换"""
        transforms = []
        
        # 前右激光雷达
        fr_x = self.get_parameter('front_right_laser.position.x').value
        fr_y = self.get_parameter('front_right_laser.position.y').value
        fr_z = self.get_parameter('front_right_laser.position.z').value
        fr_roll = self.get_parameter('front_right_laser.orientation.roll').value
        fr_pitch = self.get_parameter('front_right_laser.orientation.pitch').value
        fr_yaw = self.get_parameter('front_right_laser.orientation.yaw').value
        
        front_right_transform = self.create_transform(
            'base_link', 'front_right_laser_dynamic',
            fr_x, fr_y, fr_z, fr_roll, fr_pitch, fr_yaw
        )
        transforms.append(front_right_transform)
        
        # 后左激光雷达
        rl_x = self.get_parameter('rear_left_laser.position.x').value
        rl_y = self.get_parameter('rear_left_laser.position.y').value
        rl_z = self.get_parameter('rear_left_laser.position.z').value
        rl_roll = self.get_parameter('rear_left_laser.orientation.roll').value
        rl_pitch = self.get_parameter('rear_left_laser.orientation.pitch').value
        rl_yaw = self.get_parameter('rear_left_laser.orientation.yaw').value
        
        rear_left_transform = self.create_transform(
            'base_link', 'rear_left_laser_dynamic',
            rl_x, rl_y, rl_z, rl_roll, rl_pitch, rl_yaw
        )
        transforms.append(rear_left_transform)
        
        # 发布所有变换
        self.tf_broadcaster.sendTransform(transforms)
        
        # 记录当前位置
        self.get_logger().info('📍 Current Laser Positions:')
        self.get_logger().info(f'  Front Right: pos=({fr_x:.3f}, {fr_y:.3f}, {fr_z:.3f}) '
                              f'rot=({math.degrees(fr_roll):.1f}°, {math.degrees(fr_pitch):.1f}°, {math.degrees(fr_yaw):.1f}°)')
        self.get_logger().info(f'  Rear Left:   pos=({rl_x:.3f}, {rl_y:.3f}, {rl_z:.3f}) '
                              f'rot=({math.degrees(rl_roll):.1f}°, {math.degrees(rl_pitch):.1f}°, {math.degrees(rl_yaw):.1f}°)')

def main(args=None):
    rclpy.init(args=args)
    node = DynamicLaserTFPublisher()
    
    try:
        rclpy.spin(node)
    except KeyboardInterrupt:
        pass
    finally:
        node.destroy_node()
        rclpy.shutdown()

if __name__ == '__main__':
    main()
