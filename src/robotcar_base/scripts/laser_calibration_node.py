#!/usr/bin/env python3

import rclpy
from rclpy.node import Node
from rcl_interfaces.msg import ParameterDescriptor, ParameterType, SetParametersResult
from rcl_interfaces.srv import SetParameters
from rclpy.parameter import Parameter
import math

class LaserCalibrationNode(Node):
    """
    激光雷达标定节点
    提供动态参数调整功能，支持rqt_reconfigure
    """
    
    def __init__(self):
        super().__init__('laser_calibration_node')
        
        # 声明前右激光雷达参数
        self.declare_parameter_with_range('front_right_laser.position.x', 0.1875, -0.5, 0.5, 'Front right laser X position (m)')
        self.declare_parameter_with_range('front_right_laser.position.y', -0.15, -0.5, 0.5, 'Front right laser Y position (m)')
        self.declare_parameter_with_range('front_right_laser.position.z', 0.15, 0.0, 0.5, 'Front right laser Z position (m)')
        self.declare_parameter_with_range('front_right_laser.orientation.roll', 0.0, -1.57, 1.57, 'Front right laser roll (rad)')
        self.declare_parameter_with_range('front_right_laser.orientation.pitch', 0.0, -1.57, 1.57, 'Front right laser pitch (rad)')
        self.declare_parameter_with_range('front_right_laser.orientation.yaw', -0.6747, -3.14159, 3.14159, 'Front right laser yaw (rad)')
        
        # 声明后左激光雷达参数
        self.declare_parameter_with_range('rear_left_laser.position.x', -0.1875, -0.5, 0.5, 'Rear left laser X position (m)')
        self.declare_parameter_with_range('rear_left_laser.position.y', 0.15, -0.5, 0.5, 'Rear left laser Y position (m)')
        self.declare_parameter_with_range('rear_left_laser.position.z', 0.15, 0.0, 0.5, 'Rear left laser Z position (m)')
        self.declare_parameter_with_range('rear_left_laser.orientation.roll', 0.0, -1.57, 1.57, 'Rear left laser roll (rad)')
        self.declare_parameter_with_range('rear_left_laser.orientation.pitch', 0.0, -1.57, 1.57, 'Rear left laser pitch (rad)')
        self.declare_parameter_with_range('rear_left_laser.orientation.yaw', 2.4669, -3.14159, 3.14159, 'Rear left laser yaw (rad)')
        
        # 添加参数变化回调
        self.add_on_set_parameters_callback(self.parameter_callback)
        
        # 定时器：定期打印当前参数值
        self.timer = self.create_timer(5.0, self.print_current_parameters)
        
        self.get_logger().info('Laser calibration node started. Use rqt_reconfigure to adjust parameters.')
        self.get_logger().info('Parameters can be adjusted in real-time for laser calibration.')
    
    def declare_parameter_with_range(self, name, default_value, min_val, max_val, description):
        """声明带范围限制的参数"""
        descriptor = ParameterDescriptor(
            type=ParameterType.PARAMETER_DOUBLE,
            description=description,
            additional_constraints=f'Range: [{min_val}, {max_val}]'
        )
        self.declare_parameter(name, default_value, descriptor)
    
    def parameter_callback(self, params):
        """参数变化回调函数"""
        for param in params:
            if 'laser' in param.name:
                self.get_logger().info(f'Parameter updated: {param.name} = {param.value}')
                
                # 如果是角度参数，同时显示度数
                if 'orientation' in param.name:
                    degrees = math.degrees(param.value)
                    self.get_logger().info(f'  -> {degrees:.2f} degrees')
        
        return SetParametersResult(successful=True)
    
    def print_current_parameters(self):
        """打印当前参数值"""
        self.get_logger().info('=== Current Laser Calibration Parameters ===')
        
        # 前右激光雷达
        fr_x = self.get_parameter('front_right_laser.position.x').value
        fr_y = self.get_parameter('front_right_laser.position.y').value
        fr_z = self.get_parameter('front_right_laser.position.z').value
        fr_roll = self.get_parameter('front_right_laser.orientation.roll').value
        fr_pitch = self.get_parameter('front_right_laser.orientation.pitch').value
        fr_yaw = self.get_parameter('front_right_laser.orientation.yaw').value
        
        self.get_logger().info(f'Front Right: pos=({fr_x:.3f}, {fr_y:.3f}, {fr_z:.3f}) '
                              f'rot=({math.degrees(fr_roll):.1f}°, {math.degrees(fr_pitch):.1f}°, {math.degrees(fr_yaw):.1f}°)')
        
        # 后左激光雷达
        rl_x = self.get_parameter('rear_left_laser.position.x').value
        rl_y = self.get_parameter('rear_left_laser.position.y').value
        rl_z = self.get_parameter('rear_left_laser.position.z').value
        rl_roll = self.get_parameter('rear_left_laser.orientation.roll').value
        rl_pitch = self.get_parameter('rear_left_laser.orientation.pitch').value
        rl_yaw = self.get_parameter('rear_left_laser.orientation.yaw').value
        
        self.get_logger().info(f'Rear Left:   pos=({rl_x:.3f}, {rl_y:.3f}, {rl_z:.3f}) '
                              f'rot=({math.degrees(rl_roll):.1f}°, {math.degrees(rl_pitch):.1f}°, {math.degrees(rl_yaw):.1f}°)')

def main(args=None):
    rclpy.init(args=args)
    node = LaserCalibrationNode()
    
    try:
        rclpy.spin(node)
    except KeyboardInterrupt:
        pass
    finally:
        node.destroy_node()
        rclpy.shutdown()

if __name__ == '__main__':
    main()
