#!/usr/bin/env python3

import rclpy
from rclpy.node import Node
from rclpy.qos import QoSProfile, ReliabilityPolicy, HistoryPolicy, DurabilityPolicy
from rclpy.duration import Duration
from rclpy.time import Time

from geometry_msgs.msg import Twist, PoseStamped
from nav_msgs.msg import OccupancyGrid
from std_msgs.msg import Header
import tf2_ros
from tf2_ros.buffer import Buffer
from tf2_ros.transform_listener import TransformListener
from tf2_ros import LookupException, ConnectivityException, ExtrapolationException

import socket
import threading
import time
import sys

# --- Custom Message Definition Placeholder ---
# In a real ROS 2 project, you would define your custom message 'CarInfo' in a .msg file
# (e.g., 'RobotCar/msg/CarInfo.msg') and then generate the Python interface.
# For this example, we'll create a simple placeholder class matching its usage.
# If your 'CarInfo' message has more fields, you'll need to update this class.
class CarInfo:
    """
    Placeholder for the custom RobotCar/msg/carinfo ROS 2 message.
    In a real ROS 2 setup, this would be generated from a .msg file.
    Assumes it has a 'power' attribute.
    """
    def __init__(self, power=0):
        self.power = power

    # You might want to add a method to simulate deserialization from a ROS 2 message
    # if you were getting it from an actual topic, e.g.:
    @classmethod
    def from_ros_msg(cls, ros_msg):
        # Assuming ros_msg is the actual generated CarInfo message object
        return cls(power=ros_msg.power)

# --- RobotControlNode Class ---
class RobotControlNode(Node):
    def __init__(self):
        super().__init__('robot_control_node')
        self.get_logger().info("ROS2 Robot Control Node starting...")

        # --- Global Variables (now instance attributes) ---
        self.map_width = None
        self.map_height = None
        self.map_data = None
        self.x_value = None
        self.y_value = None
        self.theta_value = None
        self.map_origin_x = None
        self.map_origin_y = None
        self.current_power = None  # Renamed from 'power' to avoid conflict if 'power' is a local var
        self.mypose = None  # For PoseStamped messages

        # --- ROS 2 Publishers ---
        # QoS profile for /cmd_vel: usually reliable, but may vary
        cmd_vel_qos = QoSProfile(
            reliability=ReliabilityPolicy.BEST_EFFORT,
            history=HistoryPolicy.KEEP_LAST,
            depth=10
        )
        self.pub_cmd_vel = self.create_publisher(Twist, '/cmd_vel', cmd_vel_qos)

        # QoS profile for move_base_simple/goal: usually reliable, latched for goals
        goal_qos = QoSProfile(
            reliability=ReliabilityPolicy.RELIABLE,
            history=HistoryPolicy.KEEP_LAST,
            depth=1,  # Keep last goal for new subscribers
            durability=DurabilityPolicy.TRANSIENT_LOCAL  # Latch last goal
        )
        self.pub_move_goal = self.create_publisher(PoseStamped, 'move_base_simple/goal', goal_qos)

        # --- ROS 2 Subscribers ---
        # QoS profile for /map: usually reliable, latched
        map_qos = QoSProfile(
            reliability=ReliabilityPolicy.RELIABLE,
            history=HistoryPolicy.KEEP_LAST,
            depth=1,  # Keep last map for new subscribers
            durability=DurabilityPolicy.TRANSIENT_LOCAL  # Latch last map
        )
        self.create_subscription(OccupancyGrid, '/map', self.map_callback, map_qos)

        # QoS profile for CarInfo: Best effort for sensor data
        carinfo_qos = QoSProfile(
            reliability=ReliabilityPolicy.BEST_EFFORT,
            history=HistoryPolicy.KEEP_LAST,
            depth=10
        )
        # Note: 'CarInfo' would need to be imported from 'robot_car_msgs.msg' or similar
        # if it were a real generated message. Using placeholder for now.
        self.create_subscription(CarInfo, '/CarInfo', self.carinfo_callback, carinfo_qos)

        # --- TF2 Listener ---
        self.tf_buffer = Buffer()
        self.tf_listener = TransformListener(self.tf_buffer, self)
        # Create a timer to periodically look up the transform
        self.tf_lookup_timer = self.create_timer(0.1, self.pose_lookup_timer_callback)  # 10 Hz

        # --- TCP Server Thread ---
        self.tcp_server_thread = threading.Thread(target=self.start_tcp_server)
        self.tcp_server_thread.daemon = True  # Allow main program to exit even if thread is running
        self.tcp_server_thread.start()
        self.get_logger().info("TCP server thread started.")

    # --- ROS 2 Callback Functions ---
    def map_callback(self, data):
        self.map_width = data.info.width
        self.map_height = data.info.height
        self.map_data = list(data.data)
        self.map_origin_x = data.info.origin.position.x
        self.map_origin_y = data.info.origin.position.y
        self.get_logger().debug("Map data received.")

    def carinfo_callback(self, data):
        # Assuming data is an instance of our CarInfo placeholder or actual ROS2 msg
        self.current_power = data.power
        self.get_logger().debug(f"CarInfo received: Power={self.current_power}")

    def pose_lookup_timer_callback(self):
        # Lookup transform from "base_footprint" to "map"
        try:
            transform = self.tf_buffer.lookup_transform(
                "map", "base_footprint", Time(), Duration(seconds=5.0)
            )
            self.x_value = transform.transform.translation.x
            self.y_value = transform.transform.translation.y
            self.theta_value = transform.transform.rotation.z  # Z component for 2D orientation
            self.get_logger().debug(f"Robot pose: ({self.x_value:.2f}, {self.y_value:.2f}, {self.theta_value:.2f})")
        except (LookupException, ConnectivityException, ExtrapolationException) as ex:
            self.get_logger().warn(f"Could not transform 'base_footprint' to 'map': {ex}")
        except Exception as e:
            self.get_logger().error(f"An unexpected error occurred during TF lookup: {e}")

    # --- Robot Control Functions ---
    def cmd_vel_control(self, command):
        cmd_vel_msg = Twist()

        if command == 'go_up':
            cmd_vel_msg.linear.x = 0.2
        elif command == 'go_back':
            cmd_vel_msg.linear.x = -0.2
        elif command == 'turn_right':
            cmd_vel_msg.angular.z = -0.2
        elif command == 'turn_left':
            cmd_vel_msg.angular.z = 0.2
        elif command == 'pause_work' or command == 'stop_robot':
            cmd_vel_msg.linear.x = 0.0
            cmd_vel_msg.angular.z = 0.0
        else:
            self.get_logger().warn(f"Unknown cmd_vel command: {command}")

        self.pub_cmd_vel.publish(cmd_vel_msg)
        self.get_logger().info(f"Published Twist: linear.x={cmd_vel_msg.linear.x}, angular.z={cmd_vel_msg.angular.z}")

    # --- TCP Communication Functions ---
    def tcp_command(self, command):
        if command == 'connect_robot':
            return 'robot_connected'
        elif command == 'stop_robot':
            self.cmd_vel_control(command)
            time.sleep(1)  # Python's time.sleep, not ROS time
            return 'robot_stopped'
        elif command == 'start_work':
            self.get_logger().info('****** Starting work process ******')
            return 'work_starting'
        elif command == 'pause_work':
            self.cmd_vel_control(command)
            return 'work_paused'
        elif command == 'go_up':
            self.cmd_vel_control(command)
            return 'going_up'
        elif command == 'go_back':
            self.cmd_vel_control(command)
            return 'going_back'
        elif command == 'turn_right':
            self.cmd_vel_control(command)
            return 'turning_right'
        elif command == 'turn_left':
            self.cmd_vel_control(command)
            return 'turning_left'
        elif command == 'rebuild_map':
            if self.map_data is not None:
                self.get_logger().info(f"Map data size: {len(self.map_data)}")
                modified_data = [2 if x == -1 else x for x in self.map_data]
                return f'map,{self.map_width},{self.map_height},{"".join(map(str, modified_data))}!'
            else:
                return 'no_map_data'
        elif command == 'world_pose':
            if self.map_origin_x is not None and self.map_origin_y is not None:
                world_pose = f'world_pose,{self.map_origin_x},{self.map_origin_y}'
                self.get_logger().info(f'Response world pose: {world_pose}')
                return world_pose
            else:
                return 'no