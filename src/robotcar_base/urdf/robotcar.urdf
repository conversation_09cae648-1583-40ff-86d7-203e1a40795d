<?xml version="1.0" ?>
<!-- =================================================================================== -->
<!-- |    This document was autogenerated by xacro from robotcar.xacro                 | -->
<!-- |    EDITING THIS FILE BY HAND IS NOT RECOMMENDED                                 | -->
<!-- =================================================================================== -->
<robot name="robotcar">
  <!-- ======================== 常用颜色 ======================== -->
  <material name="orange">
    <color rgba="1.0 0.5 0.2 1.0"/>
  </material>
  <material name="gray">
    <color rgba="0.5 0.5 0.5 1.0"/>
  </material>
  <material name="dark_gray">
    <color rgba="0.3 0.3 0.3 1.0"/>
  </material>
  <material name="blue">
    <color rgba="0.2 0.4 0.8 1.0"/>
  </material>
  <material name="black">
    <color rgba="0.1 0.1 0.1 1.0"/>
  </material>
  <material name="red">
    <color rgba="0.8 0.2 0.2 1.0"/>
  </material>
  <material name="green">
    <color rgba="0.2 0.8 0.2 1.0"/>
  </material>
  <material name="white">
    <color rgba="0.9 0.9 0.9 1.0"/>
  </material>
  <material name="silver">
    <color rgba="0.7 0.7 0.7 1.0"/>
  </material>
  <!-- 轮子相对base_link的Z偏移 -->
  <!-- ======================== 车体结构 ======================== -->
  <link name="base_footprint"/>
  <joint name="base_joint" type="fixed">
    <parent link="base_footprint"/>
    <child link="base_link"/>
    <origin rpy="0 0 0" xyz="0 0 0.047"/>
  </joint>
  <link name="base_link">
    <visual>
      <geometry>
        <box size="0.75 0.6 0.753"/>
      </geometry>
      <material name="blue"/>
    </visual>
    <collision>
      <geometry>
        <box size="0.75 0.6 0.753"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="50"/>
      <inertia ixx="3.8625374999999993" ixy="0" ixz="0" iyy="4.706287499999999" iyz="0" izz="3.8437499999999996"/>
    </inertial>
  </link>
  <link name="camera_link">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <box size="0.05 0.04 0.03"/>
      </geometry>
      <material name="red"/>
      <!-- 改为红色，更容易看到 -->
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <box size="0.05 0.04 0.03"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <mass value="0.05"/>
      <inertia ixx="1.0416666666666666e-05" ixy="0" ixz="0" iyy="1.4166666666666668e-05" iyz="0" izz="1.7083333333333336e-05"/>
    </inertial>
  </link>
  <joint name="camera_joint" type="fixed">
    <parent link="base_link"/>
    <child link="camera_link"/>
    <origin rpy="0 0 0" xyz="0.355 0 0.27649999999999997"/>
  </joint>
  <!-- Camera optical frame (follows ROS convention: x-forward, y-left, z-up) -->
  <link name="camera_optical_frame"/>
  <joint name="camera_optical_joint" type="fixed">
    <parent link="camera_link"/>
    <child link="camera_optical_frame"/>
    <origin rpy="-1.570796326795 0 -1.570796326795" xyz="0 0 0"/>
  </joint>
  <!-- ======================== IMU传感器 ======================== -->
  <!-- IMU安装在机器人中心位置，与base_link重合 -->
  <link name="imu_link">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <box size="0.02 0.02 0.01"/>
        <!-- 小盒子表示IMU -->
      </geometry>
      <material name="green"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <box size="0.02 0.02 0.01"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="0.01"/>
      <inertia ixx="1e-6" ixy="0" ixz="0" iyy="1e-6" iyz="0" izz="1e-6"/>
    </inertial>
  </link>
  <joint name="imu_joint" type="fixed">
    <parent link="base_link"/>
    <child link="imu_link"/>
    <origin rpy="0 0 0" xyz="0 0 0"/>
    <!-- {{ AURA-X: Fix - IMU安装在base_link中心位置，作为tracking frame. }} -->
  </joint>
  <link name="front_right_laser">
    <visual>
      <geometry>
        <cylinder length="0.08" radius="0.06"/>
      </geometry>
      <material name="red"/>
    </visual>
    <collision>
      <geometry>
        <cylinder length="0.08" radius="0.06"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <mass value="0.3"/>
      <inertia ixx="0.00043" ixy="0" ixz="0" iyy="0.00043" iyz="0" izz="0.0005399999999999999"/>
    </inertial>
  </link>
  <joint name="front_right_laser_joint" type="fixed">
    <parent link="base_link"/>
    <child link="front_right_laser"/>
    <origin rpy="0.0 0.0 -0.6747" xyz="0.3499 -0.219 0.15"/>
  </joint>
  <!-- 雷达空圈 -->
  <link name="front_right_lidar_hollow_ring">
    <visual>
      <geometry>
        <cylinder length="0.054" radius="0.135"/>
      </geometry>
      <material name="dark_gray">
        <color rgba="0.3 0.3 0.3 0.8"/>
      </material>
    </visual>
    <inertial>
      <mass value="0.1"/>
      <inertia ixx="1e-6" ixy="0" ixz="0" iyy="1e-6" iyz="0" izz="1e-6"/>
    </inertial>
  </link>
  <joint name="front_right_lidar_hollow_joint" type="fixed">
    <parent link="base_link"/>
    <child link="front_right_lidar_hollow_ring"/>
    <origin rpy="0 0 0" xyz="0.3499 -0.219                    0.217"/>
  </joint>
  <link name="rear_left_laser">
    <visual>
      <geometry>
        <cylinder length="0.08" radius="0.06"/>
      </geometry>
      <material name="red"/>
    </visual>
    <collision>
      <geometry>
        <cylinder length="0.08" radius="0.06"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <mass value="0.3"/>
      <inertia ixx="0.00043" ixy="0" ixz="0" iyy="0.00043" iyz="0" izz="0.0005399999999999999"/>
    </inertial>
  </link>
  <joint name="rear_left_laser_joint" type="fixed">
    <parent link="base_link"/>
    <child link="rear_left_laser"/>
    <origin rpy="0.0 0.0 2.4669" xyz="-0.3499 0.219 0.15"/>
  </joint>
  <!-- 雷达空圈 -->
  <link name="rear_left_lidar_hollow_ring">
    <visual>
      <geometry>
        <cylinder length="0.054" radius="0.135"/>
      </geometry>
      <material name="dark_gray">
        <color rgba="0.3 0.3 0.3 0.8"/>
      </material>
    </visual>
    <inertial>
      <mass value="0.1"/>
      <inertia ixx="1e-6" ixy="0" ixz="0" iyy="1e-6" iyz="0" izz="1e-6"/>
    </inertial>
  </link>
  <joint name="rear_left_lidar_hollow_joint" type="fixed">
    <parent link="base_link"/>
    <child link="rear_left_lidar_hollow_ring"/>
    <origin rpy="0 0 0" xyz="-0.3499 0.219                    0.217"/>
  </joint>
  <link name="fl_caster_caster_link">
    <visual>
      <origin rpy="1.570796326795 0 0" xyz="0 0 0"/>
      <geometry>
        <cylinder length="0.015" radius="0.02"/>
      </geometry>
      <material name="gray"/>
    </visual>
    <collision>
      <origin rpy="1.570796326795 0 0" xyz="0 0 0"/>
      <geometry>
        <cylinder length="0.015" radius="0.02"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <mass value="0.1"/>
      <inertia ixx="1.1874999999999999e-05" ixy="0" ixz="0" iyy="1.1874999999999999e-05" iyz="0" izz="2e-05"/>
    </inertial>
  </link>
  <joint name="fl_caster_caster_joint" type="fixed">
    <parent link="base_link"/>
    <child link="fl_caster_caster_link"/>
    <origin rpy="0 0 0" xyz="0.28500000000000003                   0.21                   0.0"/>
  </joint>
  <link name="fr_caster_caster_link">
    <visual>
      <origin rpy="1.570796326795 0 0" xyz="0 0 0"/>
      <geometry>
        <cylinder length="0.015" radius="0.02"/>
      </geometry>
      <material name="gray"/>
    </visual>
    <collision>
      <origin rpy="1.570796326795 0 0" xyz="0 0 0"/>
      <geometry>
        <cylinder length="0.015" radius="0.02"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <mass value="0.1"/>
      <inertia ixx="1.1874999999999999e-05" ixy="0" ixz="0" iyy="1.1874999999999999e-05" iyz="0" izz="2e-05"/>
    </inertial>
  </link>
  <joint name="fr_caster_caster_joint" type="fixed">
    <parent link="base_link"/>
    <child link="fr_caster_caster_link"/>
    <origin rpy="0 0 0" xyz="0.28500000000000003                   -0.21                   0.0"/>
  </joint>
  <link name="rl_caster_caster_link">
    <visual>
      <origin rpy="1.570796326795 0 0" xyz="0 0 0"/>
      <geometry>
        <cylinder length="0.015" radius="0.02"/>
      </geometry>
      <material name="gray"/>
    </visual>
    <collision>
      <origin rpy="1.570796326795 0 0" xyz="0 0 0"/>
      <geometry>
        <cylinder length="0.015" radius="0.02"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <mass value="0.1"/>
      <inertia ixx="1.1874999999999999e-05" ixy="0" ixz="0" iyy="1.1874999999999999e-05" iyz="0" izz="2e-05"/>
    </inertial>
  </link>
  <joint name="rl_caster_caster_joint" type="fixed">
    <parent link="base_link"/>
    <child link="rl_caster_caster_link"/>
    <origin rpy="0 0 0" xyz="-0.28500000000000003                   0.21                   0.0"/>
  </joint>
  <link name="rr_caster_caster_link">
    <visual>
      <origin rpy="1.570796326795 0 0" xyz="0 0 0"/>
      <geometry>
        <cylinder length="0.015" radius="0.02"/>
      </geometry>
      <material name="gray"/>
    </visual>
    <collision>
      <origin rpy="1.570796326795 0 0" xyz="0 0 0"/>
      <geometry>
        <cylinder length="0.015" radius="0.02"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <mass value="0.1"/>
      <inertia ixx="1.1874999999999999e-05" ixy="0" ixz="0" iyy="1.1874999999999999e-05" iyz="0" izz="2e-05"/>
    </inertial>
  </link>
  <joint name="rr_caster_caster_joint" type="fixed">
    <parent link="base_link"/>
    <child link="rr_caster_caster_link"/>
    <origin rpy="0 0 0" xyz="-0.28500000000000003                   -0.21                   0.0"/>
  </joint>
  <link name="left_wheel_link">
    <visual>
      <origin rpy="1.570796326795 0 0" xyz="0 0 0"/>
      <geometry>
        <cylinder length="0.03" radius="0.0875"/>
      </geometry>
      <material name="black"/>
    </visual>
    <collision>
      <origin rpy="1.570796326795 0 0" xyz="0 0 0"/>
      <geometry>
        <cylinder length="0.03" radius="0.0875"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <mass value="0.5"/>
      <inertia ixx="0.00099453125" ixy="0" ixz="0" iyy="0.00099453125" iyz="0" izz="0.0019140624999999997"/>
    </inertial>
  </link>
  <joint name="left_wheel_joint" type="continuous">
    <parent link="base_link"/>
    <child link="left_wheel_link"/>
    <origin rpy="0 0 0" xyz="0 0.26499999999999996 0.0"/>
    <axis xyz="0 1 0"/>
    <limit effort="10.0" velocity="10.0"/>
    <dynamics damping="0.1" friction="0.1"/>
  </joint>
  <link name="right_wheel_link">
    <visual>
      <origin rpy="1.570796326795 0 0" xyz="0 0 0"/>
      <geometry>
        <cylinder length="0.03" radius="0.0875"/>
      </geometry>
      <material name="black"/>
    </visual>
    <collision>
      <origin rpy="1.570796326795 0 0" xyz="0 0 0"/>
      <geometry>
        <cylinder length="0.03" radius="0.0875"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <mass value="0.5"/>
      <inertia ixx="0.00099453125" ixy="0" ixz="0" iyy="0.00099453125" iyz="0" izz="0.0019140624999999997"/>
    </inertial>
  </link>
  <joint name="right_wheel_joint" type="continuous">
    <parent link="base_link"/>
    <child link="right_wheel_link"/>
    <origin rpy="0 0 0" xyz="0 -0.26499999999999996 0.0"/>
    <axis xyz="0 1 0"/>
    <limit effort="10.0" velocity="10.0"/>
    <dynamics damping="0.1" friction="0.1"/>
  </joint>
  <!-- ======================== ros2_control 配置 ======================== -->
  <ros2_control name="RobotCar" type="system">
    <hardware>
      <plugin>robotcar_base/RobotCarHardwareInterface</plugin>
      <param name="serial_port_name">/dev/STM32</param>
      <param name="serial_baud_rate">115200</param>
      <param name="wheel_radius">0.0875</param>
      <param name="wheel_separation">0.5299999999999999</param>
      <param name="loop_rate">50</param>
      <param name="device_timeout">1000</param>
      <!-- IMU传感器参数 -->
      <param name="imu_port">/dev/imu_usb</param>
      <param name="imu_baud_rate">115200</param>
      <param name="imu_frame_id">imu_link</param>
    </hardware>
    <joint name="left_wheel_joint">
      <command_interface name="velocity">
        <param name="min">-10.0</param>
        <param name="max">10.0</param>
      </command_interface>
      <state_interface name="position"/>
      <state_interface name="velocity"/>
    </joint>
    <joint name="right_wheel_joint">
      <command_interface name="velocity">
        <param name="min">-10.0</param>
        <param name="max">10.0</param>
      </command_interface>
      <state_interface name="position"/>
      <state_interface name="velocity"/>
    </joint>
    <!-- IMU传感器状态接口 -->
    <sensor name="imu_sensor">
      <state_interface name="orientation.x"/>
      <state_interface name="orientation.y"/>
      <state_interface name="orientation.z"/>
      <state_interface name="orientation.w"/>
      <state_interface name="angular_velocity.x"/>
      <state_interface name="angular_velocity.y"/>
      <state_interface name="angular_velocity.z"/>
      <state_interface name="linear_acceleration.x"/>
      <state_interface name="linear_acceleration.y"/>
      <state_interface name="linear_acceleration.z"/>
      <param name="frame_id">imu_link</param>
    </sensor>
  </ros2_control>
</robot>
