# 支持命名空间的控制器配置
# 使用通配符/**来支持任意命名空间
/**:
  controller_manager:
    ros__parameters:
      update_rate: 20  

      diff_drive_controller:
        type: diff_drive_controller/DiffDriveController

      joint_state_broadcaster:
        type: joint_state_broadcaster/JointStateBroadcaster

      imu_sensor_broadcaster:
        type: imu_sensor_broadcaster/IMUSensorBroadcaster

  diff_drive_controller:
    ros__parameters:
      left_wheel_names: ["left_wheel_joint"]
      right_wheel_names: ["right_wheel_joint"]
      wheel_separation: 0.52  # {{ AURA-X: Fix - 修正轮距参数，与URDF中的实际轮距0.55m保持一致. Approval: 寸止(ID:1754378000). }}====下位机是0.52
      wheel_radius: 0.0875     # 匹配XACRO中的wheel_radius (170mm/2，原来的两倍) =====下位机是175/2
      publish_rate: 20.0      # 降低到10Hz，与controller_manager同步，提高稳定性
      odom_frame_id: odom
      base_frame_id: base_footprint
      use_stamped_vel: false  # 是否带时间戳
      enable_odom_tf: false    # 启用diff_drive_controller的TF发布

      # Odometry settings
      odom_only_twist: false  # 使用完整的odom信息
      position_feedback: true
      velocity_feedback: true

      # 稳定性设置
      open_loop: false        # 使用反馈控制
      
      # Velocity and acceleration limits
      linear:
        x:
          has_velocity_limits: true
          max_velocity: 1.0
          has_acceleration_limits: true
          max_acceleration: 0.8
      angular:
        z:
          has_velocity_limits: true
          max_velocity: 2.0
          has_acceleration_limits: true
          max_acceleration: 1.6

  joint_state_broadcaster:
    ros__parameters:
      {}

  imu_sensor_broadcaster:
    ros__parameters:
      sensor_name: "imu_sensor"
      frame_id: "imu_link"
      # 静态协方差矩阵 (可选)
      static_covariance_orientation: [0.01, 0.0, 0.0, 0.0, 0.01, 0.0, 0.0, 0.0, 0.01]
      static_covariance_angular_velocity: [0.01, 0.0, 0.0, 0.0, 0.01, 0.0, 0.0, 0.0, 0.01]
      static_covariance_linear_acceleration: [0.01, 0.0, 0.0, 0.0, 0.01, 0.0, 0.0, 0.0, 0.01]